/**
 * Query Processor for DFSA Rulebook
 * 
 * Processes and expands user queries for improved retrieval
 */

import { RAGQuery } from './types';

export interface ProcessedQuery {
  /**
   * Original query from the user
   */
  original: string;
  
  /**
   * Processed query for retrieval
   */
  processed: string;
  
  /**
   * Optional filters for retrieval
   */
  filters?: Record<string, any>;
  
  /**
   * Optional conversation ID for maintaining context
   */
  conversationId?: string;
  
  /**
   * Optional expanded queries for hybrid search
   */
  expanded?: string[];
}

/**
 * Query processor for processing and expanding queries
 */
export interface QueryProcessor {
  /**
   * Process a user query
   * @param query User query
   * @returns Processed query
   */
  processQuery(query: string): Promise<ProcessedQuery>;
  
  /**
   * Expand a query with related terms
   * @param query User query
   * @returns Expanded queries
   */
  expandQuery?(query: string): Promise<string[]>;
  
  /**
   * Extract filters from a query
   * @param query User query
   * @returns Extracted filters
   */
  extractFilters?(query: string): Promise<Record<string, any>>;
}

/**
 * DFSA-specific query processor implementation
 */
export class DFSAQueryProcessor implements QueryProcessor {
  /**
   * Process a user query
   * @param query User query
   * @returns Processed query
   */
  public async processQuery(query: string): Promise<ProcessedQuery> {
    // Basic processing - remove extra whitespace and normalize
    const processed = query.trim().replace(/\s+/g, ' ');
    
    // Extract filters
    const filters = await this.extractFilters(query);
    
    // Expand query if needed
    const expanded = await this.expandQuery(query);
    
    return {
      original: query,
      processed,
      filters,
      expanded
    };
  }
  
  /**
   * Expand a query with DFSA-specific related terms
   * @param query User query
   * @returns Expanded queries
   */
  public async expandQuery(query: string): Promise<string[]> {
    // Simple expansion for DFSA-specific terms
    const expanded: string[] = [];
    
    // Add original query
    expanded.push(query);
    
    // Add DFSA-specific expansions
    if (query.toLowerCase().includes('financial')) {
      expanded.push(query.replace(/financial/i, 'monetary'));
      expanded.push(query.replace(/financial/i, 'fiscal'));
    }
    
    if (query.toLowerCase().includes('regulation')) {
      expanded.push(query.replace(/regulation/i, 'rule'));
      expanded.push(query.replace(/regulation/i, 'requirement'));
    }
    
    if (query.toLowerCase().includes('compliance')) {
      expanded.push(query.replace(/compliance/i, 'adherence'));
      expanded.push(query.replace(/compliance/i, 'conformity'));
    }
    
    return expanded;
  }
  
  /**
   * Extract filters from a query for DFSA rulebook
   * @param query User query
   * @returns Extracted filters
   */
  public async extractFilters(query: string): Promise<Record<string, any>> {
    const filters: Record<string, any> = {};
    
    // Extract rulebook section
    const sectionMatch = query.match(/section\s+([0-9.]+)/i);
    if (sectionMatch) {
      filters.section = sectionMatch[1];
    }
    
    // Extract rulebook module
    const moduleMatch = query.match(/module\s+([A-Za-z0-9]+)/i);
    if (moduleMatch) {
      filters.module = moduleMatch[1];
    }
    
    // Extract document type
    if (query.toLowerCase().includes('guidance')) {
      filters.documentType = 'guidance';
    } else if (query.toLowerCase().includes('rule')) {
      filters.documentType = 'rule';
    }
    
    return filters;
  }
}