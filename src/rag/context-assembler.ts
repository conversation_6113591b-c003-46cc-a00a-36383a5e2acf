/**
 * Context Assembler
 * 
 * Assembles context from retrieved chunks for response generation
 */

import { RetrievedChunk } from './types';

/**
 * Context assembler for assembling context from retrieved chunks
 */
export interface ContextAssembler {
  /**
   * Rank chunks by relevance to the query
   * @param chunks Retrieved chunks
   * @param query User query
   * @returns Ranked chunks
   */
  rankChunks(chunks: RetrievedChunk[], query: string): Promise<RetrievedChunk[]>;
  
  /**
   * Filter chunks by relevance threshold
   * @param chunks Retrieved chunks
   * @param minSimilarity Minimum similarity threshold
   * @returns Filtered chunks
   */
  filterRelevant(chunks: RetrievedChunk[], minSimilarity: number): RetrievedChunk[];
  
  /**
   * Assemble context from chunks
   * @param chunks Retrieved chunks
   * @param query User query
   * @returns Assembled context string
   */
  assembleContext(chunks: RetrievedChunk[], query: string): string;
}

/**
 * Basic context assembler implementation
 */
export class BasicContextAssembler implements ContextAssembler {
  /**
   * Rank chunks by relevance to the query
   * @param chunks Retrieved chunks
   * @param query User query
   * @returns Ranked chunks
   */
  public async rankChunks(chunks: RetrievedChunk[], query: string): Promise<RetrievedChunk[]> {
    // Simple ranking - sort by similarity score
    return [...chunks].sort((a, b) => b.similarity - a.similarity);
  }
  
  /**
   * Filter chunks by relevance threshold
   * @param chunks Retrieved chunks
   * @param minSimilarity Minimum similarity threshold
   * @returns Filtered chunks
   */
  public filterRelevant(chunks: RetrievedChunk[], minSimilarity: number): RetrievedChunk[] {
    return chunks.filter(chunk => chunk.similarity >= minSimilarity);
  }
  
  /**
   * Assemble context from chunks
   * @param chunks Retrieved chunks
   * @param query User query
   * @returns Assembled context string
   */
  public assembleContext(chunks: RetrievedChunk[], query: string): string {
    // Simple context assembly with source citations
    return chunks
      .map((chunk, index) => `[${index + 1}] ${chunk.content}\nSource: ${chunk.source}`)
      .join('\n\n');
  }
}