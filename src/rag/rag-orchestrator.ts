/**
 * RAG Orchestrator
 * 
 * Coordinates the complete RAG workflow from query processing to response generation
 */

import { EventEmitter } from 'events';
import { 
  RAGQuery, 
  RAGContext, 
  RAGResponse, 
  RAGConfig, 
  RetrievedChunk,
  ConversationMessage,
  ConversationContext,
  RAGMetrics,
  RAGError
} from './types';
import { VectorSearchQuery } from '../vector-database/vector-store';
import { VectorStoreService } from '../vector-database/vector-store-service';
import { LLMService } from '../llm/llm-service';
import { ResponseGenerator } from '../llm/response-generator';
import { QueryProcessor } from './query-processor';
import { ContextAssembler } from './context-assembler';
import { ConversationManager } from './conversation-manager';

export interface RAGOrchestratorConfig {
  /**
   * Vector store service for retrieving chunks
   */
  vectorStore: VectorStoreService;

  /**
   * LLM service for generating responses
   */
  llmService: LLMService;

  /**
   * Query processor for processing and expanding queries
   */
  queryProcessor?: QueryProcessor;

  /**
   * Context assembler for assembling context from retrieved chunks
   */
  contextAssembler?: ContextAssembler;

  /**
   * Conversation manager for maintaining conversation history
   */
  conversationManager?: ConversationManager;

  /**
   * Default RAG configuration
   */
  defaultConfig?: RAGConfig;

  /**
   * Whether to enable conversation history
   */
  enableConversation?: boolean;

  /**
   * Maximum number of retry attempts
   */
  maxRetries?: number;

  /**
   * Retry delay in milliseconds
   */
  retryDelay?: number;
}

/**
 * RAG Orchestrator for coordinating the complete RAG workflow
 */
export class RAGOrchestrator extends EventEmitter {
  private vectorStore: VectorStoreService;
  private llmService: LLMService;
  private queryProcessor?: QueryProcessor;
  private contextAssembler?: ContextAssembler;
  private conversationManager?: ConversationManager;
  private responseGenerator: ResponseGenerator;
  private config: RAGOrchestratorConfig;
  private metrics: RAGMetrics;
  private isInitialized = false;

  /**
   * Create a new RAG Orchestrator
   * @param config Orchestrator configuration
   */
  constructor(config: RAGOrchestratorConfig) {
    super();
    this.config = {
      enableConversation: true,
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    };

    this.vectorStore = config.vectorStore;
    this.llmService = config.llmService;

    // Handle optional properties explicitly
    if (config.queryProcessor !== undefined) {
      this.queryProcessor = config.queryProcessor;
    }
    if (config.contextAssembler !== undefined) {
      this.contextAssembler = config.contextAssembler;
    }
    if (config.conversationManager !== undefined) {
      this.conversationManager = config.conversationManager;
    }
    
    this.responseGenerator = new ResponseGenerator({
      llmService: this.llmService,
      validateQuality: true,
      minConfidence: 0.7
    });

    this.metrics = {
      totalQueries: 0,
      averageResponseTime: 0,
      averageConfidence: 0,
      providerUsage: {},
      errorRate: 0,
      cacheHitRate: 0
    };
  }

  /**
   * Initialize the RAG orchestrator
   */
  public async initialize(): Promise<void> {
    try {
      // Initialize vector store
      await this.vectorStore.initialize();
      
      // Initialize LLM service
      await this.llmService.initialize();
      
      // Initialize conversation manager if available
      if (this.conversationManager) {
        await this.conversationManager.initialize();
      }
      
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error: any) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Process a query through the RAG pipeline
   * @param query User query
   * @param config Optional RAG configuration
   * @returns RAG response
   */
  public async processQuery(query: string, config?: Partial<RAGConfig>): Promise<RAGResponse> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const startTime = Date.now();
    this.metrics.totalQueries++;

    try {
      // Process query
      const processedQuery = await this.processQueryInternal(query);
      
      // Retrieve relevant chunks
      const context = await this.retrieveRelevantChunks(processedQuery, config);
      
      // Get conversation history if enabled
      let conversationHistory: ConversationMessage[] | undefined;
      if (this.config.enableConversation && this.conversationManager && processedQuery.conversationId) {
        conversationHistory = await this.conversationManager.getConversationHistory(processedQuery.conversationId);
      }
      
      // Generate response
      const response = await this.generateResponse(query, context, conversationHistory, config);
      
      // Update metrics
      this.updateMetrics(response, startTime);
      
      // Store conversation message if enabled
      if (this.config.enableConversation && this.conversationManager && processedQuery.conversationId) {
        await this.conversationManager.addMessage(processedQuery.conversationId, {
          role: 'user',
          content: query,
          timestamp: new Date()
        });
        
        await this.conversationManager.addMessage(processedQuery.conversationId, {
          role: 'assistant',
          content: response.answer,
          timestamp: new Date(),
          sources: response.sources
        });
      }
      
      return response;
    } catch (error: any) {
      // Update error metrics
      this.metrics.errorRate = (this.metrics.errorRate * (this.metrics.totalQueries - 1) + 1) / this.metrics.totalQueries;
      
      // Create and throw RAG error
      const ragError: RAGError = {
        name: 'RAGError',
        message: `Failed to process query: ${error.message || 'Unknown error'}`,
        code: error.code || 'PROCESSING_FAILED',
        provider: error.provider,
        retryable: error.retryable !== undefined ? error.retryable : true,
        context: {
          query,
          error: error.message || 'Unknown error'
        },
        stack: error.stack
      };
      
      this.emit('error', ragError);
      throw ragError;
    }
  }

  /**
   * Create a new conversation
   * @returns Conversation ID
   */
  public async createConversation(): Promise<string> {
    if (!this.conversationManager) {
      throw new Error('Conversation manager is not available');
    }
    
    return this.conversationManager.createConversation();
  }

  /**
   * Get conversation history
   * @param conversationId Conversation ID
   * @returns Conversation context
   */
  public async getConversation(conversationId: string): Promise<ConversationContext> {
    if (!this.conversationManager) {
      throw new Error('Conversation manager is not available');
    }
    
    return this.conversationManager.getConversation(conversationId);
  }

  /**
   * Get orchestrator metrics
   * @returns RAG metrics
   */
  public getMetrics(): RAGMetrics {
    return { ...this.metrics };
  }

  /**
   * Reset orchestrator metrics
   */
  public resetMetrics(): void {
    this.metrics = {
      totalQueries: 0,
      averageResponseTime: 0,
      averageConfidence: 0,
      providerUsage: {},
      errorRate: 0,
      cacheHitRate: 0
    };
  }

  /**
   * Process query internally
   * @param query User query
   * @returns Processed query
   */
  private async processQueryInternal(query: string): Promise<RAGQuery> {
    if (this.queryProcessor) {
      // Use query processor if available
      const processedQuery = await this.queryProcessor.processQuery(query);
      const result: RAGQuery = {
        query: processedQuery.processed
      };
      
      // Add optional properties only if they exist
      if (processedQuery.filters) {
        result.filters = processedQuery.filters;
      }
      
      if (processedQuery.conversationId) {
        result.conversationId = processedQuery.conversationId;
      }
      
      return result;
    } else {
      // Simple query processing
      return {
        query
      };
    }
  }

  /**
   * Retrieve relevant chunks for a query
   * @param query Processed query
   * @param config Optional RAG configuration
   * @returns RAG context
   */
  private async retrieveRelevantChunks(query: RAGQuery, config?: Partial<RAGConfig>): Promise<RAGContext> {
    // Merge configurations
    const mergedConfig = {
      ...this.getDefaultConfig().retrieval,
      ...(config?.retrieval || {})
    };
    
    // Create vector search query
    const searchQuery: VectorSearchQuery = {
      topK: mergedConfig.maxResults,
      includeMetadata: true,
      includeValues: false
    };

    // Add filters only if they exist
    if (query.filters) {
      searchQuery.filter = query.filters;
    }
    
    // Search vector store
    const searchResults = await this.vectorStore.search(searchQuery);
    
    // Map search results to retrieved chunks
    const chunks: RetrievedChunk[] = searchResults.matches.map(match => ({
      id: match.id,
      content: match.metadata?.content || match.metadata?.text || '',
      metadata: match.metadata || {},
      similarity: match.score,
      source: match.metadata?.source || match.metadata?.url || match.id
    }));
    
    // Rank chunks if context assembler is available
    let rankedChunks = chunks;
    if (this.contextAssembler) {
      rankedChunks = await this.contextAssembler.rankChunks(chunks, query.query);
    }
    
    // Filter relevant chunks if context assembler is available
    let relevantChunks = rankedChunks;
    if (this.contextAssembler) {
      relevantChunks = this.contextAssembler.filterRelevant(rankedChunks, mergedConfig.minSimilarity);
    }
    
    // Create RAG context
    const context: RAGContext = {
      chunks: relevantChunks,
      totalChunks: searchResults.matches.length,
      maxSimilarity: Math.max(...relevantChunks.map(chunk => chunk.similarity), 0),
      minSimilarity: Math.min(...relevantChunks.map(chunk => chunk.similarity), 1),
      query: query.query
    };
    
    // Add conversationId only if it exists
    if (query.conversationId) {
      context.conversationId = query.conversationId;
    }
    
    return context;
  }

  /**
   * Generate response for a query
   * @param query User query
   * @param context RAG context
   * @param conversationHistory Optional conversation history
   * @param config Optional RAG configuration
   * @returns RAG response
   */
  private async generateResponse(
    query: string,
    context: RAGContext,
    conversationHistory?: ConversationMessage[],
    config?: Partial<RAGConfig>
  ): Promise<RAGResponse> {
    // Assemble context string
    const contextString = await this.assembleContext(context);
    
    // Generate response
    return this.responseGenerator.generateResponse(
      query,
      contextString,
      conversationHistory,
      config
    );
  }

  /**
   * Assemble context from retrieved chunks
   * @param context RAG context
   * @returns Assembled context string
   */
  private async assembleContext(context: RAGContext): Promise<string> {
    if (this.contextAssembler) {
      // Use context assembler if available
      return this.contextAssembler.assembleContext(context.chunks, context.query);
    } else {
      // Simple context assembly
      return context.chunks
        .map((chunk, index) => `[${index + 1}] ${chunk.content}`)
        .join('\n\n');
    }
  }

  /**
   * Update metrics with response data
   * @param response RAG response
   * @param startTime Request start time
   */
  private updateMetrics(response: RAGResponse, startTime: number): void {
    const responseTime = Date.now() - startTime;
    
    // Update response time
    const totalResponseTime = this.metrics.averageResponseTime * (this.metrics.totalQueries - 1) + responseTime;
    this.metrics.averageResponseTime = totalResponseTime / this.metrics.totalQueries;
    
    // Update confidence
    const totalConfidence = this.metrics.averageConfidence * (this.metrics.totalQueries - 1) + response.confidence;
    this.metrics.averageConfidence = totalConfidence / this.metrics.totalQueries;
    
    // Update provider usage
    this.metrics.providerUsage[response.provider] = (this.metrics.providerUsage[response.provider] || 0) + 1;
  }

  /**
   * Get default RAG configuration
   * @returns Default RAG configuration
   */
  private getDefaultConfig(): RAGConfig {
    return this.config.defaultConfig || {
      retrieval: {
        maxResults: 5,
        minSimilarity: 0.7,
        hybridSearch: true,
        rerankResults: true
      },
      generation: {
        maxTokens: 1000,
        temperature: 0.7,
        systemPrompt: 'You are a helpful assistant that answers questions based on the provided context. ' +
          'If you don\'t know the answer or the information is not in the context, say so. ' +
          'Always cite your sources using [1], [2], etc. corresponding to the context sections.',
        includeContext: true,
        citeSources: true
      },
      conversation: {
        maxHistory: 5,
        contextWindow: 4000
      }
    };
  }
}