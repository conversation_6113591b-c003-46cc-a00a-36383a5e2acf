/**
 * RAG Types
 * 
 * Type definitions for RAG components
 */

/**
 * RAG query with optional filters and conversation ID
 */
export interface RAGQuery {
  /**
   * Query text
   */
  query: string;
  
  /**
   * Optional filters for retrieval
   */
  filters?: Record<string, any>;
  
  /**
   * Optional conversation ID for maintaining context
   */
  conversationId?: string;
}

/**
 * Retrieved chunk with content and metadata
 */
export interface RetrievedChunk {
  /**
   * Chunk ID
   */
  id: string;
  
  /**
   * Chunk content
   */
  content: string;
  
  /**
   * Chunk metadata
   */
  metadata: ChunkMetadata;
  
  /**
   * Similarity score
   */
  similarity: number;
  
  /**
   * Source information
   */
  source: string;
}

/**
 * Chunk metadata
 */
export interface ChunkMetadata {
  /**
   * Document title
   */
  title?: string;
  
  /**
   * Document URL or path
   */
  url?: string;
  
  /**
   * Document source
   */
  source?: string;
  
  /**
   * Document section
   */
  section?: string;
  
  /**
   * Document type
   */
  documentType?: string;
  
  /**
   * Additional metadata
   */
  [key: string]: any;
}

/**
 * RAG context with retrieved chunks
 */
export interface RAGContext {
  /**
   * Retrieved chunks
   */
  chunks: RetrievedChunk[];
  
  /**
   * Total number of chunks retrieved
   */
  totalChunks: number;
  
  /**
   * Maximum similarity score
   */
  maxSimilarity: number;
  
  /**
   * Minimum similarity score
   */
  minSimilarity: number;
  
  /**
   * Query text
   */
  query: string;
  
  /**
   * Optional conversation ID
   */
  conversationId?: string;
}

/**
 * RAG response with answer and sources
 */
export interface RAGResponse {
  /**
   * Generated answer
   */
  answer: string;
  
  /**
   * Source citations
   */
  sources: RAGSource[];
  
  /**
   * Context used for generation
   */
  context?: RAGContext;
  
  /**
   * Confidence score
   */
  confidence: number;
  
  /**
   * Provider used for generation
   */
  provider: string;
  
  /**
   * Model used for generation
   */
  model: string;
  
  /**
   * Response time in milliseconds
   */
  responseTime: number;
}

/**
 * RAG source citation
 */
export interface RAGSource {
  /**
   * Source ID
   */
  id: string;
  
  /**
   * Source title
   */
  title: string;
  
  /**
   * Source excerpt
   */
  excerpt: string;
  
  /**
   * Source relevance score
   */
  relevance: number;
  
  /**
   * Source URL or path
   */
  url: string;
}

/**
 * RAG configuration
 */
export interface RAGConfig {
  /**
   * Retrieval configuration
   */
  retrieval: {
    /**
     * Maximum number of results to retrieve
     */
    maxResults: number;
    
    /**
     * Minimum similarity threshold
     */
    minSimilarity: number;
    
    /**
     * Whether to use hybrid search
     */
    hybridSearch: boolean;
    
    /**
     * Whether to rerank results
     */
    rerankResults: boolean;
  };
  
  /**
   * Generation configuration
   */
  generation: {
    /**
     * Maximum number of tokens to generate
     */
    maxTokens: number;
    
    /**
     * Temperature for generation
     */
    temperature: number;
    
    /**
     * System prompt for generation
     */
    systemPrompt: string;
    
    /**
     * Whether to include context in prompt
     */
    includeContext: boolean;
    
    /**
     * Whether to cite sources
     */
    citeSources: boolean;
  };
  
  /**
   * Conversation configuration
   */
  conversation: {
    /**
     * Maximum number of messages to include in history
     */
    maxHistory: number;
    
    /**
     * Maximum context window size in tokens
     */
    contextWindow: number;
  };
}

/**
 * Conversation message
 */
export interface ConversationMessage {
  /**
   * Message role (user or assistant)
   */
  role: 'user' | 'assistant' | 'system';
  
  /**
   * Message content
   */
  content: string;
  
  /**
   * Message timestamp
   */
  timestamp: Date;
  
  /**
   * Optional source citations for assistant messages
   */
  sources?: RAGSource[];
}

/**
 * Conversation context
 */
export interface ConversationContext {
  /**
   * Conversation ID
   */
  id: string;
  
  /**
   * Conversation messages
   */
  messages: ConversationMessage[];
  
  /**
   * Conversation creation timestamp
   */
  createdAt: Date;
  
  /**
   * Conversation last update timestamp
   */
  updatedAt: Date;
}

/**
 * RAG metrics
 */
export interface RAGMetrics {
  /**
   * Total number of queries processed
   */
  totalQueries: number;
  
  /**
   * Average response time in milliseconds
   */
  averageResponseTime: number;
  
  /**
   * Average confidence score
   */
  averageConfidence: number;
  
  /**
   * Provider usage statistics
   */
  providerUsage: Record<string, number>;
  
  /**
   * Error rate (0-1)
   */
  errorRate: number;
  
  /**
   * Cache hit rate (0-1)
   */
  cacheHitRate: number;
}

/**
 * RAG error
 */
export interface RAGError extends Error {
  /**
   * Error name
   */
  name: string;
  
  /**
   * Error message
   */
  message: string;
  
  /**
   * Error code
   */
  code: string;
  
  /**
   * Provider that caused the error
   */
  provider?: string;
  
  /**
   * Whether the error is retryable
   */
  retryable: boolean;
  
  /**
   * Error context
   */
  context: Record<string, any>;
}

/**
 * LLM Response (alias for RAGResponse for backward compatibility)
 */
export type LLMResponse = RAGResponse;