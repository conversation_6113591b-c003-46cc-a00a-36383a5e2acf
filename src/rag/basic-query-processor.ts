/**
 * Basic Query Processor
 * 
 * Simple implementation of query processor
 */

import { QueryProcessor, ProcessedQuery } from './query-processor';

/**
 * Basic query processor implementation
 */
export class BasicQueryProcessor implements QueryProcessor {
  /**
   * Process a user query
   * @param query User query
   * @returns Processed query
   */
  public async processQuery(query: string): Promise<ProcessedQuery> {
    // Basic processing - remove extra whitespace and normalize
    const processed = query.trim().replace(/\s+/g, ' ');
    
    // Extract filters
    const filters = await this.extractFilters(query);
    
    // Expand query if needed
    const expanded = await this.expandQuery(query);
    
    return {
      original: query,
      processed,
      filters,
      expanded
    };
  }
  
  /**
   * Expand a query with related terms
   * @param query User query
   * @returns Expanded queries
   */
  public async expandQuery(query: string): Promise<string[]> {
    // Simple expansion for general terms
    const expanded: string[] = [];
    
    // Add original query
    expanded.push(query);
    
    // Add simple expansions
    if (query.toLowerCase().includes('how')) {
      expanded.push(query.replace(/how/i, 'what is the way'));
    }
    
    if (query.toLowerCase().includes('what')) {
      expanded.push(query.replace(/what/i, 'which'));
    }
    
    return expanded;
  }
  
  /**
   * Extract filters from a query
   * @param query User query
   * @returns Extracted filters
   */
  public async extractFilters(query: string): Promise<Record<string, any>> {
    const filters: Record<string, any> = {};
    
    // Extract date range
    const dateMatch = query.match(/from\s+(\d{4}-\d{2}-\d{2})\s+to\s+(\d{4}-\d{2}-\d{2})/i);
    if (dateMatch) {
      filters.dateRange = {
        from: dateMatch[1],
        to: dateMatch[2]
      };
    }
    
    // Extract document type
    if (query.toLowerCase().includes('document')) {
      filters.documentType = 'document';
    } else if (query.toLowerCase().includes('article')) {
      filters.documentType = 'article';
    }
    
    return filters;
  }
}