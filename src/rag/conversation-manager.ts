/**
 * Conversation Manager for DFSA Rulebook
 * 
 * Manages conversation history and context for RAG queries
 */

import { ConversationMessage, ConversationContext } from './types';

/**
 * Conversation manager for maintaining conversation history
 */
export interface ConversationManager {
  /**
   * Initialize the conversation manager
   */
  initialize(): Promise<void>;
  
  /**
   * Create a new conversation
   * @returns Conversation ID
   */
  createConversation(): Promise<string>;
  
  /**
   * Get conversation context
   * @param conversationId Conversation ID
   * @returns Conversation context
   */
  getConversation(conversationId: string): Promise<ConversationContext>;
  
  /**
   * Get conversation history
   * @param conversationId Conversation ID
   * @returns Conversation messages
   */
  getConversationHistory(conversationId: string): Promise<ConversationMessage[]>;
  
  /**
   * Add a message to a conversation
   * @param conversationId Conversation ID
   * @param message Conversation message
   */
  addMessage(conversationId: string, message: ConversationMessage): Promise<void>;
  
  /**
   * Delete a conversation
   * @param conversationId Conversation ID
   */
  deleteConversation(conversationId: string): Promise<void>;
}

/**
 * DFSA-specific conversation manager implementation
 */
export class DFSAConversationManager implements ConversationManager {
  private conversations: Map<string, ConversationContext> = new Map();
  
  /**
   * Initialize the conversation manager
   */
  public async initialize(): Promise<void> {
    // No initialization needed for in-memory implementation
  }
  
  /**
   * Create a new conversation
   * @returns Conversation ID
   */
  public async createConversation(): Promise<string> {
    const conversationId = `dfsa_conv_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    this.conversations.set(conversationId, {
      id: conversationId,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    return conversationId;
  }
  
  /**
   * Get conversation context
   * @param conversationId Conversation ID
   * @returns Conversation context
   */
  public async getConversation(conversationId: string): Promise<ConversationContext> {
    const conversation = this.conversations.get(conversationId);
    
    if (!conversation) {
      throw new Error(`Conversation not found: ${conversationId}`);
    }
    
    return { ...conversation };
  }
  
  /**
   * Get conversation history
   * @param conversationId Conversation ID
   * @returns Conversation messages
   */
  public async getConversationHistory(conversationId: string): Promise<ConversationMessage[]> {
    const conversation = this.conversations.get(conversationId);
    
    if (!conversation) {
      throw new Error(`Conversation not found: ${conversationId}`);
    }
    
    return [...conversation.messages];
  }
  
  /**
   * Add a message to a conversation
   * @param conversationId Conversation ID
   * @param message Conversation message
   */
  public async addMessage(conversationId: string, message: ConversationMessage): Promise<void> {
    const conversation = this.conversations.get(conversationId);
    
    if (!conversation) {
      throw new Error(`Conversation not found: ${conversationId}`);
    }
    
    // Add DFSA-specific metadata to assistant messages
    if (message.role === 'assistant' && message.sources) {
      message.sources = message.sources.map(source => {
        // Extract DFSA-specific metadata from source
        if (source.url && source.url.includes('DFSA')) {
          const sectionMatch = source.url.match(/Section\s+([0-9.]+)/i);
          if (sectionMatch) {
            return {
              ...source,
              section: sectionMatch[1]
            };
          }
          
          const moduleMatch = source.url.match(/Module\s+([A-Za-z0-9]+)/i);
          if (moduleMatch) {
            return {
              ...source,
              module: moduleMatch[1]
            };
          }
        }
        
        return source;
      });
    }
    
    conversation.messages.push(message);
    conversation.updatedAt = new Date();
  }
  
  /**
   * Delete a conversation
   * @param conversationId Conversation ID
   */
  public async deleteConversation(conversationId: string): Promise<void> {
    if (!this.conversations.has(conversationId)) {
      throw new Error(`Conversation not found: ${conversationId}`);
    }
    
    this.conversations.delete(conversationId);
  }
}