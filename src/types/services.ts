// Service interfaces for the DFSA Rulebook RAG system

import {
  ScrapedContent,
  DocumentChunk,
  RAGResponse,
  ScrapeJob,
  ScrapeConfig,
  TestScrapeResult,
  QualityGateResult,
  ValidationResult,
  LLMResponse,
  QueryConfig,
  LLMModel,
  EmbeddingModel
} from './index';

// URL Discovery Service Interface
export interface URLDiscoveryService {
  discoverRecursive(baseUrl: string): AsyncGenerator<string>;
  discoverComprehensive(baseUrl: string): Promise<string[]>;
  validateUrl(url: string): boolean;
  isDfsaDomain(url: string): boolean;
}

// Scraping Orchestrator Interface
export interface ScrapingOrchestrator {
  performTestScrape(urls: string[]): Promise<TestScrapeResult>;
  executeFullScrape(urls: string[], config: ScrapeConfig): Promise<ScrapeJob>;
  validateQualityGate(content: ScrapedContent[]): Promise<QualityGateResult>;
  pauseJob(jobId: string): Promise<void>;
  resumeJob(jobId: string): Promise<void>;
  cancelJob(jobId: string): Promise<void>;
}

// Content Validator Interface
export interface ContentValidator {
  validateContent(content: ScrapedContent): ValidationResult;
  detectContentType(content: string): string;
  extractQualityMetrics(content: ScrapedContent): import('./index').QualityMetrics;
  checkCompleteness(content: ScrapedContent): boolean;
  detectExtractionErrors(content: ScrapedContent): string[];
}

// Brightdata Client Interface
export interface BrightdataClient {
  scrapeUrl(url: string): Promise<ScrapedContent>;
  scrapeUrls(urls: string[]): Promise<ScrapedContent[]>;
  healthCheck(): Promise<boolean>;
  getRemainingCredits(): Promise<number>;
}

// Content Processor Interface
export interface ContentProcessor {
  processContent(rawContent: ScrapedContent): Promise<ScrapedContent>;
  extractMetadata(content: ScrapedContent): import('./index').ContentMetadata;
  chunkDocument(content: ScrapedContent): DocumentChunk[];
  cleanText(text: string): string;
  preserveStructure(content: ScrapedContent): ScrapedContent;
}

// Document Chunker Interface
export interface DocumentChunker {
  chunkBySection(content: string, metadata: import('./index').ContentMetadata): DocumentChunk[];
  chunkBySemantic(content: string, maxTokens: number): DocumentChunk[];
  preserveContext(chunks: DocumentChunk[]): DocumentChunk[];
  optimizeChunkSize(chunks: DocumentChunk[]): DocumentChunk[];
}

// RAG Orchestrator Interface
export interface RAGOrchestrator {
  processQuery(query: string, config: QueryConfig): Promise<RAGResponse>;
  generateEmbeddings(content: DocumentChunk[]): Promise<void>;
  retrieveRelevantChunks(query: string, limit: number): Promise<DocumentChunk[]>;
  generateResponse(query: string, chunks: DocumentChunk[]): Promise<string>;
}

// Embedding Service Interface
export interface EmbeddingService {
  embed(text: string, model: EmbeddingModel): Promise<number[]>;
  batchEmbed(texts: string[], model: EmbeddingModel): Promise<number[][]>;
  getSupportedModels(): EmbeddingModel[];
  healthCheck(model: EmbeddingModel): Promise<boolean>;
}

// LLM Service Interface
export interface LLMService {
  generateResponse(prompt: string, context: string[], model: LLMModel): Promise<LLMResponse>;
  getSupportedModels(): LLMModel[];
  healthCheck(model: LLMModel): Promise<boolean>;
  estimateTokens(text: string): number;
}

// Vector Store Interface
export interface VectorStore {
  upsert(chunks: DocumentChunk[]): Promise<void>;
  query(embedding: number[], limit: number): Promise<DocumentChunk[]>;
  delete(ids: string[]): Promise<void>;
  healthCheck(): Promise<boolean>;
  getStats(): Promise<VectorStoreStats>;
}

export interface VectorStoreStats {
  totalVectors: number;
  dimension: number;
  indexSize: string;
  lastUpdated: Date;
}

// Job Manager Interface
export interface JobManager {
  createJob(urls: string[], config: ScrapeConfig): Promise<ScrapeJob>;
  getJob(jobId: string): Promise<ScrapeJob | null>;
  updateJobProgress(jobId: string, progress: import('./index').JobProgress): Promise<void>;
  listJobs(status?: import('./index').JobStatus): Promise<ScrapeJob[]>;
  retryFailedUrls(jobId: string): Promise<void>;
}

// External Service Manager Interface
export interface ExternalServiceManager {
  checkServiceHealth(serviceName: string): Promise<boolean>;
  getServiceStatus(): Promise<ServiceStatus[]>;
  rotateApiKeys(): Promise<void>;
  validateConfiguration(): Promise<ConfigValidationResult>;
}

export interface ServiceStatus {
  name: string;
  status: 'healthy' | 'degraded' | 'down';
  lastChecked: Date;
  responseTime?: number;
  error?: string;
}

export interface ConfigValidationResult {
  isValid: boolean;
  missingKeys: string[];
  invalidKeys: string[];
  warnings: string[];
}