// Core data models for the DFSA Rulebook RAG system

export interface ScrapedContent {
  id: string;
  url: string;
  title: string;
  content: string;
  contentType: ContentType;
  metadata: ContentMetadata;
  qualityMetrics: QualityMetrics;
  scrapedAt: Date;
  status: ContentStatus;
}

export interface DocumentChunk {
  id: string;
  contentId: string;
  chunkIndex: number;
  text: string;
  embedding?: number[];
  metadata: ChunkMetadata;
  tokens: number;
}

export interface RAGResponse {
  query: string;
  response: string;
  sources: SourceCitation[];
  confidence: number;
  model: string;
  processingTime: number;
}

export interface QualityMetrics {
  wordCount: number;
  structureCompleteness: number;
  extractionConfidence: number;
  contentTypeAccuracy: number;
  validationErrors: string[];
}

export interface ContentMetadata {
  url: string;
  title: string;
  sectionHierarchy: string[];
  lastModified?: Date;
  contentLength: number;
  language: string;
  extractedAt: Date;
}

export interface ChunkMetadata {
  sectionTitle?: string;
  sectionLevel: number;
  parentSections: string[];
  pageNumber?: number;
  startPosition: number;
  endPosition: number;
}

export interface SourceCitation {
  url: string;
  title: string;
  section?: string;
  relevanceScore: number;
  chunkId: string;
}

export interface ScrapeJob {
  id: string;
  urls: string[];
  status: JobStatus;
  progress: JobProgress;
  config: ScrapeConfig;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  errors: ScrapeError[];
}

export interface JobProgress {
  totalUrls: number;
  completedUrls: number;
  failedUrls: number;
  currentUrl?: string;
  percentage: number;
}

export interface ScrapeConfig {
  mode: 'recursive' | 'comprehensive';
  maxDepth?: number;
  delay: number;
  retryAttempts: number;
  qualityGateInterval: number;
  testScrapeSize: number;
}

export interface ScrapeError {
  url: string;
  error: string;
  timestamp: Date;
  retryCount: number;
}

export interface TestScrapeResult {
  urls: string[];
  results: ScrapedContent[];
  qualityScore: number;
  recommendations: string[];
  approved: boolean;
}

export interface QualityGateResult {
  passed: boolean;
  score: number;
  issues: QualityIssue[];
  recommendations: string[];
}

export interface QualityIssue {
  type: 'content' | 'structure' | 'extraction';
  severity: 'low' | 'medium' | 'high';
  description: string;
  affectedUrls: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  metrics: QualityMetrics;
}

export interface LLMResponse {
  content: string;
  model: string;
  usage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  finishReason: string;
}

export interface QueryConfig {
  model: LLMModel;
  embeddingModel: EmbeddingModel;
  maxChunks: number;
  temperature: number;
  includeMetadata: boolean;
}

// Enums
export enum ContentType {
  HTML = 'html',
  PDF = 'pdf',
  TEXT = 'text',
  TABLE = 'table',
  IMAGE = 'image'
}

export enum ContentStatus {
  PENDING = 'pending',
  SCRAPED = 'scraped',
  PROCESSED = 'processed',
  EMBEDDED = 'embedded',
  FAILED = 'failed'
}

export enum JobStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export enum LLMModel {
  GPT_4 = 'gpt-4',
  GPT_4_TURBO = 'gpt-4-turbo',
  GPT_3_5_TURBO = 'gpt-3.5-turbo',
  CLAUDE_3_OPUS = 'claude-3-opus',
  CLAUDE_3_SONNET = 'claude-3-sonnet',
  CLAUDE_3_HAIKU = 'claude-3-haiku'
}

export enum EmbeddingModel {
  OPENAI_ADA_002 = 'text-embedding-ada-002',
  OPENAI_3_SMALL = 'text-embedding-3-small',
  OPENAI_3_LARGE = 'text-embedding-3-large',
  COHERE_EMBED_V3 = 'embed-english-v3.0'
}