import { EnterpriseConfig } from './enterprise-scraper-architecture';

/**
 * ENTERPRISE CONFIGURATION PRESETS
 * 
 * Battle-tested configurations for different scales:
 * - Development: Small scale testing
 * - Production: High-performance production
 * - Million Scale: Ultra-high scale operations
 */

export const ENTERPRISE_CONFIGS = {
  /**
   * Development Configuration
   * For testing and development with 1K-10K URLs
   */
  DEVELOPMENT: {
    redis: {
      host: 'localhost',
      port: 6379,
      db: 0,
      maxRetriesPerRequest: 3,
      retryDelayOnFailover: 100
    },
    queue: {
      name: 'brightdata-scraping-dev',
      concurrency: 10,
      maxStalledCount: 3,
      stalledInterval: 30000,
      maxConcurrency: 50,
      removeOnComplete: 100,
      removeOnFail: 50
    },
    circuitBreaker: {
      failureThreshold: 5,
      recoveryTimeout: 30000,
      monitoringPeriod: 5000,
      halfOpenMaxCalls: 3
    },
    rateLimiting: {
      requestsPerSecond: 5,
      burstLimit: 10,
      windowSizeMs: 60000,
      distributedLimiting: false
    },
    caching: {
      enableContentCache: true,
      enableUrlCache: true,
      contentCacheTTL: 3600,
      urlCacheTTL: 7200,
      maxCacheSize: 1000,
      compressionEnabled: false
    },
    brightdata: {
      zones: ['mcp_unlocker', 'web_unlocker1', 'web_unlocker2'],
      apiKey: 'e5e6337c844ca6b30d53dc6094079af8950047632c8428686b6fc40352b7f4ee',
      timeout: 45000,
      maxRetries: 3,
      backoffMultiplier: 2,
      jitterEnabled: true
    },
    monitoring: {
      healthCheckInterval: 30000,
      metricsRetentionDays: 7,
      alertThresholds: {
        errorRate: 10,
        responseTime: 10000,
        queueDepth: 1000
      }
    }
  } as EnterpriseConfig,

  /**
   * Production Configuration
   * For production use with 10K-100K URLs
   */
  PRODUCTION: {
    redis: {
      host: 'localhost',
      port: 6379,
      db: 1,
      maxRetriesPerRequest: 5,
      retryDelayOnFailover: 200
    },
    queue: {
      name: 'brightdata-scraping-prod',
      concurrency: 50,
      maxStalledCount: 5,
      stalledInterval: 30000,
      maxConcurrency: 200,
      removeOnComplete: 500,
      removeOnFail: 200
    },
    circuitBreaker: {
      failureThreshold: 10,
      recoveryTimeout: 60000,
      monitoringPeriod: 10000,
      halfOpenMaxCalls: 5
    },
    rateLimiting: {
      requestsPerSecond: 15,
      burstLimit: 30,
      windowSizeMs: 60000,
      distributedLimiting: true
    },
    caching: {
      enableContentCache: true,
      enableUrlCache: true,
      contentCacheTTL: 7200,
      urlCacheTTL: 14400,
      maxCacheSize: 10000,
      compressionEnabled: true
    },
    brightdata: {
      zones: [
        'mcp_unlocker',
        'web_unlocker1', 'web_unlocker2', 'web_unlocker3', 'web_unlocker4', 'web_unlocker5',
        'web_unlocker6', 'web_unlocker7', 'web_unlocker8', 'web_unlocker9', 'web_unlocker10',
        'web_unlocker11', 'web_unlocker12', 'web_unlocker13', 'web_unlocker14', 'web_unlocker15'
      ],
      apiKey: 'e5e6337c844ca6b30d53dc6094079af8950047632c8428686b6fc40352b7f4ee',
      timeout: 60000,
      maxRetries: 5,
      backoffMultiplier: 1.5,
      jitterEnabled: true
    },
    monitoring: {
      healthCheckInterval: 15000,
      metricsRetentionDays: 30,
      alertThresholds: {
        errorRate: 5,
        responseTime: 8000,
        queueDepth: 5000
      }
    }
  } as EnterpriseConfig,

  /**
   * Million Scale Configuration
   * For ultra-high scale operations with 100K+ URLs
   */
  MILLION_SCALE: {
    redis: {
      host: 'localhost',
      port: 6379,
      db: 2,
      maxRetriesPerRequest: 10,
      retryDelayOnFailover: 500
    },
    queue: {
      name: 'brightdata-scraping-million',
      concurrency: 300, // Increased for 30 zones
      maxStalledCount: 15,
      stalledInterval: 60000,
      maxConcurrency: 1500, // 50 per zone max
      removeOnComplete: 2000,
      removeOnFail: 1000
    },
    circuitBreaker: {
      failureThreshold: 20,
      recoveryTimeout: 120000,
      monitoringPeriod: 15000,
      halfOpenMaxCalls: 10
    },
    rateLimiting: {
      requestsPerSecond: 100, // Increased for 30 zones
      burstLimit: 200,
      windowSizeMs: 60000,
      distributedLimiting: true
    },
    caching: {
      enableContentCache: true,
      enableUrlCache: true,
      contentCacheTTL: 14400,
      urlCacheTTL: 28800,
      maxCacheSize: 100000,
      compressionEnabled: true
    },
    brightdata: {
      zones: [
        'mcp_unlocker',
        'web_unlocker1', 'web_unlocker2', 'web_unlocker3', 'web_unlocker4', 'web_unlocker5',
        'web_unlocker6', 'web_unlocker7', 'web_unlocker8', 'web_unlocker9', 'web_unlocker10',
        'web_unlocker11', 'web_unlocker12', 'web_unlocker13', 'web_unlocker14', 'web_unlocker15',
        'web_unlocker16', 'web_unlocker17', 'web_unlocker18', 'web_unlocker19', 'web_unlocker20',
        'web_unlocker21', 'web_unlocker22', 'web_unlocker23', 'web_unlocker24', 'web_unlocker25',
        'web_unlocker26', 'web_unlocker27', 'web_unlocker28', 'web_unlocker29'
      ],
      apiKey: 'e5e6337c844ca6b30d53dc6094079af8950047632c8428686b6fc40352b7f4ee',
      timeout: 90000,
      maxRetries: 7,
      backoffMultiplier: 1.2,
      jitterEnabled: true
    },
    monitoring: {
      healthCheckInterval: 10000,
      metricsRetentionDays: 90,
      alertThresholds: {
        errorRate: 3,
        responseTime: 6000,
        queueDepth: 20000
      }
    }
  } as EnterpriseConfig,

  /**
   * ULTRA SCALE Configuration
   * For extreme scale operations with 1M+ URLs using all 30 zones
   * Maximum theoretical throughput: ~300-500 pages/second
   */
  ULTRA_SCALE: {
    redis: {
      host: 'localhost',
      port: 6379,
      db: 3,
      maxRetriesPerRequest: 15,
      retryDelayOnFailover: 1000
    },
    queue: {
      name: 'brightdata-scraping-ultra',
      concurrency: 600, // 20 per zone across 30 zones
      maxStalledCount: 20,
      stalledInterval: 90000,
      maxConcurrency: 1500, // 50 per zone max burst
      removeOnComplete: 5000,
      removeOnFail: 2000
    },
    circuitBreaker: {
      failureThreshold: 30,
      recoveryTimeout: 180000, // 3 minutes
      monitoringPeriod: 20000,
      halfOpenMaxCalls: 15
    },
    rateLimiting: {
      requestsPerSecond: 150, // Conservative for 30 zones
      burstLimit: 300,
      windowSizeMs: 60000,
      distributedLimiting: true
    },
    caching: {
      enableContentCache: true,
      enableUrlCache: true,
      contentCacheTTL: 21600, // 6 hours
      urlCacheTTL: 43200, // 12 hours
      maxCacheSize: 500000, // 500K items
      compressionEnabled: true
    },
    brightdata: {
      zones: [
        'mcp_unlocker',
        'web_unlocker1', 'web_unlocker2', 'web_unlocker3', 'web_unlocker4', 'web_unlocker5',
        'web_unlocker6', 'web_unlocker7', 'web_unlocker8', 'web_unlocker9', 'web_unlocker10',
        'web_unlocker11', 'web_unlocker12', 'web_unlocker13', 'web_unlocker14', 'web_unlocker15',
        'web_unlocker16', 'web_unlocker17', 'web_unlocker18', 'web_unlocker19', 'web_unlocker20',
        'web_unlocker21', 'web_unlocker22', 'web_unlocker23', 'web_unlocker24', 'web_unlocker25',
        'web_unlocker26', 'web_unlocker27', 'web_unlocker28', 'web_unlocker29'
      ],
      apiKey: 'e5e6337c844ca6b30d53dc6094079af8950047632c8428686b6fc40352b7f4ee',
      timeout: 120000, // 2 minutes for ultra-scale
      maxRetries: 10,
      backoffMultiplier: 1.1, // Gentle backoff
      jitterEnabled: true
    },
    monitoring: {
      healthCheckInterval: 5000, // Very frequent monitoring
      metricsRetentionDays: 180, // 6 months retention
      alertThresholds: {
        errorRate: 2, // Very strict
        responseTime: 5000,
        queueDepth: 50000
      }
    }
  } as EnterpriseConfig
};

/**
 * Enterprise Scraper Factory
 * Creates optimally configured scrapers for different scales
 */
export class EnterpriseScraperFactory {
  /**
   * Create a development scraper for testing
   */
  static createDevelopmentScraper() {
    const { EnterpriseScraper } = require('./enterprise-scraper-architecture');
    return new EnterpriseScraper(ENTERPRISE_CONFIGS.DEVELOPMENT);
  }

  /**
   * Create a production scraper for normal operations
   */
  static createProductionScraper() {
    const { EnterpriseScraper } = require('./enterprise-scraper-architecture');
    return new EnterpriseScraper(ENTERPRISE_CONFIGS.PRODUCTION);
  }

  /**
   * Create a million-scale scraper for ultra-high volume
   */
  static createMillionScaleScraper() {
    const { EnterpriseScraper } = require('./enterprise-scraper-architecture');
    return new EnterpriseScraper(ENTERPRISE_CONFIGS.MILLION_SCALE);
  }

  /**
   * Create an ultra-scale scraper for extreme volume (1M+ URLs, 30 zones)
   */
  static createUltraScaleScraper() {
    const { EnterpriseScraper } = require('./enterprise-scraper-architecture');
    return new EnterpriseScraper(ENTERPRISE_CONFIGS.ULTRA_SCALE);
  }

  /**
   * Create a custom scraper with user-defined configuration
   */
  static createCustomScraper(config: Partial<EnterpriseConfig>) {
    const { EnterpriseScraper } = require('./enterprise-scraper-architecture');
    const baseConfig = ENTERPRISE_CONFIGS.PRODUCTION;
    const mergedConfig = this.mergeConfigs(baseConfig, config);
    return new EnterpriseScraper(mergedConfig);
  }

  /**
   * Get recommended configuration based on expected scale
   */
  static getRecommendedConfig(expectedUrls: number): EnterpriseConfig {
    if (expectedUrls < 10000) {
      return ENTERPRISE_CONFIGS.DEVELOPMENT;
    } else if (expectedUrls < 100000) {
      return ENTERPRISE_CONFIGS.PRODUCTION;
    } else if (expectedUrls < 500000) {
      return ENTERPRISE_CONFIGS.MILLION_SCALE;
    } else {
      return ENTERPRISE_CONFIGS.ULTRA_SCALE; // 500K+ URLs
    }
  }

  /**
   * Validate configuration for common issues
   */
  static validateConfig(config: EnterpriseConfig): { valid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check Redis configuration
    if (!config.redis.host || !config.redis.port) {
      issues.push('Redis host and port must be specified');
    }

    // Check Brightdata configuration
    if (!config.brightdata.apiKey) {
      issues.push('Brightdata API key is required');
    }

    if (config.brightdata.zones.length === 0) {
      issues.push('At least one Brightdata zone must be configured');
    }

    // Check rate limiting
    if (config.rateLimiting.requestsPerSecond <= 0) {
      issues.push('Requests per second must be greater than 0');
    }

    // Check circuit breaker
    if (config.circuitBreaker.failureThreshold <= 0) {
      issues.push('Circuit breaker failure threshold must be greater than 0');
    }

    // Check queue configuration
    if (config.queue.concurrency <= 0) {
      issues.push('Queue concurrency must be greater than 0');
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }

  /**
   * Deep merge configuration objects
   */
  private static mergeConfigs(base: EnterpriseConfig, override: Partial<EnterpriseConfig>): EnterpriseConfig {
    const merged = JSON.parse(JSON.stringify(base)); // Deep clone

    for (const [key, value] of Object.entries(override)) {
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        merged[key] = { ...merged[key], ...value };
      } else {
        merged[key] = value;
      }
    }

    return merged;
  }

  /**
   * Get configuration recommendations based on system resources
   */
  static getSystemOptimizedConfig(): Partial<EnterpriseConfig> {
    const totalMemory = require('os').totalmem();
    const cpuCount = require('os').cpus().length;

    // Calculate optimal settings based on system resources
    const memoryGB = totalMemory / (1024 * 1024 * 1024);
    const recommendedConcurrency = Math.min(cpuCount * 10, 200);
    const recommendedCacheSize = Math.floor(memoryGB * 1000);

    return {
      queue: {
        concurrency: recommendedConcurrency,
        maxConcurrency: recommendedConcurrency * 2
      },
      caching: {
        maxCacheSize: recommendedCacheSize
      },
      rateLimiting: {
        requestsPerSecond: Math.min(recommendedConcurrency / 2, 50)
      }
    } as Partial<EnterpriseConfig>;
  }
}

/**
 * Configuration utilities
 */
export class ConfigUtils {
  /**
   * Estimate cost for scraping operation
   */
  static estimateCost(urlCount: number, costPerThousand: number = 1.50): {
    estimatedCost: number;
    costBreakdown: {
      requests: number;
      costPerRequest: number;
      totalCost: number;
    };
  } {
    const costPerRequest = costPerThousand / 1000;
    const totalCost = urlCount * costPerRequest;

    return {
      estimatedCost: totalCost,
      costBreakdown: {
        requests: urlCount,
        costPerRequest,
        totalCost
      }
    };
  }

  /**
   * Estimate completion time based on configuration
   */
  static estimateCompletionTime(
    urlCount: number, 
    config: EnterpriseConfig
  ): {
    estimatedMinutes: number;
    breakdown: {
      concurrency: number;
      avgResponseTime: number;
      throughput: number;
    };
  } {
    const totalConcurrency = config.queue.concurrency;
    const avgResponseTime = 5000; // Conservative estimate
    const throughput = totalConcurrency / (avgResponseTime / 1000);
    const estimatedSeconds = urlCount / throughput;

    return {
      estimatedMinutes: Math.ceil(estimatedSeconds / 60),
      breakdown: {
        concurrency: totalConcurrency,
        avgResponseTime,
        throughput
      }
    };
  }

  /**
   * Generate configuration summary for logging
   */
  static getConfigSummary(config: EnterpriseConfig): string {
    return `
Enterprise Scraper Configuration:
- Zones: ${config.brightdata.zones.length}
- Total Concurrency: ${config.queue.concurrency}
- Rate Limit: ${config.rateLimiting.requestsPerSecond} req/sec
- Cache Size: ${config.caching.maxCacheSize}
- Circuit Breaker Threshold: ${config.circuitBreaker.failureThreshold}
- Timeout: ${config.brightdata.timeout}ms
- Retries: ${config.brightdata.maxRetries}
    `.trim();
  }
}
