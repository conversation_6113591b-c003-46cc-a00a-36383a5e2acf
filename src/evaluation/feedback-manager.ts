/**
 * Feedback Manager
 * 
 * Manages user feedback collection and analysis
 */

import { v4 as uuidv4 } from 'uuid';
import { UserFeedback, FeedbackAnalysis } from './types';
import { logger } from '../utils/logger';

export class FeedbackManager {
  private feedbackStore: UserFeedback[] = []; // In a real implementation, this would be a database

  constructor() {
    logger.info('FeedbackManager initialized');
  }

  /**
   * Record user feedback on a response
   */
  async recordFeedback(data: {
    conversationId: string;
    messageId: string;
    rating: 'positive' | 'negative' | 'neutral';
    category?: 'accuracy' | 'relevance' | 'completeness' | 'clarity' | 'other';
    comment?: string;
    userId?: string;
  }): Promise<UserFeedback> {
    try {
      const feedback: UserFeedback = {
        id: uuidv4(),
        conversationId: data.conversationId,
        messageId: data.messageId,
        rating: data.rating,
        category: data.category || 'other',
        comment: data.comment || '',
        userId: data.userId || '',
        createdAt: new Date(),
      };

      // In a real implementation, this would save to a database
      this.feedbackStore.push(feedback);
      logger.info('Recorded user feedback', { id: feedback.id, rating: feedback.rating });

      return feedback;
    } catch (error) {
      logger.error('Error recording user feedback', { error });
      throw new Error('Failed to record user feedback');
    }
  }

  /**
   * Get feedback by ID
   */
  async getFeedback(id: string): Promise<UserFeedback | null> {
    try {
      const feedback = this.feedbackStore.find(f => f.id === id);
      return feedback || null;
    } catch (error) {
      logger.error('Error getting feedback', { error, id });
      throw new Error('Failed to get feedback');
    }
  }

  /**
   * Get feedback by conversation ID
   */
  async getFeedbackByConversation(conversationId: string): Promise<UserFeedback[]> {
    try {
      return this.feedbackStore.filter(f => f.conversationId === conversationId);
    } catch (error) {
      logger.error('Error getting feedback by conversation', { error, conversationId });
      throw new Error('Failed to get feedback by conversation');
    }
  }

  /**
   * Get all feedback
   */
  async getAllFeedback(): Promise<UserFeedback[]> {
    try {
      return [...this.feedbackStore];
    } catch (error) {
      logger.error('Error getting all feedback', { error });
      throw new Error('Failed to get all feedback');
    }
  }

  /**
   * Delete feedback by ID
   */
  async deleteFeedback(id: string): Promise<boolean> {
    try {
      const index = this.feedbackStore.findIndex(f => f.id === id);
      if (index === -1) {
        return false;
      }

      this.feedbackStore.splice(index, 1);
      logger.info('Deleted feedback', { id });
      return true;
    } catch (error) {
      logger.error('Error deleting feedback', { error, id });
      throw new Error('Failed to delete feedback');
    }
  }

  /**
   * Analyze feedback for a specific time period
   */
  async analyzeFeedback(options: {
    start: Date;
    end: Date;
    conversationId?: string;
  }): Promise<FeedbackAnalysis> {
    try {
      let feedback = this.feedbackStore.filter(f => 
        f.createdAt >= options.start && f.createdAt <= options.end
      );

      if (options.conversationId) {
        feedback = feedback.filter(f => f.conversationId === options.conversationId);
      }

      // Calculate metrics
      const totalCount = feedback.length;
      const positiveCount = feedback.filter((f: UserFeedback) => f.rating === 'positive').length;
      const negativeCount = feedback.filter((f: UserFeedback) => f.rating === 'negative').length;
      const neutralCount = feedback.filter((f: UserFeedback) => f.rating === 'neutral').length;

      const positivePercentage = totalCount > 0 ? (positiveCount / totalCount) * 100 : 0;
      const negativePercentage = totalCount > 0 ? (negativeCount / totalCount) * 100 : 0;
      const neutralPercentage = totalCount > 0 ? (neutralCount / totalCount) * 100 : 0;

      // Analyze categories
      const categoryBreakdown: Record<string, number> = {};
      feedback.forEach((f: UserFeedback) => {
        const category = f.category || 'other';
        categoryBreakdown[category] = (categoryBreakdown[category] || 0) + 1;
      });

      // Identify top issues (negative feedback categories)
      const topIssues: Array<{ category: string; count: number; percentage: number }> = [];
      feedback.forEach((f: UserFeedback) => {
        if (f.rating === 'negative' && f.category) {
          const existing = topIssues.find(issue => issue.category === f.category);
          if (existing) {
            existing.count++;
          } else {
            topIssues.push({ category: f.category, count: 1, percentage: 0 });
          }
        }
      });

      // Calculate percentages for top issues
      topIssues.forEach(issue => {
        issue.percentage = totalCount > 0 ? (issue.count / totalCount) * 100 : 0;
      });

      // Sort by count descending
      topIssues.sort((a, b) => b.count - a.count);

      const analysis: FeedbackAnalysis = {
        id: uuidv4(),
        period: {
          start: options.start,
          end: options.end,
        },
        metrics: {
          totalFeedback: totalCount,
          positivePercentage,
          negativePercentage,
          neutralPercentage,
          categoryBreakdown,
        },
        trends: [], // TODO: Implement trend analysis
        topIssues,
        createdAt: new Date(),
      };

      logger.info('Analyzed feedback', { 
        totalCount, 
        positivePercentage: positivePercentage.toFixed(1),
        topIssuesCount: topIssues.length 
      });

      return analysis;
    } catch (error) {
      logger.error('Error analyzing feedback', { error });
      throw new Error('Failed to analyze feedback');
    }
  }

  /**
   * Create feedback
   */
  async createFeedback(data: {
    conversationId: string;
    messageId: string;
    rating: 'positive' | 'negative' | 'neutral';
    category?: 'accuracy' | 'relevance' | 'completeness' | 'clarity' | 'other';
    comment?: string;
    userId?: string;
  }): Promise<UserFeedback> {
    try {
      const feedback: UserFeedback = {
        id: uuidv4(),
        conversationId: data.conversationId,
        messageId: data.messageId,
        rating: data.rating,
        category: data.category || 'other',
        comment: data.comment || '',
        userId: data.userId || '',
        createdAt: new Date(),
      };

      // In a real implementation, this would save to a database
      logger.info('Created feedback', { id: feedback.id, rating: feedback.rating });
      
      return feedback;
    } catch (error) {
      logger.error('Error creating feedback', { error });
      throw new Error('Failed to create feedback');
    }
  }

  /**
   * Get conversation feedback
   */
  async getConversationFeedback(conversationId: string): Promise<UserFeedback[]> {
    try {
      // Mock implementation - in real app would fetch from database
      return [];
    } catch (error) {
      logger.error('Error getting conversation feedback', { error, conversationId });
      throw new Error('Failed to get conversation feedback');
    }
  }



  /**
   * Get feedback analysis by ID
   */
  async getFeedbackAnalysis(id: string): Promise<FeedbackAnalysis | null> {
    // In a real implementation, this would retrieve from a database
    // For now, we'll return null as analyses are not persisted
    return null;
  }

  /**
   * Get all feedback analyses
   */
  async getAllFeedbackAnalyses(): Promise<FeedbackAnalysis[]> {
    // In a real implementation, this would retrieve from a database
    // For now, we'll return empty array as analyses are not persisted
    return [];
  }

  /**
   * Get feedback statistics
   */
  async getFeedbackStats(): Promise<{
    totalFeedback: number;
    positiveCount: number;
    negativeCount: number;
    neutralCount: number;
    averageRating: number;
  }> {
    try {
      const totalFeedback = this.feedbackStore.length;
      const positiveCount = this.feedbackStore.filter(f => f.rating === 'positive').length;
      const negativeCount = this.feedbackStore.filter(f => f.rating === 'negative').length;
      const neutralCount = this.feedbackStore.filter(f => f.rating === 'neutral').length;

      // Calculate average rating (positive=1, neutral=0, negative=-1)
      let totalRating = 0;
      this.feedbackStore.forEach(f => {
        if (f.rating === 'positive') totalRating += 1;
        else if (f.rating === 'negative') totalRating -= 1;
        // neutral adds 0
      });

      const averageRating = totalFeedback > 0 ? totalRating / totalFeedback : 0;

      return {
        totalFeedback,
        positiveCount,
        negativeCount,
        neutralCount,
        averageRating,
      };
    } catch (error) {
      logger.error('Error getting feedback stats', { error });
      throw new Error('Failed to get feedback stats');
    }
  }
}