/**
 * Multi-provider embedding service interface and base implementation
 * Supports OpenAI, Cohere, and local Hugging Face models
 */

import { logger } from '../utils/logger';

// Helper function to safely extract error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

// Core interfaces
export interface EmbeddingVector {
  embedding: number[];
  index: number;
  object: 'embedding';
}

export interface EmbeddingResponse {
  data: EmbeddingVector[];
  model: string;
  object: 'list';
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

export interface EmbeddingRequest {
  input: string | string[];
  model?: string;
  dimensions?: number;
  encoding_format?: 'float' | 'base64';
  user?: string;
}

export interface BatchEmbeddingRequest {
  inputs: string[];
  model?: string;
  dimensions?: number;
  batchSize?: number;
  maxRetries?: number;
  retryDelay?: number;
}

export interface BatchEmbeddingResponse {
  embeddings: EmbeddingVector[];
  totalProcessed: number;
  successful: number;
  failed: number;
  errors: Array<{
    index: number;
    input: string;
    error: string;
  }>;
  processingTime: number;
  averageLatency: number;
}

// Provider-specific configuration
export interface EmbeddingProviderConfig {
  provider: 'openai' | 'cohere' | 'huggingface';
  apiKey?: string;
  baseURL?: string;
  model: string;
  dimensions?: number;
  maxTokens?: number;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  rateLimit?: {
    requestsPerMinute: number;
    tokensPerMinute: number;
  };
}

export interface EmbeddingServiceConfig {
  primaryProvider: EmbeddingProviderConfig;
  fallbackProviders?: EmbeddingProviderConfig[];
  enableFallback: boolean;
  enableBatching: boolean;
  defaultBatchSize: number;
  maxConcurrency: number;
  enableCaching: boolean;
  cacheConfig?: {
    ttl: number; // Time to live in seconds
    maxSize: number; // Maximum number of cached embeddings
  };
  enableMetrics: boolean;
}

// Abstract base class for embedding providers
export abstract class EmbeddingProvider {
  protected config: EmbeddingProviderConfig;
  protected metrics: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    totalTokens: number;
    averageLatency: number;
    lastRequestTime: Date | null;
  };

  constructor(config: EmbeddingProviderConfig) {
    this.config = config;
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTokens: 0,
      averageLatency: 0,
      lastRequestTime: null
    };
  }

  abstract generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse>;
  abstract validateConfig(): boolean;
  abstract getProviderInfo(): {
    name: string;
    version: string;
    supportedModels: string[];
    maxDimensions: number;
    maxTokensPerRequest: number;
  };

  // Public method to access config for rate limiting
  public getConfig(): EmbeddingProviderConfig {
    return this.config;
  }

  // Common utility methods
  protected updateMetrics(success: boolean, tokens: number, latency: number): void {
    this.metrics.totalRequests++;
    this.metrics.lastRequestTime = new Date();
    
    if (success) {
      this.metrics.successfulRequests++;
      this.metrics.totalTokens += tokens;
      
      // Update average latency using exponential moving average
      const alpha = 0.1;
      this.metrics.averageLatency = this.metrics.averageLatency === 0 
        ? latency 
        : alpha * latency + (1 - alpha) * this.metrics.averageLatency;
    } else {
      this.metrics.failedRequests++;
    }
  }

  protected normalizeInput(input: string | string[]): string[] {
    return Array.isArray(input) ? input : [input];
  }

  protected validateInput(input: string | string[]): void {
    const inputs = this.normalizeInput(input);
    
    if (inputs.length === 0) {
      throw new Error('Input cannot be empty');
    }

    for (const text of inputs) {
      if (typeof text !== 'string') {
        throw new Error('All inputs must be strings');
      }
      if (text.trim().length === 0) {
        throw new Error('Input text cannot be empty or whitespace only');
      }
    }
  }

  protected async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  protected async retryWithBackoff<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.config.retryAttempts || 3,
    baseDelay: number = this.config.retryDelay || 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          break;
        }

        // Exponential backoff with jitter
        const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
        logger.warn(`Embedding request failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${delay}ms`, {
          error: getErrorMessage(error),
          provider: this.config.provider
        });
        
        await this.sleep(delay);
      }
    }

    throw lastError!;
  }

  getMetrics() {
    return {
      ...this.metrics,
      successRate: this.metrics.totalRequests > 0 
        ? this.metrics.successfulRequests / this.metrics.totalRequests 
        : 0
    };
  }

  resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      totalTokens: 0,
      averageLatency: 0,
      lastRequestTime: null
    };
  }
}

// Main embedding service class
export class EmbeddingService {
  private config: EmbeddingServiceConfig;
  private primaryProvider: EmbeddingProvider;
  private fallbackProviders: EmbeddingProvider[];
  private cache: Map<string, { embedding: EmbeddingVector; timestamp: number }>;
  private rateLimiter: Map<string, { requests: number; tokens: number; resetTime: number }>;

  constructor(config: EmbeddingServiceConfig) {
    this.config = config;
    this.cache = new Map();
    this.rateLimiter = new Map();
    this.fallbackProviders = [];
    
    // Initialize providers (will be implemented in provider-specific files)
    this.primaryProvider = this.createProvider(config.primaryProvider);
    
    if (config.fallbackProviders) {
      this.fallbackProviders = config.fallbackProviders.map(providerConfig => 
        this.createProvider(providerConfig)
      );
    }
  }

  private createProvider(config: EmbeddingProviderConfig): EmbeddingProvider {
    const { EmbeddingProviderFactory } = require('./provider-factory');
    return EmbeddingProviderFactory.createProvider(config);
  }

  async generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const startTime = Date.now();
    
    try {
      // Check cache first
      if (this.config.enableCaching) {
        const cached = this.getCachedEmbedding(request);
        if (cached) {
          logger.debug('Returning cached embedding', { 
            model: request.model || this.config.primaryProvider.model 
          });
          return cached;
        }
      }

      // Check rate limits
      await this.checkRateLimit(this.config.primaryProvider);

      // Try primary provider
      try {
        const response = await this.primaryProvider.generateEmbeddings(request);
        
        // Cache the response
        if (this.config.enableCaching) {
          this.cacheEmbedding(request, response);
        }

        return response;
      } catch (error) {
        logger.warn('Primary embedding provider failed', {
          provider: this.config.primaryProvider.provider,
          error: getErrorMessage(error)
        });

        // Try fallback providers if enabled
        if (this.config.enableFallback && this.fallbackProviders.length > 0) {
          return await this.tryFallbackProviders(request);
        }

        throw error;
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Embedding generation failed', {
        error: getErrorMessage(error),
        duration,
        request: {
          inputLength: Array.isArray(request.input) ? request.input.length : 1,
          model: request.model
        }
      });
      throw error;
    }
  }

  async generateBatchEmbeddings(request: BatchEmbeddingRequest): Promise<BatchEmbeddingResponse> {
    const startTime = Date.now();
    const batchSize = request.batchSize || this.config.defaultBatchSize;
    const embeddings: EmbeddingVector[] = [];
    const errors: Array<{ index: number; input: string; error: string }> = [];
    
    logger.info('Starting batch embedding generation', {
      totalInputs: request.inputs.length,
      batchSize,
      model: request.model || this.config.primaryProvider.model
    });

    // Process in batches
    for (let i = 0; i < request.inputs.length; i += batchSize) {
      const batch = request.inputs.slice(i, i + batchSize);
      
      try {
        const batchRequest: EmbeddingRequest = {
          input: batch,
          ...(request.model !== undefined && { model: request.model }),
          ...(request.dimensions !== undefined && { dimensions: request.dimensions })
        };

        const response = await this.generateEmbeddings(batchRequest);
        
        // Add embeddings with correct indices
        response.data.forEach((embedding, batchIndex) => {
          embeddings.push({
            ...embedding,
            index: i + batchIndex
          });
        });

      } catch (error) {
        // Record errors for this batch
        batch.forEach((input, batchIndex) => {
          errors.push({
            index: i + batchIndex,
            input,
            error: getErrorMessage(error)
          });
        });

        logger.warn('Batch embedding failed', {
          batchStart: i,
          batchSize: batch.length,
          error: getErrorMessage(error)
        });
      }

      // Add delay between batches to respect rate limits
      if (i + batchSize < request.inputs.length) {
        await this.sleep(100); // 100ms delay between batches
      }
    }

    const processingTime = Date.now() - startTime;
    const successful = embeddings.length;
    const failed = errors.length;

    logger.info('Batch embedding generation completed', {
      totalProcessed: request.inputs.length,
      successful,
      failed,
      processingTime,
      averageLatency: successful > 0 ? processingTime / successful : 0
    });

    return {
      embeddings,
      totalProcessed: request.inputs.length,
      successful,
      failed,
      errors,
      processingTime,
      averageLatency: successful > 0 ? processingTime / successful : 0
    };
  }

  private async tryFallbackProviders(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    for (const provider of this.fallbackProviders) {
      try {
        logger.info('Trying fallback provider', { 
          provider: provider.getProviderInfo().name 
        });
        
        await this.checkRateLimit(provider.getConfig());
        return await provider.generateEmbeddings(request);
      } catch (error) {
        logger.warn('Fallback provider failed', {
          provider: provider.getProviderInfo().name,
          error: getErrorMessage(error)
        });
        continue;
      }
    }

    throw new Error('All embedding providers failed');
  }

  private getCachedEmbedding(request: EmbeddingRequest): EmbeddingResponse | null {
    if (!this.config.enableCaching) return null;

    const cacheKey = this.generateCacheKey(request);
    const cached = this.cache.get(cacheKey);

    if (cached) {
      const now = Date.now();
      const ttl = (this.config.cacheConfig?.ttl || 3600) * 1000; // Convert to ms
      
      if (now - cached.timestamp < ttl) {
        return {
          data: [cached.embedding],
          model: request.model || this.config.primaryProvider.model,
          object: 'list',
          usage: {
            prompt_tokens: 0, // Cached, no tokens used
            total_tokens: 0
          }
        };
      } else {
        // Remove expired cache entry
        this.cache.delete(cacheKey);
      }
    }

    return null;
  }

  private cacheEmbedding(request: EmbeddingRequest, response: EmbeddingResponse): void {
    if (!this.config.enableCaching || response.data.length !== 1) return;

    const cacheKey = this.generateCacheKey(request);
    const maxSize = this.config.cacheConfig?.maxSize || 1000;

    // Remove oldest entries if cache is full
    if (this.cache.size >= maxSize) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey !== undefined) {
        this.cache.delete(oldestKey);
      }
    }

    const firstEmbedding = response.data[0];
    if (firstEmbedding) {
      this.cache.set(cacheKey, {
        embedding: firstEmbedding,
        timestamp: Date.now()
      });
    }
  }

  private generateCacheKey(request: EmbeddingRequest): string {
    const input = Array.isArray(request.input) ? request.input[0] || '' : request.input;
    const model = request.model || this.config.primaryProvider.model;
    const dimensions = request.dimensions || this.config.primaryProvider.dimensions || 'default';

    return `${model}:${dimensions}:${Buffer.from(input).toString('base64')}`;
  }

  private async checkRateLimit(providerConfig: EmbeddingProviderConfig): Promise<void> {
    if (!providerConfig.rateLimit) return;

    const key = providerConfig.provider;
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute window

    let limiter = this.rateLimiter.get(key);
    if (!limiter || now > limiter.resetTime) {
      limiter = {
        requests: 0,
        tokens: 0,
        resetTime: now + windowMs
      };
      this.rateLimiter.set(key, limiter);
    }

    if (limiter.requests >= providerConfig.rateLimit.requestsPerMinute) {
      const waitTime = limiter.resetTime - now;
      logger.warn('Rate limit exceeded, waiting', { 
        provider: key, 
        waitTime 
      });
      await this.sleep(waitTime);
      
      // Reset limiter after waiting
      limiter.requests = 0;
      limiter.tokens = 0;
      limiter.resetTime = now + windowMs;
    }

    limiter.requests++;
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Utility methods
  getProviderInfo() {
    return {
      primary: this.primaryProvider.getProviderInfo(),
      fallbacks: this.fallbackProviders.map(p => p.getProviderInfo()),
      config: {
        enableFallback: this.config.enableFallback,
        enableBatching: this.config.enableBatching,
        enableCaching: this.config.enableCaching,
        defaultBatchSize: this.config.defaultBatchSize
      }
    };
  }

  getMetrics() {
    return {
      primary: this.primaryProvider.getMetrics(),
      fallbacks: this.fallbackProviders.map(p => p.getMetrics()),
      cache: {
        size: this.cache.size,
        maxSize: this.config.cacheConfig?.maxSize || 1000,
        hitRate: 0 // TODO: Implement cache hit rate tracking
      }
    };
  }

  clearCache(): void {
    this.cache.clear();
    logger.info('Embedding cache cleared');
  }

  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    providers: Array<{
      name: string;
      status: 'healthy' | 'unhealthy';
      latency?: number;
      error?: string;
    }>;
  }> {
    const providers = [];
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    // Test primary provider
    try {
      const start = Date.now();
      await this.primaryProvider.generateEmbeddings({
        input: 'health check',
        model: this.config.primaryProvider.model
      });
      const latency = Date.now() - start;
      
      providers.push({
        name: this.primaryProvider.getProviderInfo().name,
        status: 'healthy' as const,
        latency
      });
    } catch (error) {
      providers.push({
        name: this.primaryProvider.getProviderInfo().name,
        status: 'unhealthy' as const,
        error: getErrorMessage(error)
      });
      overallStatus = 'degraded';
    }

    // Test fallback providers
    for (const provider of this.fallbackProviders) {
      try {
        const start = Date.now();
        await provider.generateEmbeddings({
          input: 'health check',
          model: provider.getConfig().model
        });
        const latency = Date.now() - start;
        
        providers.push({
          name: provider.getProviderInfo().name,
          status: 'healthy' as const,
          latency
        });
      } catch (error) {
        providers.push({
          name: provider.getProviderInfo().name,
          status: 'unhealthy' as const,
          error: getErrorMessage(error)
        });
      }
    }

    // Determine overall status
    const healthyProviders = providers.filter(p => p.status === 'healthy');
    if (healthyProviders.length === 0) {
      overallStatus = 'unhealthy';
    } else if (healthyProviders.length < providers.length) {
      overallStatus = 'degraded';
    }

    return {
      status: overallStatus,
      providers
    };
  }
}