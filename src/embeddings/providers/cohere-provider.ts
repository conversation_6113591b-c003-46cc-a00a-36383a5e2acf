/**
 * Cohere embedding provider implementation
 * Based on official Cohere TypeScript SDK documentation
 */

import { CohereClient } from 'cohere-ai';
import { 
  EmbeddingProvider, 
  EmbeddingProviderConfig, 
  EmbeddingRequest, 
  EmbeddingResponse 
} from '../embedding-service';
import { logger } from '../../utils/logger';

// Helper function to safely extract error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

export interface CohereEmbeddingConfig extends EmbeddingProviderConfig {
  provider: 'cohere';
  model: 'embed-english-v3.0' | 'embed-multilingual-v3.0' | 'embed-english-light-v3.0' | 'embed-multilingual-light-v3.0';
  inputType?: 'search_document' | 'search_query' | 'classification' | 'clustering';
  embeddingTypes?: ('float' | 'int8' | 'uint8' | 'binary' | 'ubinary')[];
  truncate?: 'NONE' | 'START' | 'END';
}

export class CohereEmbeddingProvider extends EmbeddingProvider {
  private client: CohereClient;
  protected config: CohereEmbeddingConfig;

  constructor(config: CohereEmbeddingConfig) {
    super(config);
    this.config = config;

    if (!config.apiKey) {
      throw new Error('Cohere API key is required');
    }

    this.client = new CohereClient({
      token: config.apiKey
    });

    logger.info('Cohere embedding provider initialized', {
      model: config.model,
      inputType: config.inputType || 'search_document',
      embeddingTypes: config.embeddingTypes || ['float']
    });
  }

  async generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const startTime = Date.now();
    
    try {
      this.validateInput(request.input);
      
      const inputs = this.normalizeInput(request.input);
      const model = request.model || this.config.model;
      
      logger.debug('Generating Cohere embeddings', {
        model,
        inputCount: inputs.length,
        inputType: this.config.inputType || 'search_document'
      });

      const response = await this.retryWithBackoff(async () => {
        return await this.client.v2.embed({
          model,
          texts: inputs,
          inputType: this.config.inputType || 'search_document',
          embeddingTypes: this.config.embeddingTypes || ['float'],
          truncate: this.config.truncate || 'END'
        });
      });

      const latency = Date.now() - startTime;
      
      // Cohere doesn't provide token usage in the same way as OpenAI
      // We'll estimate based on input length
      const estimatedTokens = this.estimateTokenCount(inputs.join(' '));
      this.updateMetrics(true, estimatedTokens, latency);

      logger.debug('Cohere embeddings generated successfully', {
        model: this.config.model,
        embeddingCount: response.embeddings?.float?.length || 0,
        latency
      });

      // Transform Cohere response to our standard format
      const embeddings = response.embeddings?.float || [];
      
      return {
        data: embeddings.map((embedding, index) => ({
          embedding,
          index,
          object: 'embedding' as const
        })),
        model,
        object: 'list' as const,
        usage: {
          prompt_tokens: estimatedTokens,
          total_tokens: estimatedTokens
        }
      };

    } catch (error) {
      const latency = Date.now() - startTime;
      this.updateMetrics(false, 0, latency);

      // Handle Cohere-specific errors
      if (error && typeof error === 'object' && 'statusCode' in error) {
        const cohereError = error as any;
        logger.error('Cohere API error', {
          status: cohereError.statusCode,
          message: getErrorMessage(error),
          body: cohereError.body,
          latency
        });

        // Transform specific Cohere errors to more generic ones
        if (cohereError.statusCode === 401) {
          throw new Error('Invalid Cohere API key');
        } else if (cohereError.statusCode === 429) {
          throw new Error('Cohere rate limit exceeded');
        } else if (cohereError.statusCode === 400) {
          throw new Error(`Invalid request: ${getErrorMessage(error)}`);
        } else if (cohereError.statusCode === 413) {
          throw new Error('Request too large for Cohere API');
        }
      }

      logger.error('Cohere embedding generation failed', {
        error: getErrorMessage(error),
        latency,
        model: request.model || this.config.model
      });

      throw error;
    }
  }

  validateConfig(): boolean {
    try {
      if (!this.config.apiKey) {
        logger.error('Cohere API key is missing');
        return false;
      }

      if (!this.isValidModel(this.config.model)) {
        logger.error('Invalid Cohere model', { model: this.config.model });
        return false;
      }

      if (this.config.inputType && !this.isValidInputType(this.config.inputType)) {
        logger.error('Invalid Cohere input type', { inputType: this.config.inputType });
        return false;
      }

      if (this.config.embeddingTypes) {
        for (const type of this.config.embeddingTypes) {
          if (!this.isValidEmbeddingType(type)) {
            logger.error('Invalid Cohere embedding type', { embeddingType: type });
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      logger.error('Cohere config validation failed', { error: getErrorMessage(error) });
      return false;
    }
  }

  getProviderInfo() {
    return {
      name: 'Cohere',
      version: '1.0.0',
      supportedModels: [
        'embed-english-v3.0',
        'embed-multilingual-v3.0',
        'embed-english-light-v3.0',
        'embed-multilingual-light-v3.0'
      ],
      maxDimensions: this.getMaxDimensions(this.config.model),
      maxTokensPerRequest: this.getMaxTokensPerRequest(this.config.model)
    };
  }

  private isValidModel(model: string): boolean {
    const validModels = [
      'embed-english-v3.0',
      'embed-multilingual-v3.0',
      'embed-english-light-v3.0',
      'embed-multilingual-light-v3.0'
    ];
    return validModels.includes(model);
  }

  private isValidInputType(inputType: string): boolean {
    const validTypes = ['search_document', 'search_query', 'classification', 'clustering'];
    return validTypes.includes(inputType);
  }

  private isValidEmbeddingType(embeddingType: string): boolean {
    const validTypes = ['float', 'int8', 'uint8', 'binary', 'ubinary'];
    return validTypes.includes(embeddingType);
  }

  private getMaxDimensions(model: string): number {
    switch (model) {
      case 'embed-english-v3.0':
      case 'embed-multilingual-v3.0':
        return 1024;
      case 'embed-english-light-v3.0':
      case 'embed-multilingual-light-v3.0':
        return 384;
      default:
        return 1024;
    }
  }

  private getMaxTokensPerRequest(model: string): number {
    // Cohere embedding models generally support up to 512 tokens per input
    return 512;
  }

  // Utility method to estimate token count (rough approximation)
  private estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  // Method to validate input length
  protected validateInput(input: string | string[]): void {
    super.validateInput(input);
    
    const inputs = this.normalizeInput(input);
    const maxTokens = this.getMaxTokensPerRequest(this.config.model);
    
    for (const text of inputs) {
      const estimatedTokens = this.estimateTokenCount(text);
      if (estimatedTokens > maxTokens) {
        throw new Error(
          `Input text too long: ${estimatedTokens} tokens (max: ${maxTokens})`
        );
      }
    }

    // Cohere has a limit on the number of texts per request
    if (inputs.length > 96) {
      throw new Error(`Too many inputs: ${inputs.length} (max: 96)`);
    }
  }

  // Method to get embedding dimensions for a specific model
  getEmbeddingDimensions(model?: string): number {
    const targetModel = model || this.config.model;
    return this.getMaxDimensions(targetModel);
  }

  // Method to test connection
  async testConnection(): Promise<boolean> {
    try {
      const response = await this.client.v2.embed({
        model: this.config.model,
        texts: ['test connection'],
        inputType: this.config.inputType || 'search_document',
        embeddingTypes: ['float']
      });
      
      logger.info('Cohere connection test successful', {
        model: this.config.model,
        dimensions: response.embeddings?.float?.[0]?.length || 0
      });
      
      return true;
    } catch (error) {
      logger.error('Cohere connection test failed', {
        error: getErrorMessage(error)
      });
      return false;
    }
  }

  // Method to get current usage/quota information
  async getUsageInfo(): Promise<{
    tokensUsed: number;
    requestsUsed: number;
    averageLatency: number;
  }> {
    const metrics = this.getMetrics();
    return {
      tokensUsed: metrics.totalTokens,
      requestsUsed: metrics.totalRequests,
      averageLatency: metrics.averageLatency
    };
  }

  // Method to create embeddings for different use cases
  async createDocumentEmbeddings(texts: string[]): Promise<EmbeddingResponse> {
    return this.generateEmbeddings({
      input: texts,
      model: this.config.model
    });
  }

  async createQueryEmbeddings(queries: string[]): Promise<EmbeddingResponse> {
    // Temporarily change input type for queries
    const originalInputType = this.config.inputType;
    this.config.inputType = 'search_query';
    
    try {
      const result = await this.generateEmbeddings({
        input: queries,
        model: this.config.model
      });
      return result;
    } finally {
      // Restore original input type
      if (originalInputType !== undefined) {
        this.config.inputType = originalInputType;
      } else {
        delete (this.config as any).inputType;
      }
    }
  }

  // Method to create embeddings for classification tasks
  async createClassificationEmbeddings(texts: string[]): Promise<EmbeddingResponse> {
    // Temporarily change input type for classification
    const originalInputType = this.config.inputType;
    this.config.inputType = 'classification';
    
    try {
      const result = await this.generateEmbeddings({
        input: texts,
        model: this.config.model
      });
      return result;
    } finally {
      // Restore original input type
      if (originalInputType !== undefined) {
        this.config.inputType = originalInputType;
      } else {
        delete (this.config as any).inputType;
      }
    }
  }

  // Method to create embeddings for clustering tasks
  async createClusteringEmbeddings(texts: string[]): Promise<EmbeddingResponse> {
    // Temporarily change input type for clustering
    const originalInputType = this.config.inputType;
    this.config.inputType = 'clustering';
    
    try {
      const result = await this.generateEmbeddings({
        input: texts,
        model: this.config.model
      });
      return result;
    } finally {
      // Restore original input type
      if (originalInputType !== undefined) {
        this.config.inputType = originalInputType;
      } else {
        delete (this.config as any).inputType;
      }
    }
  }
}