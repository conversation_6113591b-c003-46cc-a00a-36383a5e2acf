/**
 * OpenAI embedding provider implementation
 * Based on official OpenAI Node.js SDK documentation
 */

import OpenAI from 'openai';
import { 
  EmbeddingProvider, 
  EmbeddingProviderConfig, 
  EmbeddingRequest, 
  EmbeddingResponse 
} from '../embedding-service';
import { logger } from '../../utils/logger';

// Helper function to safely extract error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

export interface OpenAIEmbeddingConfig extends EmbeddingProviderConfig {
  provider: 'openai';
  model: 'text-embedding-3-small' | 'text-embedding-3-large' | 'text-embedding-ada-002';
  dimensions?: number; // Available for text-embedding-3-* models
  encoding_format?: 'float' | 'base64';
  organization?: string;
  project?: string;
}

export class OpenAIEmbeddingProvider extends EmbeddingProvider {
  private client: OpenAI;
  protected config: OpenAIEmbeddingConfig;

  constructor(config: OpenAIEmbeddingConfig) {
    super(config);
    this.config = config;

    if (!config.apiKey) {
      throw new Error('OpenAI API key is required');
    }

    this.client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: config.baseURL,
      organization: config.organization,
      project: config.project,
      timeout: config.timeout || 30000,
      maxRetries: 0 // We handle retries ourselves
    });

    logger.info('OpenAI embedding provider initialized', {
      model: config.model,
      dimensions: config.dimensions,
      baseURL: config.baseURL || 'default'
    });
  }

  async generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const startTime = Date.now();
    
    try {
      this.validateInput(request.input);
      
      const inputs = this.normalizeInput(request.input);
      const model = request.model || this.config.model;
      
      logger.debug('Generating OpenAI embeddings', {
        model,
        inputCount: inputs.length,
        dimensions: request.dimensions || this.config.dimensions
      });

      const response = await this.retryWithBackoff(async () => {
        const createParams: any = {
          model,
          input: inputs,
          encoding_format: request.encoding_format || this.config.encoding_format || 'float'
        };

        if (request.dimensions !== undefined || this.config.dimensions !== undefined) {
          createParams.dimensions = request.dimensions || this.config.dimensions;
        }

        if (request.user !== undefined) {
          createParams.user = request.user;
        }

        return await this.client.embeddings.create(createParams);
      });

      const latency = Date.now() - startTime;
      this.updateMetrics(true, response.usage.total_tokens, latency);

      logger.debug('OpenAI embeddings generated successfully', {
        model: response.model,
        embeddingCount: response.data.length,
        totalTokens: response.usage.total_tokens,
        latency
      });

      // Transform OpenAI response to our standard format
      return {
        data: response.data.map((item, index) => ({
          embedding: item.embedding,
          index,
          object: 'embedding' as const
        })),
        model: response.model,
        object: 'list' as const,
        usage: {
          prompt_tokens: response.usage.prompt_tokens,
          total_tokens: response.usage.total_tokens
        }
      };

    } catch (error) {
      const latency = Date.now() - startTime;
      this.updateMetrics(false, 0, latency);

      // Handle OpenAI-specific errors
      if (error instanceof OpenAI.APIError) {
        logger.error('OpenAI API error', {
          status: error.status,
          code: error.code,
          type: error.type,
          message: error.message,
          latency
        });

        // Transform specific OpenAI errors to more generic ones
        if (error.status === 401) {
          throw new Error('Invalid OpenAI API key');
        } else if (error.status === 429) {
          throw new Error('OpenAI rate limit exceeded');
        } else if (error.status === 400) {
          throw new Error(`Invalid request: ${error.message}`);
        } else if (error.status === 413) {
          throw new Error('Request too large for OpenAI API');
        }
      }

      logger.error('OpenAI embedding generation failed', {
        error: getErrorMessage(error),
        latency,
        model: request.model || this.config.model
      });

      throw error;
    }
  }

  validateConfig(): boolean {
    try {
      if (!this.config.apiKey) {
        logger.error('OpenAI API key is missing');
        return false;
      }

      if (!this.isValidModel(this.config.model)) {
        logger.error('Invalid OpenAI model', { model: this.config.model });
        return false;
      }

      // Validate dimensions for newer models
      if (this.config.dimensions && this.config.model === 'text-embedding-ada-002') {
        logger.warn('Dimensions parameter not supported for text-embedding-ada-002');
      }

      if (this.config.dimensions) {
        const maxDimensions = this.getMaxDimensions(this.config.model);
        if (this.config.dimensions > maxDimensions) {
          logger.error('Dimensions exceed maximum for model', {
            requested: this.config.dimensions,
            maximum: maxDimensions,
            model: this.config.model
          });
          return false;
        }
      }

      return true;
    } catch (error) {
      logger.error('OpenAI config validation failed', { error: getErrorMessage(error) });
      return false;
    }
  }

  getProviderInfo() {
    return {
      name: 'OpenAI',
      version: '1.0.0',
      supportedModels: [
        'text-embedding-3-small',
        'text-embedding-3-large', 
        'text-embedding-ada-002'
      ],
      maxDimensions: this.getMaxDimensions(this.config.model),
      maxTokensPerRequest: this.getMaxTokensPerRequest(this.config.model)
    };
  }

  private isValidModel(model: string): boolean {
    const validModels = [
      'text-embedding-3-small',
      'text-embedding-3-large',
      'text-embedding-ada-002'
    ];
    return validModels.includes(model);
  }

  private getMaxDimensions(model: string): number {
    switch (model) {
      case 'text-embedding-3-small':
        return 1536;
      case 'text-embedding-3-large':
        return 3072;
      case 'text-embedding-ada-002':
        return 1536;
      default:
        return 1536;
    }
  }

  private getMaxTokensPerRequest(model: string): number {
    // OpenAI embedding models generally support up to 8192 tokens per request
    return 8192;
  }

  // Utility method to estimate token count (rough approximation)
  private estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  // Method to validate input length
  protected validateInput(input: string | string[]): void {
    super.validateInput(input);
    
    const inputs = this.normalizeInput(input);
    const maxTokens = this.getMaxTokensPerRequest(this.config.model);
    
    for (const text of inputs) {
      const estimatedTokens = this.estimateTokenCount(text);
      if (estimatedTokens > maxTokens) {
        throw new Error(
          `Input text too long: ${estimatedTokens} tokens (max: ${maxTokens})`
        );
      }
    }
  }

  // Method to get embedding dimensions for a specific model
  getEmbeddingDimensions(model?: string): number {
    const targetModel = model || this.config.model;
    
    if (this.config.dimensions && targetModel !== 'text-embedding-ada-002') {
      return this.config.dimensions;
    }
    
    return this.getMaxDimensions(targetModel);
  }

  // Method to test connection
  async testConnection(): Promise<boolean> {
    try {
      const createParams: any = {
        model: this.config.model,
        input: 'test connection'
      };

      if (this.config.dimensions !== undefined) {
        createParams.dimensions = this.config.dimensions;
      }

      const response = await this.client.embeddings.create(createParams);
      
      logger.info('OpenAI connection test successful', {
        model: response.model,
        dimensions: response.data[0]?.embedding.length || 0
      });
      
      return true;
    } catch (error) {
      logger.error('OpenAI connection test failed', {
        error: getErrorMessage(error)
      });
      return false;
    }
  }

  // Method to get current usage/quota information (if available)
  async getUsageInfo(): Promise<{
    tokensUsed: number;
    requestsUsed: number;
    averageLatency: number;
  }> {
    const metrics = this.getMetrics();
    return {
      tokensUsed: metrics.totalTokens,
      requestsUsed: metrics.totalRequests,
      averageLatency: metrics.averageLatency
    };
  }
}