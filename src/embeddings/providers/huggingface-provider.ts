/**
 * Hugging Face embedding provider implementation
 * Based on Transformers.js documentation for local embeddings
 */

// Dynamic import for optional dependency
type Pipeline = any;
import { 
  EmbeddingProvider, 
  EmbeddingProviderConfig, 
  EmbeddingRequest, 
  EmbeddingResponse 
} from '../embedding-service';
import { logger } from '../../utils/logger';

// Helper function to safely extract error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

export interface HuggingFaceEmbeddingConfig extends EmbeddingProviderConfig {
  provider: 'huggingface';
  model: string; // e.g., 'mixedbread-ai/mxbai-embed-xsmall-v1', 'Xenova/all-MiniLM-L6-v2'
  device?: 'cpu' | 'webgpu' | 'wasm';
  dtype?: 'fp32' | 'fp16' | 'q8' | 'q4';
  pooling?: 'mean' | 'cls' | 'max';
  normalize?: boolean;
  cacheDir?: string;
  localModelPath?: string;
  allowRemoteModels?: boolean;
}

export class HuggingFaceEmbeddingProvider extends EmbeddingProvider {
  private pipeline: Pipeline | null = null;
  protected config: HuggingFaceEmbeddingConfig;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  constructor(config: HuggingFaceEmbeddingConfig) {
    super(config);
    this.config = config;

    logger.info('Hugging Face embedding provider initialized', {
      model: config.model,
      device: config.device || 'cpu',
      dtype: config.dtype || 'fp32',
      pooling: config.pooling || 'mean',
      normalize: config.normalize !== false
    });
  }

  private async initializePipeline(): Promise<void> {
    if (this.isInitialized) return;
    
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._initializePipeline();
    return this.initializationPromise;
  }

  private async _initializePipeline(): Promise<void> {
    try {
      logger.info('Initializing Hugging Face pipeline', {
        model: this.config.model,
        device: this.config.device || 'cpu'
      });

      // Configure environment if specified
      try {
        let transformersModule: any;
        try {
          // Use dynamic import with string to avoid TypeScript module resolution
          const moduleName = '@huggingface/transformers';
          transformersModule = await import(moduleName);
        } catch {
          throw new Error('@huggingface/transformers package is not installed. Please install it to use HuggingFace embeddings.');
        }

        if (this.config.cacheDir) {
          transformersModule.env.cacheDir = this.config.cacheDir;
        }

        if (this.config.localModelPath) {
          transformersModule.env.localModelPath = this.config.localModelPath;
        }

        if (this.config.allowRemoteModels !== undefined) {
          transformersModule.env.allowRemoteModels = this.config.allowRemoteModels;
        }
      } catch (error) {
        throw error;
      }

      // Create the feature extraction pipeline
      try {
        let transformersModule: any;
        try {
          // Use dynamic import with string to avoid TypeScript module resolution
          const moduleName = '@huggingface/transformers';
          transformersModule = await import(moduleName);
        } catch {
          throw new Error('@huggingface/transformers package is not installed. Please install it to use HuggingFace embeddings.');
        }

        this.pipeline = await transformersModule.pipeline(
          'feature-extraction',
          this.config.model,
          {
            device: this.config.device || 'cpu',
            dtype: this.config.dtype || 'fp32'
          }
        );
      } catch (importError) {
        throw importError;
      }

      this.isInitialized = true;
      logger.info('Hugging Face pipeline initialized successfully', {
        model: this.config.model
      });

    } catch (error) {
      logger.error('Failed to initialize Hugging Face pipeline', {
        model: this.config.model,
        error: getErrorMessage(error)
      });
      throw new Error(`Failed to initialize Hugging Face model: ${getErrorMessage(error)}`);
    }
  }

  async generateEmbeddings(request: EmbeddingRequest): Promise<EmbeddingResponse> {
    const startTime = Date.now();
    
    try {
      await this.initializePipeline();
      
      if (!this.pipeline) {
        throw new Error('Pipeline not initialized');
      }

      this.validateInput(request.input);
      
      const inputs = this.normalizeInput(request.input);
      const model = request.model || this.config.model;
      
      logger.debug('Generating Hugging Face embeddings', {
        model,
        inputCount: inputs.length,
        device: this.config.device || 'cpu'
      });

      const response = await this.retryWithBackoff(async () => {
        if (!this.pipeline) {
          throw new Error('Pipeline not initialized');
        }

        return await this.pipeline(inputs, {
          pooling: this.config.pooling || 'mean',
          normalize: this.config.normalize !== false
        });
      });

      const latency = Date.now() - startTime;
      
      // Estimate token count for metrics
      const estimatedTokens = this.estimateTokenCount(inputs.join(' '));
      this.updateMetrics(true, estimatedTokens, latency);

      logger.debug('Hugging Face embeddings generated successfully', {
        model,
        embeddingCount: inputs.length,
        latency
      });

      // Transform Hugging Face response to our standard format
      const embeddings = this.transformResponse(response, inputs.length);
      
      return {
        data: embeddings.map((embedding, index) => ({
          embedding,
          index,
          object: 'embedding' as const
        })),
        model,
        object: 'list' as const,
        usage: {
          prompt_tokens: estimatedTokens,
          total_tokens: estimatedTokens
        }
      };

    } catch (error) {
      const latency = Date.now() - startTime;
      this.updateMetrics(false, 0, latency);

      logger.error('Hugging Face embedding generation failed', {
        error: getErrorMessage(error),
        latency,
        model: request.model || this.config.model
      });

      throw error;
    }
  }

  private transformResponse(response: any, inputCount: number): number[][] {
    try {
      // Handle different response formats from Transformers.js
      if (response && typeof response.tolist === 'function') {
        // Tensor response
        return response.tolist();
      } else if (Array.isArray(response)) {
        // Array response
        return response;
      } else if (response && response.data) {
        // Object with data property
        return response.data;
      } else {
        throw new Error('Unexpected response format from Hugging Face model');
      }
    } catch (error) {
      logger.error('Failed to transform Hugging Face response', {
        error: getErrorMessage(error),
        responseType: typeof response
      });
      throw new Error(`Failed to process model response: ${getErrorMessage(error)}`);
    }
  }

  validateConfig(): boolean {
    try {
      if (!this.config.model) {
        logger.error('Hugging Face model name is required');
        return false;
      }

      if (this.config.device && !this.isValidDevice(this.config.device)) {
        logger.error('Invalid device specified', { device: this.config.device });
        return false;
      }

      if (this.config.dtype && !this.isValidDtype(this.config.dtype)) {
        logger.error('Invalid dtype specified', { dtype: this.config.dtype });
        return false;
      }

      if (this.config.pooling && !this.isValidPooling(this.config.pooling)) {
        logger.error('Invalid pooling method specified', { pooling: this.config.pooling });
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Hugging Face config validation failed', { error: getErrorMessage(error) });
      return false;
    }
  }

  getProviderInfo() {
    return {
      name: 'Hugging Face (Local)',
      version: '1.0.0',
      supportedModels: [
        'mixedbread-ai/mxbai-embed-xsmall-v1',
        'Xenova/all-MiniLM-L6-v2',
        'Xenova/all-mpnet-base-v2',
        'sentence-transformers/all-MiniLM-L6-v2',
        'sentence-transformers/all-mpnet-base-v2'
      ],
      maxDimensions: this.getEstimatedDimensions(this.config.model),
      maxTokensPerRequest: this.getMaxTokensPerRequest(this.config.model)
    };
  }

  private isValidDevice(device: string): boolean {
    const validDevices = ['cpu', 'webgpu', 'wasm'];
    return validDevices.includes(device);
  }

  private isValidDtype(dtype: string): boolean {
    const validDtypes = ['fp32', 'fp16', 'q8', 'q4'];
    return validDtypes.includes(dtype);
  }

  private isValidPooling(pooling: string): boolean {
    const validPooling = ['mean', 'cls', 'max'];
    return validPooling.includes(pooling);
  }

  private getEstimatedDimensions(model: string): number {
    // Common embedding dimensions for popular models
    const modelDimensions: Record<string, number> = {
      'mixedbread-ai/mxbai-embed-xsmall-v1': 512,
      'Xenova/all-MiniLM-L6-v2': 384,
      'Xenova/all-mpnet-base-v2': 768,
      'sentence-transformers/all-MiniLM-L6-v2': 384,
      'sentence-transformers/all-mpnet-base-v2': 768
    };

    return modelDimensions[model] || 768; // Default to 768 if unknown
  }

  private getMaxTokensPerRequest(model: string): number {
    // Most sentence transformer models support 512 tokens
    return 512;
  }

  // Utility method to estimate token count
  private estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  // Method to validate input length
  protected validateInput(input: string | string[]): void {
    super.validateInput(input);
    
    const inputs = this.normalizeInput(input);
    const maxTokens = this.getMaxTokensPerRequest(this.config.model);
    
    for (const text of inputs) {
      const estimatedTokens = this.estimateTokenCount(text);
      if (estimatedTokens > maxTokens) {
        throw new Error(
          `Input text too long: ${estimatedTokens} tokens (max: ${maxTokens})`
        );
      }
    }
  }

  // Method to get embedding dimensions
  getEmbeddingDimensions(model?: string): number {
    const targetModel = model || this.config.model;
    return this.getEstimatedDimensions(targetModel);
  }

  // Method to test connection (initialize pipeline)
  async testConnection(): Promise<boolean> {
    try {
      await this.initializePipeline();
      
      if (!this.pipeline) {
        return false;
      }

      // Test with a simple input
      const testResult = await this.pipeline('test connection', {
        pooling: this.config.pooling || 'mean',
        normalize: this.config.normalize !== false
      });

      const embeddings = this.transformResponse(testResult, 1);
      
      logger.info('Hugging Face connection test successful', {
        model: this.config.model,
        dimensions: embeddings[0]?.length || 0
      });
      
      return true;
    } catch (error) {
      logger.error('Hugging Face connection test failed', {
        error: getErrorMessage(error)
      });
      return false;
    }
  }

  // Method to get current usage information
  async getUsageInfo(): Promise<{
    tokensUsed: number;
    requestsUsed: number;
    averageLatency: number;
  }> {
    const metrics = this.getMetrics();
    return {
      tokensUsed: metrics.totalTokens,
      requestsUsed: metrics.totalRequests,
      averageLatency: metrics.averageLatency
    };
  }

  // Method to preload the model (useful for warming up)
  async preloadModel(): Promise<void> {
    await this.initializePipeline();
    logger.info('Hugging Face model preloaded', {
      model: this.config.model
    });
  }

  // Method to clear the model from memory
  async clearModel(): Promise<void> {
    if (this.pipeline) {
      // Note: Transformers.js doesn't have an explicit cleanup method
      // The pipeline will be garbage collected when no longer referenced
      this.pipeline = null;
      this.isInitialized = false;
      this.initializationPromise = null;
      
      logger.info('Hugging Face model cleared from memory', {
        model: this.config.model
      });
    }
  }

  // Method to get model information
  async getModelInfo(): Promise<{
    modelName: string;
    isLoaded: boolean;
    device: string;
    dtype: string;
    estimatedDimensions: number;
  }> {
    return {
      modelName: this.config.model,
      isLoaded: this.isInitialized,
      device: this.config.device || 'cpu',
      dtype: this.config.dtype || 'fp32',
      estimatedDimensions: this.getEstimatedDimensions(this.config.model)
    };
  }
}