/**
 * Factory for creating embedding providers
 */

import { EmbeddingProvider, EmbeddingProviderConfig } from './embedding-service';
import { OpenAIEmbeddingProvider, OpenAIEmbeddingConfig } from './providers/openai-provider';
import { CohereEmbeddingProvider, CohereEmbeddingConfig } from './providers/cohere-provider';
import { HuggingFaceEmbeddingProvider, HuggingFaceEmbeddingConfig } from './providers/huggingface-provider';
import { logger } from '../utils/logger';

// Helper function to safely extract error message
function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return String(error);
}

export class EmbeddingProviderFactory {
  static createProvider(config: EmbeddingProviderConfig): EmbeddingProvider {
    logger.info('Creating embedding provider', {
      provider: config.provider,
      model: config.model
    });

    switch (config.provider) {
      case 'openai':
        return new OpenAIEmbeddingProvider(config as OpenAIEmbeddingConfig);
      
      case 'cohere':
        return new CohereEmbeddingProvider(config as CohereEmbeddingConfig);
      
      case 'huggingface':
        return new HuggingFaceEmbeddingProvider(config as HuggingFaceEmbeddingConfig);
      
      default:
        throw new Error(`Unsupported embedding provider: ${config.provider}`);
    }
  }

  static validateProviderConfig(config: EmbeddingProviderConfig): boolean {
    try {
      const provider = this.createProvider(config);
      return provider.validateConfig();
    } catch (error) {
      logger.error('Provider config validation failed', {
        provider: config.provider,
        error: getErrorMessage(error)
      });
      return false;
    }
  }

  static getSupportedProviders(): string[] {
    return ['openai', 'cohere', 'huggingface'];
  }

  static getProviderRequirements(providerType: string): {
    requiredFields: string[];
    optionalFields: string[];
    description: string;
  } {
    switch (providerType) {
      case 'openai':
        return {
          requiredFields: ['apiKey', 'model'],
          optionalFields: ['dimensions', 'baseURL', 'organization', 'project', 'encoding_format'],
          description: 'OpenAI embedding provider using official OpenAI API'
        };
      
      case 'cohere':
        return {
          requiredFields: ['apiKey', 'model'],
          optionalFields: ['inputType', 'embeddingTypes', 'truncate'],
          description: 'Cohere embedding provider using official Cohere API'
        };
      
      case 'huggingface':
        return {
          requiredFields: ['model'],
          optionalFields: ['device', 'dtype', 'pooling', 'normalize', 'cacheDir', 'localModelPath'],
          description: 'Hugging Face embedding provider using local Transformers.js models'
        };
      
      default:
        throw new Error(`Unknown provider type: ${providerType}`);
    }
  }

  static getRecommendedModels(providerType: string): Array<{
    model: string;
    dimensions: number;
    description: string;
    useCase: string;
  }> {
    switch (providerType) {
      case 'openai':
        return [
          {
            model: 'text-embedding-3-small',
            dimensions: 1536,
            description: 'Latest small embedding model with good performance',
            useCase: 'General purpose, cost-effective'
          },
          {
            model: 'text-embedding-3-large',
            dimensions: 3072,
            description: 'Latest large embedding model with best performance',
            useCase: 'High accuracy requirements'
          },
          {
            model: 'text-embedding-ada-002',
            dimensions: 1536,
            description: 'Previous generation model, still reliable',
            useCase: 'Legacy applications'
          }
        ];
      
      case 'cohere':
        return [
          {
            model: 'embed-english-v3.0',
            dimensions: 1024,
            description: 'Latest English embedding model',
            useCase: 'English text processing'
          },
          {
            model: 'embed-multilingual-v3.0',
            dimensions: 1024,
            description: 'Latest multilingual embedding model',
            useCase: 'Multilingual applications'
          },
          {
            model: 'embed-english-light-v3.0',
            dimensions: 384,
            description: 'Lightweight English model',
            useCase: 'Resource-constrained environments'
          }
        ];
      
      case 'huggingface':
        return [
          {
            model: 'mixedbread-ai/mxbai-embed-xsmall-v1',
            dimensions: 512,
            description: 'Small, efficient embedding model',
            useCase: 'Local deployment, fast inference'
          },
          {
            model: 'Xenova/all-MiniLM-L6-v2',
            dimensions: 384,
            description: 'Popular sentence transformer model',
            useCase: 'General purpose, well-tested'
          },
          {
            model: 'Xenova/all-mpnet-base-v2',
            dimensions: 768,
            description: 'Higher quality sentence transformer',
            useCase: 'Better accuracy, larger model'
          }
        ];
      
      default:
        return [];
    }
  }

  static createDefaultConfig(
    providerType: string,
    apiKey?: string,
    model?: string
  ): EmbeddingProviderConfig {
    const recommendedModels = this.getRecommendedModels(providerType);
    const defaultModel = model || recommendedModels[0]?.model;

    if (!defaultModel) {
      throw new Error(`No default model available for provider: ${providerType}`);
    }

    const baseConfig = {
      provider: providerType as any,
      model: defaultModel,
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000
    };

    switch (providerType) {
      case 'openai':
        return {
          ...baseConfig,
          provider: 'openai',
          apiKey: apiKey || process.env.OPENAI_API_KEY || '',
          dimensions: 1536,
          encoding_format: 'float',
          rateLimit: {
            requestsPerMinute: 3000,
            tokensPerMinute: 1000000
          }
        } as OpenAIEmbeddingConfig;
      
      case 'cohere':
        return {
          ...baseConfig,
          provider: 'cohere',
          apiKey: apiKey || process.env.COHERE_API_KEY || '',
          inputType: 'search_document',
          embeddingTypes: ['float'],
          truncate: 'END',
          rateLimit: {
            requestsPerMinute: 1000,
            tokensPerMinute: 100000
          }
        } as CohereEmbeddingConfig;
      
      case 'huggingface':
        return {
          ...baseConfig,
          provider: 'huggingface',
          device: 'cpu',
          dtype: 'fp32',
          pooling: 'mean',
          normalize: true,
          allowRemoteModels: true
        } as HuggingFaceEmbeddingConfig;
      
      default:
        throw new Error(`Unsupported provider type: ${providerType}`);
    }
  }
}