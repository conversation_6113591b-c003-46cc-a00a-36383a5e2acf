import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import path from 'path';
import { config } from './config/environment';
import { logger } from './utils/logger';
import { apiRouter } from './api';
import { repositories } from './database/init';
import { setupSocketHandlers } from './socket';

// Create Express app
const app = express();
const httpServer = createServer(app);

// Create Socket.IO server
const io = new SocketIOServer(httpServer, {
  cors: {
    origin: config.security.corsOrigin,
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// Setup Socket.IO handlers
setupSocketHandlers(io);

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'blob:'],
      connectSrc: ["'self'", 'ws:', 'wss:']
    }
  }
}));
app.use(cors({ origin: config.security.corsOrigin, credentials: true }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Serve static files from the React app in production
if (config.env === 'production') {
  app.use(express.static(path.join(__dirname, '../frontend/build')));
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: config.env,
    version: '1.0.0'
  });
});

// Basic route
app.get('/api', (req, res) => {
  res.json({
    message: 'DFSA Rulebook RAG System API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      api: '/api'
    }
  });
});

// Mount API router
app.use('/api', apiRouter);

// API documentation route
app.get('/api-docs', (req, res) => {
  res.json({
    openapi: '3.0.0',
    info: {
      title: 'DFSA Rulebook RAG System API',
      version: '1.0.0',
      description: 'API for DFSA Rulebook scraping and RAG querying'
    },
    servers: [
      {
        url: `http://localhost:${config.port}`,
        description: 'Development server'
      }
    ],
    paths: {
      '/api/scraping/start': {
        post: {
          summary: 'Start a new scraping job',
          description: 'Initiates a new scraping job with the specified configuration'
        }
      },
      '/api/query': {
        post: {
          summary: 'Process a RAG query',
          description: 'Processes a natural language query using the RAG pipeline'
        }
      },
      '/api/config': {
        get: {
          summary: 'Get all system configurations',
          description: 'Retrieves all system configuration values'
        }
      }
    }
  });
});

// Serve React app for any other routes in production
if (config.env === 'production') {
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/build/index.html'));
  });
}

// Error handling middleware
app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: config.env === 'development' ? error.message : 'Something went wrong'
  });
});

// Database repositories are initialized when imported
logger.info('Database repositories initialized successfully');

// Start server
const server = httpServer.listen(config.port, () => {
  logger.info(`DFSA Rulebook RAG System started on port ${config.port}`);
  logger.info(`Environment: ${config.env}`);
  logger.info(`Health check: http://localhost:${config.port}/health`);
  logger.info(`API documentation: http://localhost:${config.port}/api-docs`);
  logger.info(`Socket.IO server running`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Process terminated');
    process.exit(0);
  });
});

export default app;