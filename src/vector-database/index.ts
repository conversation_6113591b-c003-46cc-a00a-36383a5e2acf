/**
 * Vector Database Module
 * 
 * Unified vector database integration supporting multiple providers
 * with advanced features like health monitoring, fallback mechanisms,
 * and performance optimization.
 */

// Core interfaces and types
export { VectorStore } from './vector-store';
export { VectorRecord } from './vector-store';
export { VectorSearchQuery } from './vector-store';
export { VectorSearchResponse } from './vector-store';
export { VectorSearchResult } from './vector-store';
export { VectorStoreConfig } from './vector-store';
export { VectorStoreStats } from './vector-store';
export { VectorStoreHealth } from './vector-store';
export { VectorStoreError } from './vector-store';
export { VectorStoreConnectionError } from './vector-store';
export { VectorStoreIndexError } from './vector-store';
export { VectorStoreQueryError } from './vector-store';

// Provider implementations
export { PineconeVectorStore } from './providers/pinecone-store';
export { PineconeConfig } from './providers/pinecone-store';

export { WeaviateVectorStore } from './providers/weaviate-store';
export { WeaviateConfig } from './providers/weaviate-store';

// Factory and service
export { VectorStoreFactory } from './vector-store-factory';

export { VectorStoreService } from './vector-store-service';
export { VectorStoreServiceConfig } from './vector-store-service';
export { VectorStoreMetrics } from './vector-store-service';

// Utility functions for common vector operations
export const VectorUtils = {
  /**
   * Calculate cosine similarity between two vectors
   */
  cosineSimilarity: function(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < a.length; i++) {
      const aVal = a[i];
      const bVal = b[i];
      if (aVal !== undefined && bVal !== undefined) {
        dotProduct += aVal * bVal;
        normA += aVal * aVal;
        normB += bVal * bVal;
      }
    }

    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (normA * normB);
  },

  /**
   * Calculate Euclidean distance between two vectors
   */
  euclideanDistance: function(a: number[], b: number[]): number {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length');
    }

    let sum = 0;
    for (let i = 0; i < a.length; i++) {
      const aVal = a[i];
      const bVal = b[i];
      if (aVal !== undefined && bVal !== undefined) {
        const diff = aVal - bVal;
        sum += diff * diff;
      }
    }

    return Math.sqrt(sum);
  },

  /**
   * Normalize a vector to unit length
   */
  normalizeVector: function(vector: number[]): number[] {
    const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    if (norm === 0) return vector;
    return vector.map(val => val / norm);
  },

  /**
   * Convert similarity score to distance
   */
  similarityToDistance: function(similarity: number, metric: 'cosine' | 'euclidean' | 'dotproduct'): number {
    switch (metric) {
      case 'cosine':
        return 1 - similarity;
      case 'euclidean':
        // For normalized vectors, euclidean distance = sqrt(2 * (1 - cosine_similarity))
        return Math.sqrt(2 * (1 - similarity));
      case 'dotproduct':
        return -similarity; // Higher dot product = lower distance
      default:
        return 1 - similarity;
    }
  },

  /**
   * Convert distance to similarity score
   */
  distanceToSimilarity: function(distance: number, metric: 'cosine' | 'euclidean' | 'dotproduct'): number {
    switch (metric) {
      case 'cosine':
        return 1 - distance;
      case 'euclidean':
        // For normalized vectors
        return 1 - (distance * distance) / 2;
      case 'dotproduct':
        return -distance;
      default:
        return 1 - distance;
    }
  },

  /**
   * Validate vector dimensions
   */
  validateVector: function(vector: number[], expectedDimension: number): boolean {
    return Array.isArray(vector) && 
           vector.length === expectedDimension && 
           vector.every(val => typeof val === 'number' && !isNaN(val));
  },

  /**
   * Generate random vector for testing
   */
  generateRandomVector: function(dimension: number, normalize: boolean = false): number[] {
    const vector = Array.from({ length: dimension }, () => Math.random() * 2 - 1);
    return normalize ? this.normalizeVector(vector) : vector;
  },

  /**
   * Batch vectors into smaller chunks for processing
   */
  batchVectors: function<T>(vectors: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < vectors.length; i += batchSize) {
      batches.push(vectors.slice(i, i + batchSize));
    }
    return batches;
  }
};

// Default configurations for quick setup
export const DefaultVectorConfigs = {
  /**
   * Pinecone configuration for semantic search
   */
  pineconeSemanticSearch: {
    provider: 'pinecone' as const,
    dimension: 1536,
    metric: 'cosine' as const,
    pinecone: {
      cloud: 'aws' as const,
      region: 'us-west-2'
    },
    timeout: 30000,
    retryAttempts: 3
  },

  /**
   * Pinecone configuration for high performance
   */
  pineconeHighPerformance: {
    provider: 'pinecone' as const,
    dimension: 768,
    metric: 'cosine' as const,
    pinecone: {
      cloud: 'gcp' as const,
      region: 'us-central1',
      podType: 'p2.x1',
      replicas: 2
    },
    timeout: 15000,
    retryAttempts: 5
  },

  /**
   * Weaviate configuration for semantic search
   */
  weaviateSemanticSearch: {
    provider: 'weaviate' as const,
    dimension: 1536,
    metric: 'cosine' as const,
    weaviate: {
      scheme: 'https' as const,
      host: 'localhost:8080',
      className: 'Document',
      vectorizer: 'text2vec-openai'
    },
    timeout: 30000,
    retryAttempts: 3
  },

  /**
   * Weaviate configuration for multimodal search
   */
  weaviateMultimodal: {
    provider: 'weaviate' as const,
    dimension: 512,
    metric: 'cosine' as const,
    weaviate: {
      scheme: 'https' as const,
      host: 'localhost:8080',
      className: 'MultimodalObject',
      vectorizer: 'multi2vec-clip'
    },
    timeout: 30000,
    retryAttempts: 3
  }
};

// Service configuration presets
export const DefaultServiceConfigs = {
  /**
   * Production configuration with health monitoring and fallback
   */
  production: {
    healthCheck: {
      enabled: true,
      interval: 30000, // 30 seconds
      timeout: 5000, // 5 seconds
      retryAttempts: 3
    },
    performance: {
      enableMetrics: true,
      metricsRetention: 3600000 // 1 hour
    },
    retry: {
      maxAttempts: 5,
      baseDelay: 1000, // 1 second
      maxDelay: 30000, // 30 seconds
      backoffMultiplier: 2
    }
  },

  /**
   * Development configuration with relaxed settings
   */
  development: {
    healthCheck: {
      enabled: true,
      interval: 60000, // 1 minute
      timeout: 10000, // 10 seconds
      retryAttempts: 2
    },
    performance: {
      enableMetrics: true,
      metricsRetention: 1800000 // 30 minutes
    },
    retry: {
      maxAttempts: 3,
      baseDelay: 500, // 0.5 seconds
      maxDelay: 5000, // 5 seconds
      backoffMultiplier: 1.5
    }
  },

  /**
   * Testing configuration with minimal overhead
   */
  testing: {
    healthCheck: {
      enabled: false,
      interval: 300000, // 5 minutes
      timeout: 2000, // 2 seconds
      retryAttempts: 1
    },
    performance: {
      enableMetrics: false,
      metricsRetention: 300000 // 5 minutes
    },
    retry: {
      maxAttempts: 1,
      baseDelay: 100, // 0.1 seconds
      maxDelay: 1000, // 1 second
      backoffMultiplier: 1
    }
  }
};