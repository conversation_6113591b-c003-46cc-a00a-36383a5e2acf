/**
 * Vector Store Interface and Types
 * 
 * Provides a unified interface for vector database operations across different providers
 * (Pinecone, Weaviate, etc.) with comprehensive metadata support and search capabilities.
 */

export interface VectorRecord {
  id: string;
  vector: number[];
  metadata?: Record<string, any>;
  sparseVector?: {
    indices: number[];
    values: number[];
  };
}

export interface VectorSearchQuery {
  vector?: number[];
  id?: string;
  topK: number;
  filter?: Record<string, any>;
  includeMetadata?: boolean;
  includeValues?: boolean;
  namespace?: string;
  sparseVector?: {
    indices: number[];
    values: number[];
  };
}

export interface VectorSearchResult {
  id: string;
  score: number;
  vector?: number[];
  metadata?: Record<string, any>;
  sparseVector?: {
    indices: number[];
    values: number[];
  };
}

export interface VectorSearchResponse {
  matches: VectorSearchResult[];
  namespace?: string;
  usage?: {
    readUnits?: number;
    writeUnits?: number;
  };
}

export interface VectorStoreStats {
  totalRecords: number;
  dimension: number;
  namespaces?: Record<string, { recordCount: number }>;
  indexFullness?: number;
}

export interface VectorStoreConfig {
  provider: 'pinecone' | 'weaviate';
  apiKey?: string;
  environment?: string;
  indexName: string;
  dimension: number;
  metric?: 'cosine' | 'euclidean' | 'dotproduct';
  namespace?: string;
  timeout?: number;
  retryAttempts?: number;
  // Pinecone specific
  pinecone?: {
    cloud?: 'aws' | 'gcp' | 'azure';
    region?: string;
    podType?: string;
    replicas?: number;
    shards?: number;
  };
  // Weaviate specific
  weaviate?: {
    scheme?: 'http' | 'https';
    host?: string;
    className?: string;
    vectorizer?: string;
  };
}

export interface VectorStoreHealth {
  isHealthy: boolean;
  provider: string;
  indexName: string;
  status: 'ready' | 'initializing' | 'error';
  lastChecked: Date;
  error?: string;
  responseTime?: number;
}

/**
 * Abstract base class for vector store implementations
 */
export abstract class VectorStore {
  protected config: VectorStoreConfig;
  protected isInitialized: boolean = false;

  constructor(config: VectorStoreConfig) {
    this.config = config;
  }

  /**
   * Initialize the vector store connection
   */
  abstract initialize(): Promise<void>;

  /**
   * Check if the vector store is ready for operations
   */
  abstract isReady(): Promise<boolean>;

  /**
   * Get health status of the vector store
   */
  abstract getHealth(): Promise<VectorStoreHealth>;

  /**
   * Upsert vectors into the store
   */
  abstract upsert(vectors: VectorRecord[], namespace?: string): Promise<void>;

  /**
   * Search for similar vectors
   */
  abstract search(query: VectorSearchQuery): Promise<VectorSearchResponse>;

  /**
   * Fetch vectors by IDs
   */
  abstract fetch(ids: string[], namespace?: string): Promise<Record<string, VectorRecord>>;

  /**
   * Delete vectors by IDs
   */
  abstract delete(ids: string[], namespace?: string): Promise<void>;

  /**
   * Delete all vectors in a namespace
   */
  abstract deleteAll(namespace?: string): Promise<void>;

  /**
   * Get statistics about the vector store
   */
  abstract getStats(): Promise<VectorStoreStats>;

  /**
   * Create or update the index/collection
   */
  abstract createIndex(): Promise<void>;

  /**
   * Delete the index/collection
   */
  abstract deleteIndex(): Promise<void>;

  /**
   * List all available indexes/collections
   */
  abstract listIndexes(): Promise<string[]>;

  /**
   * Close the connection and cleanup resources
   */
  abstract close(): Promise<void>;

  /**
   * Get the current configuration
   */
  getConfig(): VectorStoreConfig {
    return { ...this.config };
  }

  /**
   * Check if the store is initialized
   */
  getInitializationStatus(): boolean {
    return this.isInitialized;
  }
}

/**
 * Vector Store Error Classes
 */
export class VectorStoreError extends Error {
  constructor(message: string, public provider: string, public operation: string) {
    super(message);
    this.name = 'VectorStoreError';
  }
}

export class VectorStoreConnectionError extends VectorStoreError {
  constructor(message: string, provider: string) {
    super(message, provider, 'connection');
    this.name = 'VectorStoreConnectionError';
  }
}

export class VectorStoreIndexError extends VectorStoreError {
  constructor(message: string, provider: string) {
    super(message, provider, 'index');
    this.name = 'VectorStoreIndexError';
  }
}

export class VectorStoreQueryError extends VectorStoreError {
  constructor(message: string, provider: string) {
    super(message, provider, 'query');
    this.name = 'VectorStoreQueryError';
  }
}