/**
 * Vector Store Service
 * 
 * High-level service that provides vector database operations with advanced features
 * like connection pooling, health monitoring, automatic retries, and performance metrics.
 */

import { EventEmitter } from 'events';
import { VectorStore, VectorStoreConfig, VectorRecord, VectorSearchQuery, VectorSearchResponse, VectorStoreHealth } from './vector-store';
import { VectorStoreFactory } from './vector-store-factory';

export interface VectorStoreServiceConfig {
  primary: VectorStoreConfig;
  fallback?: VectorStoreConfig;
  healthCheck: {
    enabled: boolean;
    interval: number; // milliseconds
    timeout: number; // milliseconds
    retryAttempts: number;
  };
  performance: {
    enableMetrics: boolean;
    metricsRetention: number; // milliseconds
  };
  retry: {
    maxAttempts: number;
    baseDelay: number; // milliseconds
    maxDelay: number; // milliseconds
    backoffMultiplier: number;
  };
}

export interface VectorStoreMetrics {
  operations: {
    total: number;
    successful: number;
    failed: number;
    averageLatency: number;
  };
  health: {
    isHealthy: boolean;
    lastHealthCheck: Date;
    consecutiveFailures: number;
  };
  performance: {
    searchLatency: number[];
    upsertLatency: number[];
    fetchLatency: number[];
  };
}

export class VectorStoreService extends EventEmitter {
  private primaryStore!: VectorStore; // Initialized in initialize() method
  private fallbackStore?: VectorStore;
  private config: VectorStoreServiceConfig;
  private metrics!: VectorStoreMetrics; // Initialized in initializeMetrics() method
  private healthCheckInterval?: NodeJS.Timeout;
  private isInitialized: boolean = false;

  constructor(config: VectorStoreServiceConfig) {
    super();
    this.config = config;
    this.initializeMetrics();
  }

  /**
   * Initialize the vector store service
   */
  async initialize(): Promise<void> {
    try {
      // Initialize primary store
      this.primaryStore = VectorStoreFactory.create(this.config.primary);
      await this.primaryStore.initialize();

      // Initialize fallback store if configured
      if (this.config.fallback) {
        this.fallbackStore = VectorStoreFactory.create(this.config.fallback);
        await this.fallbackStore.initialize();
      }

      // Start health monitoring
      if (this.config.healthCheck.enabled) {
        this.startHealthMonitoring();
      }

      this.isInitialized = true;
      this.emit('initialized');
    } catch (error: any) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Upsert vectors with retry logic and fallback
   */
  async upsert(vectors: VectorRecord[], namespace?: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      await this.executeWithRetry(async () => {
        await this.primaryStore.upsert(vectors, namespace);
      });

      this.recordMetrics('upsert', Date.now() - startTime, true);
      this.emit('upsert', { count: vectors.length, namespace });
    } catch (error: any) {
      this.recordMetrics('upsert', Date.now() - startTime, false);
      
      // Try fallback if available
      if (this.fallbackStore) {
        try {
          await this.fallbackStore.upsert(vectors, namespace);
          this.emit('fallback-used', { operation: 'upsert', error: error.message });
          return;
        } catch (fallbackError) {
          this.emit('fallback-failed', { operation: 'upsert', error: fallbackError });
        }
      }

      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Search vectors with retry logic and fallback
   */
  async search(query: VectorSearchQuery): Promise<VectorSearchResponse> {
    const startTime = Date.now();
    
    try {
      const result = await this.executeWithRetry(async () => {
        return await this.primaryStore.search(query);
      });

      this.recordMetrics('search', Date.now() - startTime, true);
      this.emit('search', { query, resultCount: result.matches.length });
      
      return result;
    } catch (error: any) {
      this.recordMetrics('search', Date.now() - startTime, false);
      
      // Try fallback if available
      if (this.fallbackStore) {
        try {
          const result = await this.fallbackStore.search(query);
          this.emit('fallback-used', { operation: 'search', error: error.message });
          return result;
        } catch (fallbackError) {
          this.emit('fallback-failed', { operation: 'search', error: fallbackError });
        }
      }

      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Fetch vectors by IDs with retry logic and fallback
   */
  async fetch(ids: string[], namespace?: string): Promise<Record<string, VectorRecord>> {
    const startTime = Date.now();
    
    try {
      const result = await this.executeWithRetry(async () => {
        return await this.primaryStore.fetch(ids, namespace);
      });

      this.recordMetrics('fetch', Date.now() - startTime, true);
      this.emit('fetch', { ids, namespace, resultCount: Object.keys(result).length });
      
      return result;
    } catch (error: any) {
      this.recordMetrics('fetch', Date.now() - startTime, false);
      
      // Try fallback if available
      if (this.fallbackStore) {
        try {
          const result = await this.fallbackStore.fetch(ids, namespace);
          this.emit('fallback-used', { operation: 'fetch', error: error.message });
          return result;
        } catch (fallbackError) {
          this.emit('fallback-failed', { operation: 'fetch', error: fallbackError });
        }
      }

      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Delete vectors with retry logic
   */
  async delete(ids: string[], namespace?: string): Promise<void> {
    const startTime = Date.now();
    
    try {
      await this.executeWithRetry(async () => {
        await this.primaryStore.delete(ids, namespace);
      });

      this.recordMetrics('delete', Date.now() - startTime, true);
      this.emit('delete', { ids, namespace });
    } catch (error: any) {
      this.recordMetrics('delete', Date.now() - startTime, false);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Get health status of all stores
   */
  async getHealth(): Promise<{ primary: VectorStoreHealth; fallback?: VectorStoreHealth }> {
    const health: any = {
      primary: await this.primaryStore.getHealth()
    };

    if (this.fallbackStore) {
      health.fallback = await this.fallbackStore.getHealth();
    }

    return health;
  }

  /**
   * Get service metrics
   */
  getMetrics(): VectorStoreMetrics {
    return { ...this.metrics };
  }

  /**
   * Get service status
   */
  getStatus(): {
    isInitialized: boolean;
    isHealthy: boolean;
    primaryProvider: string;
    fallbackProvider?: string;
    metrics: VectorStoreMetrics;
  } {
    const result: any = {
      isInitialized: this.isInitialized,
      isHealthy: this.metrics.health.isHealthy,
      primaryProvider: this.config.primary.provider,
      metrics: this.metrics
    };

    if (this.config.fallback?.provider !== undefined) {
      result.fallbackProvider = this.config.fallback.provider;
    }

    return result;
  }

  /**
   * Close all connections and cleanup
   */
  async close(): Promise<void> {
    try {
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
      }

      if (this.primaryStore) {
        await this.primaryStore.close();
      }

      if (this.fallbackStore) {
        await this.fallbackStore.close();
      }

      this.isInitialized = false;
      this.emit('closed');
    } catch (error: any) {
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Execute operation with retry logic
   */
  private async executeWithRetry<T>(operation: () => Promise<T>): Promise<T> {
    let lastError: Error;
    let delay = this.config.retry.baseDelay;

    for (let attempt = 1; attempt <= this.config.retry.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error: any) {
        lastError = error;
        
        if (attempt === this.config.retry.maxAttempts) {
          break;
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // Exponential backoff
        delay = Math.min(
          delay * this.config.retry.backoffMultiplier,
          this.config.retry.maxDelay
        );

        this.emit('retry', { attempt, error: error.message, nextDelay: delay });
      }
    }

    throw lastError!;
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const health = await this.primaryStore.getHealth();
        
        if (health.isHealthy) {
          this.metrics.health.isHealthy = true;
          this.metrics.health.consecutiveFailures = 0;
        } else {
          this.metrics.health.consecutiveFailures++;
          
          if (this.metrics.health.consecutiveFailures >= this.config.healthCheck.retryAttempts) {
            this.metrics.health.isHealthy = false;
            this.emit('unhealthy', health);
          }
        }

        this.metrics.health.lastHealthCheck = new Date();
        this.emit('health-check', health);
      } catch (error: any) {
        this.metrics.health.consecutiveFailures++;
        this.metrics.health.isHealthy = false;
        this.emit('health-check-error', error);
      }
    }, this.config.healthCheck.interval);
  }

  /**
   * Initialize metrics tracking
   */
  private initializeMetrics(): void {
    this.metrics = {
      operations: {
        total: 0,
        successful: 0,
        failed: 0,
        averageLatency: 0
      },
      health: {
        isHealthy: true,
        lastHealthCheck: new Date(),
        consecutiveFailures: 0
      },
      performance: {
        searchLatency: [],
        upsertLatency: [],
        fetchLatency: []
      }
    };
  }

  /**
   * Record operation metrics
   */
  private recordMetrics(operation: string, latency: number, success: boolean): void {
    if (!this.config.performance.enableMetrics) {
      return;
    }

    this.metrics.operations.total++;
    
    if (success) {
      this.metrics.operations.successful++;
    } else {
      this.metrics.operations.failed++;
    }

    // Update average latency
    const totalLatency = this.metrics.operations.averageLatency * (this.metrics.operations.total - 1) + latency;
    this.metrics.operations.averageLatency = totalLatency / this.metrics.operations.total;

    // Record operation-specific latency
    const latencyArray = this.metrics.performance[`${operation}Latency` as keyof typeof this.metrics.performance] as number[];
    if (latencyArray) {
      latencyArray.push(latency);
      
      // Keep only recent metrics
      const maxEntries = Math.floor(this.config.performance.metricsRetention / 1000);
      if (latencyArray.length > maxEntries) {
        latencyArray.splice(0, latencyArray.length - maxEntries);
      }
    }
  }

  /**
   * Get default service configuration
   */
  static getDefaultConfig(): Omit<VectorStoreServiceConfig, 'primary'> {
    return {
      healthCheck: {
        enabled: true,
        interval: 30000, // 30 seconds
        timeout: 5000, // 5 seconds
        retryAttempts: 3
      },
      performance: {
        enableMetrics: true,
        metricsRetention: 3600000 // 1 hour
      },
      retry: {
        maxAttempts: 3,
        baseDelay: 1000, // 1 second
        maxDelay: 10000, // 10 seconds
        backoffMultiplier: 2
      }
    };
  }
}