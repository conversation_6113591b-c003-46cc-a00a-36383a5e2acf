/**
 * Weaviate Vector Store Implementation
 * 
 * Implements the VectorStore interface using <PERSON><PERSON><PERSON> as the backend.
 * Based on official Weaviate TypeScript client documentation and best practices.
 */

import weaviate, { type WeaviateClient, ApiKey } from 'weaviate-client';
import {
  VectorStore,
  VectorRecord,
  VectorSearchQuery,
  VectorSearchResponse,
  VectorSearchResult,
  VectorStoreConfig,
  VectorStoreStats,
  VectorStoreHealth,
  VectorStoreError,
  VectorStoreConnectionError,
  VectorStoreIndexError,
  VectorStoreQueryError
} from '../vector-store';

export interface WeaviateConfig extends VectorStoreConfig {
  provider: 'weaviate';
  weaviate: {
    scheme: 'http' | 'https';
    host: string;
    className: string;
    vectorizer?: string;
    generative?: string;
  };
  apiKey?: string;
}

export class WeaviateVectorStore extends VectorStore {
  private client!: WeaviateClient; // Use definite assignment assertion since it's initialized in initialize()
  private collection: any;
  protected config: WeaviateConfig;

  constructor(config: WeaviateConfig) {
    super(config);
    this.config = config;
    
    if (!config.weaviate.host) {
      throw new VectorStoreError('Weaviate host is required', 'weaviate', 'initialization');
    }
  }

  async initialize(): Promise<void> {
    try {
      // Initialize Weaviate client
      const clientConfig: any = {
        scheme: this.config.weaviate.scheme || 'http',
        host: this.config.weaviate.host
      };

      if (this.config.apiKey) {
        clientConfig.authCredentials = new ApiKey(this.config.apiKey);
      }

      const connectionOptions: any = {};
      if (this.config.apiKey) {
        connectionOptions.authCredentials = new ApiKey(this.config.apiKey);
      }
      if (clientConfig.headers) {
        connectionOptions.headers = clientConfig.headers;
      }

      this.client = await weaviate.connectToWeaviateCloud(
        this.config.weaviate?.host || 'localhost:8080',
        connectionOptions
      );

      // Get collection reference
      this.collection = this.client.collections.get(this.config.weaviate.className);

      // Verify connection
      try {
        const isLive = await this.client.isLive();
        if (!isLive) {
          throw new Error('Weaviate instance is not responding');
        }
      } catch (error) {
        throw new Error(`Failed to connect to Weaviate: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
      
      this.isInitialized = true;
    } catch (error: any) {
      throw new VectorStoreConnectionError(
        `Failed to initialize Weaviate: ${error.message}`,
        'weaviate'
      );
    }
  }

  async isReady(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        return false;
      }

      const isLive = await this.client.isLive();
      const isReady = await this.client.isReady();

      return isLive && isReady;
    } catch (error) {
      return false;
    }
  }

  async getHealth(): Promise<VectorStoreHealth> {
    const startTime = Date.now();
    
    try {
      const isLive = await this.client.isLive();
      const isReady = await this.client.isReady();
      const responseTime = Date.now() - startTime;
      
      return {
        isHealthy: isLive && isReady,
        provider: 'weaviate',
        indexName: this.config.weaviate.className,
        status: (isLive && isReady) ? 'ready' : 'initializing',
        lastChecked: new Date(),
        responseTime
      };
    } catch (error: any) {
      return {
        isHealthy: false,
        provider: 'weaviate',
        indexName: this.config.weaviate.className,
        status: 'error',
        lastChecked: new Date(),
        error: error.message,
        responseTime: Date.now() - startTime
      };
    }
  }

  async upsert(vectors: VectorRecord[], namespace?: string): Promise<void> {
    try {
      const targetCollection = namespace ? 
        this.client.collections.get(`${this.config.weaviate.className}_${namespace}`) : 
        this.collection;

      // Batch insert objects with vectors
      const objects = vectors.map(vector => ({
        id: vector.id,
        properties: vector.metadata || {},
        vector: vector.vector
      }));

      await targetCollection.data.insertMany(objects);
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to upsert vectors: ${error.message}`,
        'weaviate'
      );
    }
  }

  async search(query: VectorSearchQuery): Promise<VectorSearchResponse> {
    try {
      const targetCollection = query.namespace ? 
        this.client.collections.get(`${this.config.weaviate.className}_${query.namespace}`) : 
        this.collection;

      let searchQuery;

      if (query.vector) {
        // Vector similarity search
        searchQuery = targetCollection.query.nearVector(
          query.vector,
          {
            limit: query.topK,
            returnMetadata: ['distance'],
            returnProperties: Object.keys(query.filter || {}),
            where: query.filter ? this.buildWhereFilter(query.filter) : undefined
          }
        );
      } else if (query.id) {
        // Search by existing object ID
        searchQuery = targetCollection.query.nearObject(
          query.id,
          {
            limit: query.topK,
            returnMetadata: ['distance'],
            returnProperties: Object.keys(query.filter || {}),
            where: query.filter ? this.buildWhereFilter(query.filter) : undefined
          }
        );
      } else {
        throw new Error('Either vector or id must be provided for search');
      }

      const response = await searchQuery;

      const matches: VectorSearchResult[] = response.objects.map((obj: any) => ({
        id: obj.uuid,
        score: 1 - (obj.metadata?.distance || 0), // Convert distance to similarity score
        vector: query.includeValues ? obj.vector : undefined,
        metadata: obj.properties
      }));

      return {
        matches,
        namespace: query.namespace || ''
      };
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to search vectors: ${error.message}`,
        'weaviate'
      );
    }
  }

  async fetch(ids: string[], namespace?: string): Promise<Record<string, VectorRecord>> {
    try {
      const targetCollection = namespace ? 
        this.client.collections.get(`${this.config.weaviate.className}_${namespace}`) : 
        this.collection;

      const records: Record<string, VectorRecord> = {};

      // Fetch objects by IDs
      for (const id of ids) {
        try {
          const obj = await targetCollection.data.getById(id, {
            returnVector: true
          });

          if (obj) {
            records[id] = {
              id: obj.uuid,
              vector: obj.vector || [],
              metadata: obj.properties
            };
          }
        } catch (error) {
          // Object not found, continue with others
          continue;
        }
      }

      return records;
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to fetch vectors: ${error.message}`,
        'weaviate'
      );
    }
  }

  async delete(ids: string[], namespace?: string): Promise<void> {
    try {
      const targetCollection = namespace ? 
        this.client.collections.get(`${this.config.weaviate.className}_${namespace}`) : 
        this.collection;

      // Delete objects by IDs
      for (const id of ids) {
        try {
          await targetCollection.data.deleteById(id);
        } catch (error) {
          // Object not found, continue with others
          continue;
        }
      }
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to delete vectors: ${error.message}`,
        'weaviate'
      );
    }
  }

  async deleteAll(namespace?: string): Promise<void> {
    try {
      const className = namespace ? 
        `${this.config.weaviate.className}_${namespace}` : 
        this.config.weaviate.className;

      // Delete all objects in the collection
      await this.client.collections.delete(className);
      
      // Recreate the collection
      await this.createCollection(className);
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to delete all vectors: ${error.message}`,
        'weaviate'
      );
    }
  }

  async getStats(): Promise<VectorStoreStats> {
    try {
      // Get collection statistics
      const response = await this.collection.aggregate.overAll();
      
      return {
        totalRecords: response.totalCount || 0,
        dimension: this.config.dimension,
        namespaces: {} // Weaviate doesn't have explicit namespaces
      };
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to get stats: ${error.message}`,
        'weaviate'
      );
    }
  }

  async createIndex(): Promise<void> {
    try {
      await this.createCollection(this.config.weaviate.className);
    } catch (error: any) {
      throw new VectorStoreIndexError(
        `Failed to create collection: ${error.message}`,
        'weaviate'
      );
    }
  }

  async deleteIndex(): Promise<void> {
    try {
      await this.client.collections.delete(this.config.weaviate.className);
    } catch (error: any) {
      throw new VectorStoreIndexError(
        `Failed to delete collection: ${error.message}`,
        'weaviate'
      );
    }
  }

  async listIndexes(): Promise<string[]> {
    try {
      const collections = await this.client.collections.listAll();
      return collections.map(collection => collection.name);
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to list collections: ${error.message}`,
        'weaviate'
      );
    }
  }

  async close(): Promise<void> {
    try {
      if (this.client) {
        this.client.close();
      }
      this.isInitialized = false;
    } catch (error: any) {
      // Ignore cleanup errors
    }
  }

  /**
   * Create a Weaviate collection with proper configuration
   */
  private async createCollection(className: string): Promise<void> {
    const collectionConfig: any = {
      name: className,
      vectorizers: {
        default: {
          vectorizer: this.config.weaviate.vectorizer || 'none',
          vectorIndexConfig: {
            distance: this.config.metric || 'cosine'
          }
        }
      }
    };

    if (this.config.weaviate.generative) {
      collectionConfig.generative = {
        generative: this.config.weaviate.generative
      };
    }

    await this.client.collections.create(collectionConfig);
  }

  /**
   * Build Weaviate where filter from generic filter object
   */
  private buildWhereFilter(filter: Record<string, any>): any {
    const whereConditions: any[] = [];

    for (const [key, value] of Object.entries(filter)) {
      if (typeof value === 'string') {
        whereConditions.push({
          path: [key],
          operator: 'Equal',
          valueText: value
        });
      } else if (typeof value === 'number') {
        whereConditions.push({
          path: [key],
          operator: 'Equal',
          valueNumber: value
        });
      } else if (typeof value === 'boolean') {
        whereConditions.push({
          path: [key],
          operator: 'Equal',
          valueBoolean: value
        });
      } else if (typeof value === 'object' && value !== null) {
        // Handle complex filters like { $gt: 5 }, { $in: ['a', 'b'] }
        for (const [op, val] of Object.entries(value)) {
          switch (op) {
            case '$gt':
              whereConditions.push({
                path: [key],
                operator: 'GreaterThan',
                valueNumber: val
              });
              break;
            case '$gte':
              whereConditions.push({
                path: [key],
                operator: 'GreaterThanEqual',
                valueNumber: val
              });
              break;
            case '$lt':
              whereConditions.push({
                path: [key],
                operator: 'LessThan',
                valueNumber: val
              });
              break;
            case '$lte':
              whereConditions.push({
                path: [key],
                operator: 'LessThanEqual',
                valueNumber: val
              });
              break;
            case '$ne':
              whereConditions.push({
                path: [key],
                operator: 'NotEqual',
                valueText: typeof val === 'string' ? val : undefined,
                valueNumber: typeof val === 'number' ? val : undefined
              });
              break;
          }
        }
      }
    }

    if (whereConditions.length === 1) {
      return whereConditions[0];
    } else if (whereConditions.length > 1) {
      return {
        operator: 'And',
        operands: whereConditions
      };
    }

    return undefined;
  }

  /**
   * Get detailed collection information
   */
  async getCollectionInfo(): Promise<any> {
    try {
      return await this.client.collections.get(this.config.weaviate.className).config.get();
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to get collection info: ${error.message}`,
        'weaviate'
      );
    }
  }
}