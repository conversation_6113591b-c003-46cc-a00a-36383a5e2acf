/**
 * Pinecone Vector Store Implementation
 * 
 * Implements the VectorStore interface using <PERSON><PERSON><PERSON> as the backend.
 * Based on official Pinecone TypeScript client documentation and best practices.
 */

import { Pinecone, PineconeRecord } from '@pinecone-database/pinecone';
import {
  VectorStore,
  VectorRecord,
  VectorSearchQuery,
  VectorSearchResponse,
  VectorSearchResult,
  VectorStoreConfig,
  VectorStoreStats,
  VectorStoreHealth,
  VectorStoreError,
  VectorStoreConnectionError,
  VectorStoreIndexError,
  VectorStoreQueryError
} from '../vector-store';

export interface PineconeConfig extends VectorStoreConfig {
  provider: 'pinecone';
  apiKey: string;
  environment?: string;
  pinecone: {
    cloud: 'aws' | 'gcp' | 'azure';
    region: string;
    podType?: string;
    replicas?: number;
    shards?: number;
  };
}

export class PineconeVectorStore extends VectorStore {
  private client: Pinecone;
  private index: any;
  protected config: PineconeConfig;

  constructor(config: PineconeConfig) {
    super(config);
    this.config = config;

    if (!config.apiKey) {
      throw new VectorStoreError('Pinecone API key is required', 'pinecone', 'initialization');
    }

    // Initialize client
    this.client = new Pinecone({
      apiKey: config.apiKey,
      environment: config.environment || 'us-west1-gcp'
    });
  }

  async initialize(): Promise<void> {
    try {
      // Initialize Pinecone client
      this.client = new Pinecone({
        apiKey: this.config.apiKey,
        environment: this.config.environment || 'us-east-1',
      });

      // Get index reference
      this.index = this.client.index(this.config.indexName);

      // Verify connection by checking if index exists
      await this.client.describeIndex(this.config.indexName);
      
      this.isInitialized = true;
    } catch (error: any) {
      throw new VectorStoreConnectionError(
        `Failed to initialize Pinecone: ${error.message}`,
        'pinecone'
      );
    }
  }

  async isReady(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        return false;
      }

      const indexInfo = await this.client.describeIndex(this.config.indexName);
      return indexInfo.status?.ready === true;
    } catch (error) {
      return false;
    }
  }

  async getHealth(): Promise<VectorStoreHealth> {
    const startTime = Date.now();
    
    try {
      const indexInfo = await this.client.describeIndex(this.config.indexName);
      const responseTime = Date.now() - startTime;
      
      return {
        isHealthy: indexInfo.status?.ready === true,
        provider: 'pinecone',
        indexName: this.config.indexName,
        status: indexInfo.status?.ready ? 'ready' : 'initializing',
        lastChecked: new Date(),
        responseTime
      };
    } catch (error: any) {
      return {
        isHealthy: false,
        provider: 'pinecone',
        indexName: this.config.indexName,
        status: 'error',
        lastChecked: new Date(),
        error: error.message,
        responseTime: Date.now() - startTime
      };
    }
  }

  async upsert(vectors: VectorRecord[], namespace?: string): Promise<void> {
    try {
      const pineconeRecords: PineconeRecord[] = vectors.map(vector => {
        const record: any = {
          id: vector.id,
          values: vector.vector,
          metadata: vector.metadata
        };

        if (vector.sparseVector !== undefined) {
          record.sparseValues = vector.sparseVector;
        }

        return record;
      });

      const targetIndex = namespace ? this.index.namespace(namespace) : this.index;
      await targetIndex.upsert(pineconeRecords);
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to upsert vectors: ${error.message}`,
        'pinecone'
      );
    }
  }

  async search(query: VectorSearchQuery): Promise<VectorSearchResponse> {
    try {
      const targetIndex = query.namespace ? this.index.namespace(query.namespace) : this.index;
      
      const searchParams: any = {
        topK: query.topK,
        includeMetadata: query.includeMetadata ?? true,
        includeValues: query.includeValues ?? false
      };

      if (query.vector) {
        searchParams.vector = query.vector;
      }

      if (query.id) {
        searchParams.id = query.id;
      }

      if (query.filter) {
        searchParams.filter = query.filter;
      }

      if (query.sparseVector) {
        searchParams.sparseVector = query.sparseVector;
      }

      const response = await targetIndex.query(searchParams);

      const matches: VectorSearchResult[] = response.matches?.map((match: any) => ({
        id: match.id,
        score: match.score,
        vector: match.values,
        metadata: match.metadata,
        sparseVector: match.sparseValues
      })) || [];

      return {
        matches,
        namespace: query.namespace || '',
        usage: response.usage
      };
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to search vectors: ${error.message}`,
        'pinecone'
      );
    }
  }

  async fetch(ids: string[], namespace?: string): Promise<Record<string, VectorRecord>> {
    try {
      const targetIndex = namespace ? this.index.namespace(namespace) : this.index;
      const response = await targetIndex.fetch(ids);

      const records: Record<string, VectorRecord> = {};
      
      if (response.records) {
        for (const [id, record] of Object.entries(response.records)) {
          const pineconeRecord = record as any;
          records[id] = {
            id,
            vector: pineconeRecord.values || [],
            metadata: pineconeRecord.metadata,
            sparseVector: pineconeRecord.sparseValues
          };
        }
      }

      return records;
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to fetch vectors: ${error.message}`,
        'pinecone'
      );
    }
  }

  async delete(ids: string[], namespace?: string): Promise<void> {
    try {
      const targetIndex = namespace ? this.index.namespace(namespace) : this.index;
      
      if (ids.length === 1) {
        await targetIndex.deleteOne(ids[0]);
      } else {
        await targetIndex.deleteMany(ids);
      }
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to delete vectors: ${error.message}`,
        'pinecone'
      );
    }
  }

  async deleteAll(namespace?: string): Promise<void> {
    try {
      const targetIndex = namespace ? this.index.namespace(namespace) : this.index;
      await targetIndex.deleteAll();
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to delete all vectors: ${error.message}`,
        'pinecone'
      );
    }
  }

  async getStats(): Promise<VectorStoreStats> {
    try {
      const stats = await this.index.describeIndexStats();
      
      return {
        totalRecords: stats.totalRecordCount || 0,
        dimension: stats.dimension || this.config.dimension,
        namespaces: stats.namespaces || {},
        indexFullness: stats.indexFullness || 0
      };
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to get stats: ${error.message}`,
        'pinecone'
      );
    }
  }

  async createIndex(): Promise<void> {
    try {
      const createParams: any = {
        name: this.config.indexName,
        dimension: this.config.dimension,
        metric: this.config.metric || 'cosine',
        waitUntilReady: true
      };

      // Configure for serverless or pod-based deployment
      if (this.config.pinecone.cloud && this.config.pinecone.region) {
        createParams.spec = {
          serverless: {
            cloud: this.config.pinecone.cloud,
            region: this.config.pinecone.region
          }
        };
      } else if (this.config.environment) {
        createParams.spec = {
          pod: {
            environment: this.config.environment,
            podType: this.config.pinecone.podType || 'p1.x1',
            pods: 1,
            replicas: this.config.pinecone.replicas || 1,
            shards: this.config.pinecone.shards || 1
          }
        };
      }

      await this.client.createIndex(createParams);
    } catch (error: any) {
      throw new VectorStoreIndexError(
        `Failed to create index: ${error.message}`,
        'pinecone'
      );
    }
  }

  async deleteIndex(): Promise<void> {
    try {
      await this.client.deleteIndex(this.config.indexName);
    } catch (error: any) {
      throw new VectorStoreIndexError(
        `Failed to delete index: ${error.message}`,
        'pinecone'
      );
    }
  }

  async listIndexes(): Promise<string[]> {
    try {
      const response = await this.client.listIndexes();
      return (response as any).indexes?.map((index: any) => index.name) || [];
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to list indexes: ${error.message}`,
        'pinecone'
      );
    }
  }

  async close(): Promise<void> {
    // Pinecone client doesn't require explicit cleanup
    this.isInitialized = false;
  }

  /**
   * Configure index settings (pod-based only)
   */
  async configureIndex(replicas?: number, podType?: string): Promise<void> {
    try {
      const configParams: any = {};
      
      if (replicas !== undefined) {
        configParams.replicas = replicas;
      }
      
      if (podType) {
        configParams.podType = podType;
      }

      await this.client.configureIndex(this.config.indexName, configParams);
    } catch (error: any) {
      throw new VectorStoreIndexError(
        `Failed to configure index: ${error.message}`,
        'pinecone'
      );
    }
  }

  /**
   * Get detailed index information
   */
  async getIndexInfo(): Promise<any> {
    try {
      return await this.client.describeIndex(this.config.indexName);
    } catch (error: any) {
      throw new VectorStoreQueryError(
        `Failed to get index info: ${error.message}`,
        'pinecone'
      );
    }
  }
}