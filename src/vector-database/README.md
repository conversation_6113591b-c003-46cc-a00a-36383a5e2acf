# Vector Database Integration

A comprehensive vector database integration system supporting multiple providers (Pinecone, Weaviate) with advanced features like health monitoring, automatic fallbacks, and performance optimization.

## Features

- **Multi-Provider Support**: Unified interface for Pinecone and Weaviate
- **Health Monitoring**: Real-time health checks and status reporting
- **Automatic Fallbacks**: Seamless switching between primary and fallback providers
- **Performance Metrics**: Detailed operation tracking and latency monitoring
- **Retry Logic**: Exponential backoff retry mechanisms for resilient operations
- **Batch Operations**: Efficient bulk vector operations
- **Type Safety**: Full TypeScript support with comprehensive error handling

## Quick Start

### Basic Usage

```typescript
import { VectorStoreFactory, DefaultVectorConfigs } from './vector-database';

// Create a Pinecone store
const config = {
  ...DefaultVectorConfigs.pineconeSemanticSearch,
  apiKey: process.env.PINECONE_API_KEY!,
  indexName: 'my-index'
};

const store = VectorStoreFactory.create(config);
await store.initialize();

// Upsert vectors
await store.upsert([
  {
    id: 'doc-1',
    vector: [0.1, 0.2, 0.3, ...], // 1536-dimensional vector
    metadata: { title: 'Document 1', category: 'regulation' }
  }
]);

// Search for similar vectors
const results = await store.search({
  vector: [0.1, 0.2, 0.3, ...],
  topK: 5,
  includeMetadata: true
});
```

### Advanced Service with Fallback

```typescript
import { VectorStoreService, DefaultServiceConfigs } from './vector-database';

const service = new VectorStoreService({
  primary: {
    provider: 'pinecone',
    apiKey: process.env.PINECONE_API_KEY!,
    indexName: 'primary-index',
    dimension: 1536,
    // ... other config
  },
  fallback: {
    provider: 'weaviate',
    indexName: 'fallback-index',
    weaviate: {
      scheme: 'http',
      host: 'localhost:8080',
      className: 'Document'
    },
    // ... other config
  },
  ...DefaultServiceConfigs.production
});

await service.initialize();

// Operations automatically use fallback if primary fails
await service.upsert(vectors);
const results = await service.search(query);
```

## Providers

### Pinecone

Fully managed vector database service with excellent performance and scalability.

**Configuration:**
```typescript
const pineconeConfig: PineconeConfig = {
  provider: 'pinecone',
  apiKey: 'your-api-key',
  indexName: 'my-index',
  dimension: 1536,
  metric: 'cosine',
  pinecone: {
    cloud: 'aws',
    region: 'us-west-2'
  }
};
```

**Features:**
- Serverless and pod-based deployments
- Built-in metadata filtering
- Hybrid search with sparse vectors
- Automatic scaling
- Global availability

**Best For:**
- Production applications
- High-performance requirements
- Simple vector search use cases
- Managed infrastructure preference

### Weaviate

Open-source vector database with rich querying capabilities and ML model integrations.

**Configuration:**
```typescript
const weaviateConfig: WeaviateConfig = {
  provider: 'weaviate',
  indexName: 'my-collection',
  dimension: 1536,
  metric: 'cosine',
  weaviate: {
    scheme: 'https',
    host: 'your-cluster.weaviate.network',
    className: 'Document',
    vectorizer: 'text2vec-openai'
  }
};
```

**Features:**
- Open source with cloud options
- GraphQL API
- Hybrid search (vector + keyword)
- Built-in ML model integrations
- Multi-tenancy support
- Custom schema definitions

**Best For:**
- Complex search requirements
- Hybrid search needs
- Open source preference
- Custom ML model integration
- Multi-tenant applications

## API Reference

### VectorStore Interface

#### Core Methods

```typescript
// Initialize connection
await store.initialize(): Promise<void>

// Check if ready
await store.isReady(): Promise<boolean>

// Get health status
await store.getHealth(): Promise<VectorStoreHealth>

// Upsert vectors
await store.upsert(vectors: VectorRecord[], namespace?: string): Promise<void>

// Search vectors
await store.search(query: VectorSearchQuery): Promise<VectorSearchResponse>

// Fetch by IDs
await store.fetch(ids: string[], namespace?: string): Promise<Record<string, VectorRecord>>

// Delete vectors
await store.delete(ids: string[], namespace?: string): Promise<void>

// Get statistics
await store.getStats(): Promise<VectorStoreStats>
```

#### Index Management

```typescript
// Create index/collection
await store.createIndex(): Promise<void>

// Delete index/collection
await store.deleteIndex(): Promise<void>

// List all indexes
await store.listIndexes(): Promise<string[]>

// Close connection
await store.close(): Promise<void>
```

### VectorStoreService

High-level service with advanced features:

```typescript
const service = new VectorStoreService(config);

// Event listeners
service.on('initialized', () => console.log('Ready'));
service.on('fallback-used', (event) => console.log('Fallback:', event));
service.on('health-check', (health) => console.log('Health:', health));
service.on('error', (error) => console.error('Error:', error));

// Operations with automatic retry and fallback
await service.upsert(vectors);
const results = await service.search(query);

// Get metrics and status
const metrics = service.getMetrics();
const status = service.getStatus();
```

## Configuration

### Environment Variables

```bash
# Pinecone
PINECONE_API_KEY=your-pinecone-api-key

# Weaviate
WEAVIATE_URL=https://your-cluster.weaviate.network
WEAVIATE_API_KEY=your-weaviate-api-key

# Optional: Provider-specific settings
PINECONE_ENVIRONMENT=us-west1-gcp
WEAVIATE_SCHEME=https
```

### Default Configurations

Pre-configured setups for common use cases:

```typescript
import { DefaultVectorConfigs, DefaultServiceConfigs } from './vector-database';

// Vector store configs
DefaultVectorConfigs.pineconeSemanticSearch
DefaultVectorConfigs.pineconeHighPerformance
DefaultVectorConfigs.weaviateSemanticSearch
DefaultVectorConfigs.weaviateMultimodal

// Service configs
DefaultServiceConfigs.production
DefaultServiceConfigs.development
DefaultServiceConfigs.testing
```

## Utilities

### Vector Operations

```typescript
import { VectorUtils } from './vector-database';

// Similarity calculations
const similarity = VectorUtils.cosineSimilarity(vector1, vector2);
const distance = VectorUtils.euclideanDistance(vector1, vector2);

// Vector normalization
const normalized = VectorUtils.normalizeVector(vector);

// Validation
const isValid = VectorUtils.validateVector(vector, expectedDimension);

// Test data generation
const randomVector = VectorUtils.generateRandomVector(1536, true);

// Batch processing
const batches = VectorUtils.batchVectors(vectors, batchSize);
```

### Metric Conversions

```typescript
// Convert between similarity and distance
const distance = VectorUtils.similarityToDistance(similarity, 'cosine');
const similarity = VectorUtils.distanceToSimilarity(distance, 'cosine');
```

## Error Handling

The system provides specific error types for different failure scenarios:

```typescript
import { 
  VectorStoreError,
  VectorStoreConnectionError,
  VectorStoreIndexError,
  VectorStoreQueryError 
} from './vector-database';

try {
  await store.search(query);
} catch (error) {
  if (error instanceof VectorStoreConnectionError) {
    console.log('Connection issue:', error.message);
  } else if (error instanceof VectorStoreQueryError) {
    console.log('Query issue:', error.message);
  }
}
```

## Performance Optimization

### Batch Operations

```typescript
// Process vectors in batches for better performance
const batches = VectorUtils.batchVectors(vectors, 100);
for (const batch of batches) {
  await store.upsert(batch);
}
```

### Connection Pooling

```typescript
// Use service for connection pooling and health monitoring
const service = new VectorStoreService({
  primary: config,
  ...DefaultServiceConfigs.production
});
```

### Metrics Monitoring

```typescript
// Monitor performance metrics
const metrics = service.getMetrics();
console.log('Average latency:', metrics.operations.averageLatency);
console.log('Success rate:', metrics.operations.successful / metrics.operations.total);
```

## Migration Guide

### From Direct Provider SDKs

1. **Replace direct SDK imports:**
   ```typescript
   // Before
   import { Pinecone } from '@pinecone-database/pinecone';
   
   // After
   import { VectorStoreFactory } from './vector-database';
   ```

2. **Update configuration:**
   ```typescript
   // Before
   const pinecone = new Pinecone({ apiKey: 'key' });
   
   // After
   const store = VectorStoreFactory.create({
     provider: 'pinecone',
     apiKey: 'key',
     // ... other config
   });
   ```

3. **Update operations:**
   ```typescript
   // Before
   await pinecone.index('name').upsert(vectors);
   
   // After
   await store.upsert(vectors);
   ```

### Between Providers

Use the factory's migration guide:

```typescript
const guide = VectorStoreFactory.getMigrationGuide('pinecone', 'weaviate');
console.log(guide);
```

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Check API keys and network connectivity
   - Verify index/collection exists
   - Check provider-specific requirements

2. **Dimension Mismatches**
   - Ensure all vectors have consistent dimensions
   - Verify index configuration matches vector dimensions

3. **Performance Issues**
   - Use batch operations for bulk data
   - Monitor metrics and adjust batch sizes
   - Consider using fallback providers

### Debug Mode

Enable detailed logging:

```typescript
const service = new VectorStoreService(config);
service.on('retry', (event) => console.log('Retry:', event));
service.on('fallback-used', (event) => console.log('Fallback:', event));
service.on('error', (error) => console.error('Error:', error));
```

## Examples

See `examples/vector-database/vector-store-example.ts` for comprehensive usage examples including:

- Basic provider setup
- Advanced service configuration
- Batch operations
- Performance testing
- Error handling
- Utility functions

## Contributing

When adding new providers or features:

1. Implement the `VectorStore` interface
2. Add provider-specific configuration types
3. Update the factory with validation logic
4. Add comprehensive tests
5. Update documentation and examples

## License

This vector database integration is part of the DFSA Rulebook RAG system and follows the same licensing terms.