/**
 * Vector Store Factory
 * 
 * Factory class for creating vector store instances with validation,
 * configuration recommendations, and provider-specific optimizations.
 */

import { VectorStore, VectorStoreConfig } from './vector-store';
import { PineconeVectorStore, PineconeConfig } from './providers/pinecone-store';
import { WeaviateVectorStore, WeaviateConfig } from './providers/weaviate-store';

export class VectorStoreFactory {
  /**
   * Create a vector store instance based on configuration
   */
  static create(config: VectorStoreConfig): VectorStore {
    this.validateConfig(config);

    switch (config.provider) {
      case 'pinecone':
        return new PineconeVectorStore(config as PineconeConfig);
      
      case 'weaviate':
        return new WeaviateVectorStore(config as WeaviateConfig);
      
      default:
        throw new Error(`Unsupported vector store provider: ${config.provider}`);
    }
  }

  /**
   * Validate vector store configuration
   */
  private static validateConfig(config: VectorStoreConfig): void {
    if (!config.provider) {
      throw new Error('Vector store provider is required');
    }

    if (!config.indexName) {
      throw new Error('Index name is required');
    }

    if (!config.dimension || config.dimension <= 0) {
      throw new Error('Valid dimension is required');
    }

    // Provider-specific validation
    switch (config.provider) {
      case 'pinecone':
        this.validatePineconeConfig(config as PineconeConfig);
        break;
      
      case 'weaviate':
        this.validateWeaviateConfig(config as WeaviateConfig);
        break;
    }
  }

  /**
   * Validate Pinecone-specific configuration
   */
  private static validatePineconeConfig(config: PineconeConfig): void {
    if (!config.apiKey) {
      throw new Error('Pinecone API key is required');
    }

    if (!config.pinecone) {
      throw new Error('Pinecone configuration is required');
    }

    if (!config.pinecone.cloud || !config.pinecone.region) {
      throw new Error('Pinecone cloud and region are required for serverless deployment');
    }

    // Validate metric
    const validMetrics = ['cosine', 'euclidean', 'dotproduct'];
    if (config.metric && !validMetrics.includes(config.metric)) {
      throw new Error(`Invalid metric. Must be one of: ${validMetrics.join(', ')}`);
    }

    // Validate dimension limits
    if (config.dimension > 20000) {
      throw new Error('Pinecone dimension cannot exceed 20,000');
    }
  }

  /**
   * Validate Weaviate-specific configuration
   */
  private static validateWeaviateConfig(config: WeaviateConfig): void {
    if (!config.weaviate) {
      throw new Error('Weaviate configuration is required');
    }

    if (!config.weaviate.host) {
      throw new Error('Weaviate host is required');
    }

    if (!config.weaviate.className) {
      throw new Error('Weaviate class name is required');
    }

    // Validate scheme
    if (config.weaviate.scheme && !['http', 'https'].includes(config.weaviate.scheme)) {
      throw new Error('Weaviate scheme must be http or https');
    }

    // Validate class name format
    if (!/^[A-Z][a-zA-Z0-9_]*$/.test(config.weaviate.className)) {
      throw new Error('Weaviate class name must start with uppercase letter and contain only alphanumeric characters and underscores');
    }
  }

  /**
   * Get recommended configuration for a provider
   */
  static getRecommendedConfig(provider: 'pinecone' | 'weaviate', useCase: string): Partial<VectorStoreConfig> {
    switch (provider) {
      case 'pinecone':
        return this.getPineconeRecommendations(useCase);
      
      case 'weaviate':
        return this.getWeaviateRecommendations(useCase);
      
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Get Pinecone configuration recommendations
   */
  private static getPineconeRecommendations(useCase: string): Partial<PineconeConfig> {
    const baseConfig: Partial<PineconeConfig> = {
      provider: 'pinecone',
      metric: 'cosine',
      timeout: 30000,
      retryAttempts: 3
    };

    switch (useCase.toLowerCase()) {
      case 'semantic-search':
      case 'rag':
        return {
          ...baseConfig,
          dimension: 1536, // OpenAI text-embedding-3-small
          pinecone: {
            cloud: 'aws',
            region: 'us-west-2'
          }
        };
      
      case 'image-search':
        return {
          ...baseConfig,
          dimension: 512, // CLIP embeddings
          metric: 'cosine',
          pinecone: {
            cloud: 'aws',
            region: 'us-west-2'
          }
        };
      
      case 'high-performance':
        return {
          ...baseConfig,
          dimension: 768,
          pinecone: {
            cloud: 'gcp',
            region: 'us-central1',
            podType: 'p2.x1',
            replicas: 2
          }
        };
      
      default:
        return baseConfig;
    }
  }

  /**
   * Get Weaviate configuration recommendations
   */
  private static getWeaviateRecommendations(useCase: string): Partial<WeaviateConfig> {
    const baseConfig: Partial<WeaviateConfig> = {
      provider: 'weaviate',
      metric: 'cosine',
      timeout: 30000,
      retryAttempts: 3,
      weaviate: {
        scheme: 'https',
        host: 'localhost:8080',
        className: 'Document'
      }
    };

    switch (useCase.toLowerCase()) {
      case 'semantic-search':
      case 'rag':
        return {
          ...baseConfig,
          dimension: 1536,
          weaviate: {
            ...baseConfig.weaviate!,
            className: 'Document',
            vectorizer: 'text2vec-openai'
          }
        };
      
      case 'multimodal':
        return {
          ...baseConfig,
          dimension: 512,
          weaviate: {
            ...baseConfig.weaviate!,
            className: 'MultimodalObject',
            vectorizer: 'multi2vec-clip'
          }
        };
      
      case 'local-processing':
        return {
          ...baseConfig,
          dimension: 384,
          weaviate: {
            ...baseConfig.weaviate!,
            className: 'LocalDocument',
            vectorizer: 'text2vec-transformers'
          }
        };
      
      default:
        return baseConfig;
    }
  }

  /**
   * Get provider comparison information
   */
  static getProviderComparison(): Record<string, any> {
    return {
      pinecone: {
        strengths: [
          'Fully managed service',
          'Excellent performance and scalability',
          'Simple API and setup',
          'Built-in metadata filtering',
          'Serverless and pod-based options'
        ],
        limitations: [
          'Proprietary/closed source',
          'Cost can be high for large datasets',
          'Limited to vector operations',
          'Vendor lock-in'
        ],
        bestFor: [
          'Production applications',
          'High-performance requirements',
          'Simple vector search use cases',
          'Teams wanting managed infrastructure'
        ],
        pricing: 'Usage-based, can be expensive for large datasets'
      },
      weaviate: {
        strengths: [
          'Open source with cloud options',
          'Rich querying capabilities',
          'Built-in ML model integrations',
          'Hybrid search (vector + keyword)',
          'GraphQL API',
          'Multi-tenancy support'
        ],
        limitations: [
          'More complex setup and configuration',
          'Requires more operational overhead',
          'Learning curve for GraphQL',
          'Performance tuning needed'
        ],
        bestFor: [
          'Complex search requirements',
          'Hybrid search needs',
          'Open source preference',
          'Custom ML model integration',
          'Multi-tenant applications'
        ],
        pricing: 'Free for self-hosted, usage-based for cloud'
      }
    };
  }

  /**
   * Get migration guide between providers
   */
  static getMigrationGuide(from: string, to: string): string[] {
    const guides: Record<string, string[]> = {
      'pinecone-to-weaviate': [
        '1. Export data from Pinecone using fetch operations',
        '2. Create Weaviate collection with appropriate schema',
        '3. Transform metadata to Weaviate properties format',
        '4. Batch import data using Weaviate client',
        '5. Update application code to use Weaviate API patterns',
        '6. Test search functionality and performance',
        '7. Update monitoring and alerting'
      ],
      'weaviate-to-pinecone': [
        '1. Export data from Weaviate using GraphQL queries',
        '2. Create Pinecone index with matching dimensions',
        '3. Transform Weaviate objects to Pinecone records',
        '4. Batch upsert data to Pinecone',
        '5. Update application code to use Pinecone API',
        '6. Test search functionality and performance',
        '7. Update monitoring and cost tracking'
      ]
    };

    const key = `${from}-to-${to}`;
    return guides[key] || ['Migration guide not available for this combination'];
  }
}