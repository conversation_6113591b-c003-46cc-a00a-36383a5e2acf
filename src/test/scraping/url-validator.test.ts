import { URLValidator } from '../../scraping/url-validator';

describe('URLValidator', () => {
  let validator: URLValidator;

  beforeEach(() => {
    validator = new URLValidator();
  });

  describe('validateUrl', () => {
    it('should validate DFSA rulebook URLs', () => {
      const validUrls = [
        'https://dfsaen.thomsonreuters.com/rulebook/general-module',
        'https://www.dfsaen.thomsonreuters.com/rulebook/conduct-of-business',
        'https://dfsaen.thomsonreuters.com/rules/general-provisions',
        'https://dfsaen.thomsonreuters.com/guidance/anti-money-laundering'
      ];

      validUrls.forEach(url => {
        const result = validator.validateUrl(url);
        expect(result.isValid).toBe(true);
        expect(result.normalizedUrl).toBeDefined();
      });
    });

    it('should reject non-DFSA URLs', () => {
      const invalidUrls = [
        'https://google.com',
        'https://example.com/rulebook',
        'https://other-site.com/dfsa/rules'
      ];

      invalidUrls.forEach(url => {
        const result = validator.validateUrl(url);
        expect(result.isValid).toBe(false);
        expect(result.reason).toContain('Not a DFSA domain URL');
      });
    });

    it('should reject excluded patterns', () => {
      const excludedUrls = [
        'https://dfsaen.thomsonreuters.com/search?q=test',
        'https://dfsaen.thomsonreuters.com/print/rulebook',
        'https://dfsaen.thomsonreuters.com/rulebook/document.pdf',
        'https://dfsaen.thomsonreuters.com/images/logo.png',
        'https://dfsaen.thomsonreuters.com/css/styles.css',
        'https://dfsaen.thomsonreuters.com/js/script.js'
      ];

      excludedUrls.forEach(url => {
        const result = validator.validateUrl(url);
        expect(result.isValid).toBe(false);
        expect(result.reason).toContain('Matches excluded pattern');
      });
    });

    it('should reject URLs without valid content paths', () => {
      const invalidPaths = [
        'https://dfsaen.thomsonreuters.com/',
        'https://dfsaen.thomsonreuters.com/about',
        'https://dfsaen.thomsonreuters.com/contact',
        'https://dfsaen.thomsonreuters.com/news'
      ];

      invalidPaths.forEach(url => {
        const result = validator.validateUrl(url);
        expect(result.isValid).toBe(false);
        expect(result.reason).toContain('Does not match valid DFSA content patterns');
      });
    });

    it('should normalize URLs correctly', () => {
      const testCases = [
        {
          input: 'https://dfsaen.thomsonreuters.com/rulebook/test#section1',
          expected: 'https://dfsaen.thomsonreuters.com/rulebook/test'
        },
        {
          input: 'https://dfsaen.thomsonreuters.com/rulebook/test/',
          expected: 'https://dfsaen.thomsonreuters.com/rulebook/test'
        },
        {
          input: 'https://dfsaen.thomsonreuters.com/rulebook/test?b=2&a=1',
          expected: 'https://dfsaen.thomsonreuters.com/rulebook/test?a=1&b=2'
        }
      ];

      testCases.forEach(({ input, expected }) => {
        const result = validator.validateUrl(input);
        expect(result.isValid).toBe(true);
        expect(result.normalizedUrl).toBe(expected);
      });
    });
  });

  describe('validateUrls', () => {
    it('should batch validate multiple URLs', () => {
      const urls = [
        'https://dfsaen.thomsonreuters.com/rulebook/general',
        'https://google.com',
        'https://dfsaen.thomsonreuters.com/rules/conduct',
        'https://dfsaen.thomsonreuters.com/search?q=test'
      ];

      const result = validator.validateUrls(urls);
      
      expect(result.valid).toHaveLength(2);
      expect(result.invalid).toHaveLength(2);
      expect(result.valid).toContain('https://dfsaen.thomsonreuters.com/rulebook/general');
      expect(result.valid).toContain('https://dfsaen.thomsonreuters.com/rules/conduct');
    });
  });

  describe('deduplicateUrls', () => {
    it('should remove duplicate URLs', () => {
      const urls = [
        'https://dfsaen.thomsonreuters.com/rulebook/test',
        'https://dfsaen.thomsonreuters.com/rulebook/test/',
        'https://dfsaen.thomsonreuters.com/rulebook/test#section',
        'https://dfsaen.thomsonreuters.com/rulebook/other'
      ];

      const result = validator.deduplicateUrls(urls);
      
      expect(result.unique).toHaveLength(2);
      expect(result.duplicatesRemoved).toBe(2);
      expect(result.unique).toContain('https://dfsaen.thomsonreuters.com/rulebook/test');
      expect(result.unique).toContain('https://dfsaen.thomsonreuters.com/rulebook/other');
    });
  });
});