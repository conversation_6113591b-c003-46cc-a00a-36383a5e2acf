import { BrightdataClient } from '../../scraping/brightdata-client';

// Mock puppeteer-core
jest.mock('puppeteer-core', () => ({
  connect: jest.fn(() => Promise.resolve({
    newPage: jest.fn(() => Promise.resolve({
      setViewport: jest.fn(),
      setUserAgent: jest.fn(),
      setExtraHTTPHeaders: jest.fn(),
      setRequestInterception: jest.fn(),
      on: jest.fn(),
      goto: jest.fn(() => Promise.resolve({
        status: () => 200,
        headers: () => ({ 'content-type': 'text/html' })
      })),
      waitForSelector: jest.fn(),
      waitForTimeout: jest.fn(),
      content: jest.fn(() => Promise.resolve('<html><head><title>Test Page</title></head><body><h1>DFSA Rulebook</h1><p>Test content</p></body></html>')),
      title: jest.fn(() => Promise.resolve('Test Page')),
      screenshot: jest.fn(() => Promise.resolve(Buffer.from('fake-screenshot'))),
      close: jest.fn()
    })),
    close: jest.fn()
  }))
}));

describe('BrightdataClient', () => {
  let client: BrightdataClient;

  beforeEach(() => {
    client = new BrightdataClient({
      username: 'test-username',
      password: 'test-password',
      zone: 'browser_api',
      country: 'US'
    });
  });

  afterEach(async () => {
    await client.close();
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with provided config', () => {
      const config = {
        username: 'test-username',
        password: 'test-password',
        zone: 'residential_browser',
        country: 'UK'
      };

      const testClient = new BrightdataClient(config);
      const health = testClient.getHealthStatus();
      
      expect(health.config.zone).toBe(config.zone);
      expect(health.config.country).toBe(config.country);
    });

    it('should throw error if username is missing', () => {
      expect(() => {
        new BrightdataClient({ password: 'test-password' } as any);
      }).toThrow('Brightdata username and password are required');
    });

    it('should throw error if password is missing', () => {
      expect(() => {
        new BrightdataClient({ username: 'test-username' } as any);
      }).toThrow('Brightdata username and password are required');
    });
  });

  describe('initialize', () => {
    it('should connect to browser using WebSocket endpoint', async () => {
      const puppeteer = require('puppeteer-core');
      
      await client.initialize();
      
      expect(puppeteer.connect).toHaveBeenCalledWith(
        expect.objectContaining({
          browserWSEndpoint: expect.stringContaining('wss://')
        })
      );
    });
  });

  describe('scrapeUrl', () => {
    it('should successfully scrape a URL', async () => {
      const testUrl = 'https://dfsaen.thomsonreuters.com/rulebook/test';
      
      const result = await client.scrapeUrl(testUrl);
      
      expect(result.url).toBe(testUrl);
      expect(result.title).toBe('Test Page');
      expect(result.content).toContain('DFSA Rulebook');
      expect(result.statusCode).toBe(200);
      expect(result.error).toBeUndefined();
      expect(result.loadTime).toBeGreaterThanOrEqual(0);
    });

    it('should handle scraping options', async () => {
      const testUrl = 'https://dfsaen.thomsonreuters.com/rulebook/test';
      const options = {
        waitForSelector: 'h1',
        waitForTimeout: 5000,
        takeScreenshot: true,
        viewport: { width: 1280, height: 720 },
        customHeaders: { 'Accept': 'text/html' }
      };
      
      const result = await client.scrapeUrl(testUrl, options);
      
      expect(result.screenshot).toBeDefined();
      expect(result.screenshot).toBeInstanceOf(Buffer);
    });

    it('should block resources when specified', async () => {
      const testUrl = 'https://dfsaen.thomsonreuters.com/rulebook/test';
      const options = {
        blockResources: ['*.css', '*.js', 'image']
      };
      
      const result = await client.scrapeUrl(testUrl, options);
      
      expect(result.error).toBeUndefined();
      // Verify that request interception was set up
      expect(result.error).toBeUndefined();
      // Note: In a real test, we would verify request interception was called
    });

    it('should handle scraping errors gracefully', async () => {
      const puppeteer = require('puppeteer-core');
      const mockPage = {
        setViewport: jest.fn(),
        setUserAgent: jest.fn(),
        setExtraHTTPHeaders: jest.fn(),
        goto: jest.fn(() => Promise.reject(new Error('Network error'))),
        close: jest.fn()
      };
      
      puppeteer.connect.mockResolvedValueOnce({
        newPage: jest.fn(() => Promise.resolve(mockPage)),
        close: jest.fn()
      });

      const testUrl = 'https://invalid-url.com';
      const result = await client.scrapeUrl(testUrl);
      
      expect(result.error).toBe('Network error');
      expect(result.statusCode).toBe(0);
      expect(result.content).toBe('');
    });
  });

  describe('scrapeUrls', () => {
    it('should scrape multiple URLs with concurrency control', async () => {
      const testUrls = [
        'https://dfsaen.thomsonreuters.com/rulebook/test1',
        'https://dfsaen.thomsonreuters.com/rulebook/test2',
        'https://dfsaen.thomsonreuters.com/rulebook/test3'
      ];
      
      const results = await client.scrapeUrls(testUrls, {}, 2);
      
      expect(results).toHaveLength(3);
      results.forEach((result, index) => {
        expect(result.url).toBe(testUrls[index]);
        expect(result.title).toBe('Test Page');
        expect(result.error).toBeUndefined();
      });
    });

    it('should handle mixed success and failure results', async () => {
      // Create a fresh client for this test
      const testClient = new BrightdataClient({
        username: 'test-username',
        password: 'test-password',
        zone: 'browser_api',
        country: 'US'
      });

      const puppeteer = require('puppeteer-core');
      let pageCallCount = 0;
      
      const createMockPage = () => {
        pageCallCount++;
        const shouldFail = pageCallCount === 2;
        
        return {
          setViewport: jest.fn(),
          setUserAgent: jest.fn(),
          setExtraHTTPHeaders: jest.fn(),
          goto: jest.fn(() => {
            if (shouldFail) {
              return Promise.reject(new Error('Failed'));
            }
            return Promise.resolve({
              status: () => 200,
              headers: () => ({ 'content-type': 'text/html' })
            });
          }),
          content: jest.fn(() => Promise.resolve('<html><title>Test</title></html>')),
          title: jest.fn(() => Promise.resolve('Test')),
          close: jest.fn()
        };
      };
      
      puppeteer.connect.mockResolvedValue({
        newPage: jest.fn(() => Promise.resolve(createMockPage())),
        close: jest.fn()
      });

      const testUrls = [
        'https://dfsaen.thomsonreuters.com/rulebook/test1',
        'https://dfsaen.thomsonreuters.com/rulebook/test2'
      ];
      
      const results = await testClient.scrapeUrls(testUrls);
      
      expect(results).toHaveLength(2);
      expect(results[0]?.error).toBeUndefined();
      expect(results[1]?.error).toBe('Failed');

      await testClient.close();
    });
  });

  describe('testConnection', () => {
    it('should return true for successful connection test', async () => {
      const puppeteer = require('puppeteer-core');
      const mockPage = {
        setViewport: jest.fn(),
        setUserAgent: jest.fn(),
        setExtraHTTPHeaders: jest.fn(),
        goto: jest.fn(() => Promise.resolve({
          status: () => 200,
          headers: () => ({ 'content-type': 'text/html' })
        })),
        waitForTimeout: jest.fn(),
        content: jest.fn(() => Promise.resolve('<html><body>{"ip": "*******", "country": "US"}</body></html>')),
        title: jest.fn(() => Promise.resolve('Geo Test')),
        close: jest.fn()
      };
      
      puppeteer.connect.mockResolvedValueOnce({
        newPage: jest.fn(() => Promise.resolve(mockPage)),
        close: jest.fn()
      });

      const result = await client.testConnection();
      
      expect(result).toBe(true);
    });

    it('should return false for failed connection test', async () => {
      const puppeteer = require('puppeteer-core');
      const mockPage = {
        setViewport: jest.fn(),
        setUserAgent: jest.fn(),
        setExtraHTTPHeaders: jest.fn(),
        goto: jest.fn(() => Promise.reject(new Error('Connection failed'))),
        close: jest.fn()
      };
      
      puppeteer.connect.mockResolvedValueOnce({
        newPage: jest.fn(() => Promise.resolve(mockPage)),
        close: jest.fn()
      });

      const result = await client.testConnection();
      
      expect(result).toBe(false);
    });
  });

  describe('getSessionInfo', () => {
    it('should return session information', async () => {
      const puppeteer = require('puppeteer-core');
      const mockPage = {
        setViewport: jest.fn(),
        setUserAgent: jest.fn(),
        setExtraHTTPHeaders: jest.fn(),
        goto: jest.fn(() => Promise.resolve({
          status: () => 200,
          headers: () => ({ 'content-type': 'text/html' })
        })),
        content: jest.fn(() => Promise.resolve('<html><body>{"ip": "*******", "country": "US"}</body></html>')),
        title: jest.fn(() => Promise.resolve('Geo Test')),
        close: jest.fn()
      };
      
      puppeteer.connect.mockResolvedValueOnce({
        newPage: jest.fn(() => Promise.resolve(mockPage)),
        close: jest.fn()
      });

      const sessionInfo = await client.getSessionInfo();
      
      expect(sessionInfo.proxyIp).toBe('*******');
    });
  });

  describe('getHealthStatus', () => {
    it('should return health status', () => {
      const health = client.getHealthStatus();
      
      expect(health.initialized).toBe(false);
      expect(health.config.zone).toBe('browser_api');
      expect(health.config.country).toBe('US');
    });

    it('should show initialized status after initialization', async () => {
      await client.initialize();
      const health = client.getHealthStatus();
      
      expect(health.initialized).toBe(true);
    });
  });
});