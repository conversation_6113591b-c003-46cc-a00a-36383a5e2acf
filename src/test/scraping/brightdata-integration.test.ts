import { BrightdataClient } from '../../scraping/brightdata-client';

/**
 * Integration tests for Brightdata Browser API
 * These tests require actual Brightdata credentials and should be run manually
 * Set BRIGHTDATA_USERNAME and BRIGHTDATA_PASSWORD environment variables
 */
describe('BrightdataClient Integration Tests', () => {
  let client: BrightdataClient;
  
  // Skip these tests unless explicitly running integration tests
  const runIntegrationTests = process.env.RUN_INTEGRATION_TESTS === 'true';
  const hasCredentials = process.env.BRIGHTDATA_USERNAME && process.env.BRIGHTDATA_PASSWORD;

  beforeAll(() => {
    if (!runIntegrationTests) {
      console.log('Skipping integration tests. Set RUN_INTEGRATION_TESTS=true to run.');
      return;
    }

    if (!hasCredentials) {
      console.log('Skipping integration tests. Set BRIGHTDATA_USERNAME and BRIGHTDATA_PASSWORD.');
      return;
    }

    client = new BrightdataClient({
      username: process.env.BRIGHTDATA_USERNAME!,
      password: process.env.BRIGHTDATA_PASSWORD!,
      zone: 'browser_api',
      country: 'US',
      timeout: 60000
    });
  });

  afterAll(async () => {
    if (client) {
      await client.close();
    }
  });

  describe('Connection and Authentication', () => {
    it('should successfully connect to Brightdata Browser API', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      const connectionTest = await client.testConnection();
      expect(connectionTest).toBe(true);
    }, 30000);

    it('should get session information', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      const sessionInfo = await client.getSessionInfo();
      expect(sessionInfo.proxyIp).toBeDefined();
      expect(sessionInfo.country).toBeDefined();
    }, 30000);
  });

  describe('DFSA Website Scraping', () => {
    it('should successfully scrape DFSA main page', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      const testUrl = 'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa';
      
      const result = await client.scrapeUrl(testUrl, {
        waitForSelector: 'h1, .content, .main',
        waitForTimeout: 10000,
        blockResources: ['image', 'stylesheet', 'font'],
        solveCaptcha: true,
        captchaTimeout: 30000
      });

      expect(result.error).toBeUndefined();
      expect(result.statusCode).toBe(200);
      expect(result.title).toBeTruthy();
      expect(result.content).toContain('DFSA');
      expect(result.loadTime).toBeGreaterThan(0);
    }, 120000);

    it('should handle DFSA pages with complex content', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      const testUrls = [
        'https://dfsaen.thomsonreuters.com/rulebook/general-module',
        'https://dfsaen.thomsonreuters.com/rulebook/conduct-of-business'
      ];

      const results = await client.scrapeUrls(testUrls, {
        waitForTimeout: 5000,
        blockResources: ['image', 'stylesheet', 'font', 'media'],
        solveCaptcha: true
      }, 1); // Low concurrency for testing

      expect(results).toHaveLength(2);
      
      const successfulResults = results.filter(r => !r.error);
      expect(successfulResults.length).toBeGreaterThan(0);

      successfulResults.forEach(result => {
        expect(result.statusCode).toBe(200);
        expect(result.content.length).toBeGreaterThan(100);
        expect(result.title).toBeTruthy();
      });
    }, 180000);
  });

  describe('Error Handling and Resilience', () => {
    it('should handle invalid URLs gracefully', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      const invalidUrl = 'https://invalid-domain-that-does-not-exist.com';
      
      const result = await client.scrapeUrl(invalidUrl);
      
      expect(result.error).toBeDefined();
      expect(result.statusCode).toBe(0);
      expect(result.content).toBe('');
    }, 60000);

    it('should respect rate limiting', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      const rateLimitedClient = new BrightdataClient({
        username: process.env.BRIGHTDATA_USERNAME!,
        password: process.env.BRIGHTDATA_PASSWORD!,
        rateLimitDelay: 2000 // 2 seconds between requests
      });

      const testUrls = [
        'https://geo.brdtest.com/mygeo.json',
        'https://geo.brdtest.com/mygeo.json'
      ];

      const startTime = Date.now();
      const results = await rateLimitedClient.scrapeUrls(testUrls, {}, 1);
      const totalTime = Date.now() - startTime;

      expect(results).toHaveLength(2);
      expect(totalTime).toBeGreaterThan(2000); // Should take at least 2 seconds due to rate limiting

      await rateLimitedClient.close();
    }, 60000);
  });

  describe('Advanced Features', () => {
    it('should handle CAPTCHA solving when enabled', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      // Test with a page that might have CAPTCHAs
      const testUrl = 'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa';
      
      const result = await client.scrapeUrl(testUrl, {
        solveCaptcha: true,
        captchaTimeout: 30000,
        waitForTimeout: 10000
      });

      // Should succeed regardless of whether CAPTCHA was present
      expect(result.error).toBeUndefined();
      expect(result.statusCode).toBe(200);
    }, 120000);

    it('should take screenshots when requested', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      const testUrl = 'https://geo.brdtest.com/mygeo.json';
      
      const result = await client.scrapeUrl(testUrl, {
        takeScreenshot: true,
        waitForTimeout: 3000
      });

      expect(result.error).toBeUndefined();
      expect(result.screenshot).toBeDefined();
      expect(result.screenshot).toBeInstanceOf(Buffer);
      expect(result.screenshot!.length).toBeGreaterThan(0);
    }, 60000);

    it('should block resources effectively', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      const testUrl = 'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa';
      
      // Test without blocking
      const resultWithoutBlocking = await client.scrapeUrl(testUrl, {
        waitForTimeout: 5000
      });

      // Test with blocking
      const resultWithBlocking = await client.scrapeUrl(testUrl, {
        waitForTimeout: 5000,
        blockResources: ['image', 'stylesheet', 'font', 'media']
      });

      expect(resultWithoutBlocking.error).toBeUndefined();
      expect(resultWithBlocking.error).toBeUndefined();
      
      // Blocking should generally result in faster load times
      // (though this isn't guaranteed due to network variability)
      expect(resultWithBlocking.loadTime).toBeLessThan(resultWithoutBlocking.loadTime * 2);
    }, 120000);
  });

  describe('Performance and Reliability', () => {
    it('should maintain consistent performance across multiple requests', async () => {
      if (!runIntegrationTests || !hasCredentials) {
        pending('Integration test skipped');
        return;
      }

      const testUrl = 'https://geo.brdtest.com/mygeo.json';
      const numRequests = 3;
      const loadTimes: number[] = [];

      for (let i = 0; i < numRequests; i++) {
        const result = await client.scrapeUrl(testUrl, {
          waitForTimeout: 2000
        });
        
        expect(result.error).toBeUndefined();
        expect(result.statusCode).toBe(200);
        loadTimes.push(result.loadTime);

        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Check that load times are reasonable and consistent
      const avgLoadTime = loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length;
      expect(avgLoadTime).toBeLessThan(30000); // Should average less than 30 seconds
      
      // No load time should be more than 3x the average (indicating a major issue)
      loadTimes.forEach(time => {
        expect(time).toBeLessThan(avgLoadTime * 3);
      });
    }, 180000);
  });
});

/**
 * Manual test runner for integration tests
 * Run with: npm run test:integration
 */
export const runManualIntegrationTests = async () => {
  console.log('🧪 Running Brightdata Integration Tests');
  console.log('Make sure to set BRIGHTDATA_USERNAME and BRIGHTDATA_PASSWORD environment variables');
  
  if (!process.env.BRIGHTDATA_USERNAME || !process.env.BRIGHTDATA_PASSWORD) {
    console.error('❌ Missing Brightdata credentials');
    return;
  }

  const client = new BrightdataClient({
    username: process.env.BRIGHTDATA_USERNAME,
    password: process.env.BRIGHTDATA_PASSWORD,
    zone: 'browser_api',
    country: 'US'
  });

  try {
    console.log('🔍 Testing connection...');
    const connectionTest = await client.testConnection();
    console.log(`Connection: ${connectionTest ? '✅ Success' : '❌ Failed'}`);

    if (connectionTest) {
      console.log('📊 Getting session info...');
      const sessionInfo = await client.getSessionInfo();
      console.log('Session Info:', sessionInfo);

      console.log('🌐 Testing DFSA scraping...');
      const dfsaResult = await client.scrapeUrl(
        'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa',
        {
          waitForTimeout: 10000,
          blockResources: ['image', 'stylesheet'],
          solveCaptcha: true
        }
      );

      console.log('DFSA Scraping Result:');
      console.log(`  Status: ${dfsaResult.error ? '❌ Failed' : '✅ Success'}`);
      console.log(`  Status Code: ${dfsaResult.statusCode}`);
      console.log(`  Title: ${dfsaResult.title}`);
      console.log(`  Content Length: ${dfsaResult.content.length} chars`);
      console.log(`  Load Time: ${dfsaResult.loadTime}ms`);
      if (dfsaResult.error) {
        console.log(`  Error: ${dfsaResult.error}`);
      }
    }
  } catch (error) {
    console.error('❌ Integration test failed:', error);
  } finally {
    await client.close();
  }
};

// Run manual tests if this file is executed directly
if (require.main === module) {
  runManualIntegrationTests().catch(console.error);
}