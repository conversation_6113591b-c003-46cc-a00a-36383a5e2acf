import { DFSAURLDiscoveryService } from '../../scraping/url-discovery-service';

// Mock puppeteer for testing
jest.mock('puppeteer', () => ({
  launch: jest.fn(() => Promise.resolve({
    newPage: jest.fn(() => Promise.resolve({
      setUserAgent: jest.fn(),
      goto: jest.fn(),
      evaluate: jest.fn(() => Promise.resolve([
        'https://dfsaen.thomsonreuters.com/rulebook/general-module',
        'https://dfsaen.thomsonreuters.com/rulebook/conduct-of-business',
        'https://dfsaen.thomsonreuters.com/rules/general-provisions'
      ])),
      close: jest.fn()
    })),
    close: jest.fn()
  }))
}));

// Mock fetch for robots.txt and sitemap requests
global.fetch = jest.fn();

describe('DFSAURLDiscoveryService Integration', () => {
  let service: DFSAURLDiscoveryService;

  beforeEach(() => {
    service = new DFSAURLDiscoveryService({
      maxUrls: 100,
      maxDepth: 2,
      crawlDelay: 100 // Faster for testing
    });
    jest.clearAllMocks();
  });

  describe('discoverComprehensive', () => {
    it('should discover URLs comprehensively', async () => {
      // Mock robots.txt response
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(`
User-agent: *
Allow: /
Sitemap: https://dfsaen.thomsonreuters.com/sitemap.xml
          `.trim())
        })
        // Mock sitemap response
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve(`
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://dfsaen.thomsonreuters.com/rulebook/general-module</loc>
    <lastmod>2024-01-01</lastmod>
  </url>
  <url>
    <loc>https://dfsaen.thomsonreuters.com/rulebook/conduct-of-business</loc>
    <lastmod>2024-01-02</lastmod>
  </url>
</urlset>
          `.trim())
        });

      const urls = await service.discoverComprehensive('https://dfsaen.thomsonreuters.com');

      expect(urls).toBeInstanceOf(Array);
      expect(urls.length).toBeGreaterThan(0);
      
      // Should contain URLs from both sitemap and crawling
      urls.forEach(url => {
        expect(url).toMatch(/^https:\/\/dfsaen\.thomsonreuters\.com/);
        expect(url).toMatch(/\/(rulebook|rules|guidance|consultation|policy)\//);
      });
    });

    it('should handle robots.txt disallow', async () => {
      // Mock robots.txt that disallows crawling
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(`
User-agent: *
Disallow: /
        `.trim())
      });

      const urls = await service.discoverComprehensive('https://dfsaen.thomsonreuters.com');

      expect(urls).toEqual([]);
    });

    it('should handle missing robots.txt gracefully', async () => {
      // Mock 404 response for robots.txt
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404
      });

      const urls = await service.discoverComprehensive('https://dfsaen.thomsonreuters.com');

      expect(urls).toBeInstanceOf(Array);
      // Should still discover URLs through crawling
    });
  });

  describe('discoverRecursive', () => {
    it('should discover URLs recursively', async () => {
      // Mock robots.txt response
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(`
User-agent: *
Allow: /
        `.trim())
      });

      const discoveredUrls: string[] = [];
      
      for await (const url of service.discoverRecursive('https://dfsaen.thomsonreuters.com/rulebook')) {
        discoveredUrls.push(url);
        
        // Limit for testing
        if (discoveredUrls.length >= 5) {
          break;
        }
      }

      expect(discoveredUrls.length).toBeGreaterThan(0);
      discoveredUrls.forEach(url => {
        expect(url).toMatch(/^https:\/\/dfsaen\.thomsonreuters\.com/);
      });
    });

    it('should respect robots.txt disallow in recursive mode', async () => {
      // Mock robots.txt that disallows crawling
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(`
User-agent: *
Disallow: /
        `.trim())
      });

      const discoveredUrls: string[] = [];
      
      for await (const url of service.discoverRecursive('https://dfsaen.thomsonreuters.com/rulebook')) {
        discoveredUrls.push(url);
      }

      expect(discoveredUrls).toEqual([]);
    });
  });
});