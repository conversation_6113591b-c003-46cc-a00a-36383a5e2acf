import { RobotsParser } from '../../scraping/robots-parser';

// Mock fetch for testing
global.fetch = jest.fn();

describe('RobotsParser', () => {
  let parser: RobotsParser;

  beforeEach(() => {
    parser = new RobotsParser('DFSA-Scraper/1.0');
    jest.clearAllMocks();
  });

  describe('parseRobotsTxt', () => {
    it('should parse robots.txt with allow rules', async () => {
      const robotsContent = `
User-agent: *
Allow: /
Crawl-delay: 1
Sitemap: https://example.com/sitemap.xml
      `.trim();

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(robotsContent)
      });

      const result = await parser.parseRobotsTxt('https://example.com');

      expect(result.allowed).toBe(true);
      expect(result.crawlDelay).toBe(1000); // Should be converted to milliseconds
      expect(result.sitemapUrls).toContain('https://example.com/sitemap.xml');
    });

    it('should parse robots.txt with disallow rules', async () => {
      const robotsContent = `
User-agent: *
Disallow: /
      `.trim();

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(robotsContent)
      });

      const result = await parser.parseRobotsTxt('https://example.com');

      expect(result.allowed).toBe(false);
      expect(result.sitemapUrls).toEqual([]);
    });

    it('should handle specific user agent rules', async () => {
      const robotsContent = `
User-agent: DFSA-Scraper
Allow: /
Crawl-delay: 2

User-agent: *
Disallow: /
      `.trim();

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(robotsContent)
      });

      const result = await parser.parseRobotsTxt('https://example.com');

      expect(result.allowed).toBe(true);
      expect(result.crawlDelay).toBe(2000);
    });

    it('should handle missing robots.txt', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404
      });

      const result = await parser.parseRobotsTxt('https://example.com');

      expect(result.allowed).toBe(true); // Default to allowed
      expect(result.sitemapUrls).toEqual([]);
    });

    it('should handle fetch errors gracefully', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      const result = await parser.parseRobotsTxt('https://example.com');

      expect(result.allowed).toBe(true); // Default to allowed on error
      expect(result.sitemapUrls).toEqual([]);
    });

    it('should extract multiple sitemap URLs', async () => {
      const robotsContent = `
User-agent: *
Allow: /
Sitemap: https://example.com/sitemap1.xml
Sitemap: https://example.com/sitemap2.xml
Sitemap: https://example.com/sitemap-index.xml
      `.trim();

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(robotsContent)
      });

      const result = await parser.parseRobotsTxt('https://example.com');

      expect(result.sitemapUrls).toHaveLength(3);
      expect(result.sitemapUrls).toContain('https://example.com/sitemap1.xml');
      expect(result.sitemapUrls).toContain('https://example.com/sitemap2.xml');
      expect(result.sitemapUrls).toContain('https://example.com/sitemap-index.xml');
    });

    it('should ignore comments and empty lines', async () => {
      const robotsContent = `
# This is a comment
User-agent: *

# Another comment
Allow: /

Sitemap: https://example.com/sitemap.xml
      `.trim();

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(robotsContent)
      });

      const result = await parser.parseRobotsTxt('https://example.com');

      expect(result.allowed).toBe(true);
      expect(result.sitemapUrls).toContain('https://example.com/sitemap.xml');
    });
  });
});