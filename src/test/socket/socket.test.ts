import { Server } from 'socket.io';
import { createServer } from 'http';
import { AddressInfo } from 'net';
import { io as ioc, Socket as ClientSocket } from 'socket.io-client';
import { setupSocketHandlers, getActiveConnections } from '../../socket';

// Mock the scraping orchestrator
jest.mock('../../scraping/scraping-orchestrator', () => {
  return {
    ScrapingOrchestrator: jest.fn().mockImplementation(() => {
      return {
        jobManager: {
          setCallbacks: jest.fn(),
        },
        getJobStatus: jest.fn().mockImplementation((jobId) => {
          if (jobId === 'test-job-id') {
            return Promise.resolve({
              id: 'test-job-id',
              status: 'running',
              progress: { percentage: 50 },
            });
          }
          return Promise.resolve(null);
        }),
        getJobs: jest.fn().mockResolvedValue([
          {
            id: 'test-job-id',
            status: 'running',
            progress: { percentage: 50 },
          },
        ]),
        cancelJob: jest.fn().mockImplementation((jobId) => {
          return Promise.resolve(jobId === 'test-job-id');
        }),
        retryJob: jest.fn().mockImplementation((jobId) => {
          return Promise.resolve(jobId === 'test-job-id');
        }),
        getQueueStats: jest.fn().mockResolvedValue({
          waiting: 1,
          active: 2,
          completed: 3,
          failed: 0,
        }),
      };
    }),
  };
});

describe('Socket.IO Integration', () => {
  let io: Server;
  let clientSocket: ClientSocket;
  let httpServer: any;

  beforeAll((done) => {
    // Create HTTP server
    httpServer = createServer();
    
    // Create Socket.IO server
    io = new Server(httpServer);
    
    // Setup Socket.IO handlers
    setupSocketHandlers(io);
    
    // Start server
    httpServer.listen(() => {
      const port = (httpServer.address() as AddressInfo).port;
      clientSocket = ioc(`http://localhost:${port}`);
      clientSocket.on('connect', done);
    });
  });

  afterAll(() => {
    io.close();
    clientSocket.close();
    httpServer.close();
  });

  test('should connect and disconnect', (done) => {
    expect(clientSocket.connected).toBe(true);
    
    // Check if connection is tracked
    expect(getActiveConnections().size).toBe(1);
    
    // Disconnect
    clientSocket.disconnect();
    
    // Wait for disconnect to be processed
    setTimeout(() => {
      expect(getActiveConnections().size).toBe(0);
      
      // Reconnect for other tests
      clientSocket.connect();
      setTimeout(done, 50);
    }, 50);
  });

  test('should get job status', (done) => {
    clientSocket.emit('get:job', 'test-job-id', (response: any) => {
      expect(response.success).toBe(true);
      expect(response.job.id).toBe('test-job-id');
      expect(response.job.status).toBe('running');
      done();
    });
  });

  test('should get all jobs', (done) => {
    clientSocket.emit('get:jobs', {}, (response: any) => {
      expect(response.success).toBe(true);
      expect(response.jobs).toHaveLength(1);
      expect(response.jobs[0].id).toBe('test-job-id');
      done();
    });
  });

  test('should get queue stats', (done) => {
    clientSocket.emit('get:stats', (response: any) => {
      expect(response.success).toBe(true);
      expect(response.stats).toHaveProperty('waiting', 1);
      expect(response.stats).toHaveProperty('active', 2);
      expect(response.stats).toHaveProperty('completed', 3);
      expect(response.stats).toHaveProperty('failed', 0);
      done();
    });
  });

  test('should subscribe to job updates', (done) => {
    // Subscribe to job updates
    clientSocket.emit('subscribe:job', 'test-job-id');
    
    // Listen for job status
    clientSocket.once('job:status', (data) => {
      expect(data.id).toBe('test-job-id');
      expect(data.status).toBe('running');
      
      // Unsubscribe
      clientSocket.emit('unsubscribe:job', 'test-job-id');
      done();
    });
  });

  test('should cancel job', (done) => {
    clientSocket.emit('cancel:job', 'test-job-id', (response: any) => {
      expect(response.success).toBe(true);
      done();
    });
  });

  test('should retry job', (done) => {
    clientSocket.emit('retry:job', 'test-job-id', (response: any) => {
      expect(response.success).toBe(true);
      done();
    });
  });
});