/**
 * API Tests
 * 
 * Tests for the REST API endpoints
 */

import request from 'supertest';
import app from '../../index';
import { repositories } from '../../database/init';

// Mock the repositories and services
jest.mock('../../database/init', () => ({
  repositories: {
    systemConfig: {
      getAllConfigs: jest.fn().mockResolvedValue([
        { key: 'test.key', value: 'test-value', description: 'Test config' }
      ]),
      findByKey: jest.fn().mockImplementation((key) => {
        if (key === 'test.key') {
          return Promise.resolve({ key, value: 'test-value', description: 'Test config' });
        }
        return Promise.resolve(null);
      }),
      setValue: jest.fn().mockImplementation((key, value, description) => {
        return Promise.resolve({ key, value, description });
      }),
      getMultipleValues: jest.fn().mockResolvedValue({
        'test.key': 'test-value'
      }),
      deleteByKey: jest.fn().mockResolvedValue(undefined),
      bulkSet: jest.fn().mockImplementation((configs) => {
        return Promise.resolve(configs.map(c => ({ key: c.key, value: c.value, description: c.description })));
      }),
      getScrapingConfig: jest.fn().mockResolvedValue({
        'scraping.max_concurrent': 5,
        'scraping.default_delay': 1000
      }),
      getRagConfig: jest.fn().mockResolvedValue({
        'rag.chunk_size': 1000,
        'rag.chunk_overlap': 200
      })
    },
    initialize: jest.fn().mockResolvedValue(undefined)
  }
}));

jest.mock('../../scraping/scraping-orchestrator', () => {
  return {
    ScrapingOrchestrator: jest.fn().mockImplementation(() => {
      return {
        startScraping: jest.fn().mockResolvedValue({ 
          jobId: 'test-job-id', 
          testResult: { approved: true, qualityScore: 0.9 } 
        }),
        getJobStatus: jest.fn().mockResolvedValue({
          id: 'test-job-id',
          status: 'running',
          progress: { percentage: 50 }
        }),
        getJobs: jest.fn().mockResolvedValue([
          { id: 'test-job-id', status: 'running' }
        ]),
        cancelJob: jest.fn().mockResolvedValue(true),
        retryJob: jest.fn().mockResolvedValue(true),
        getQueueStats: jest.fn().mockResolvedValue({
          waiting: 1,
          active: 2,
          completed: 3,
          failed: 0
        })
      };
    })
  };
});

jest.mock('../../rag/rag-orchestrator', () => {
  return {
    RAGOrchestrator: jest.fn().mockImplementation(() => {
      return {
        initialize: jest.fn().mockResolvedValue(undefined),
        processQuery: jest.fn().mockResolvedValue({
          query: 'test query',
          answer: 'test answer',
          sources: [{ title: 'Source 1', url: 'http://example.com' }],
          confidence: 0.9,
          provider: 'openai'
        }),
        createConversation: jest.fn().mockResolvedValue('test-conversation-id'),
        getConversation: jest.fn().mockResolvedValue({
          id: 'test-conversation-id',
          messages: []
        }),
        getMetrics: jest.fn().mockReturnValue({
          totalQueries: 10,
          averageResponseTime: 500,
          averageConfidence: 0.85
        }),
        resetMetrics: jest.fn()
      };
    })
  };
});

// Mock other dependencies
jest.mock('../../vector-database/vector-store-service', () => {
  return {
    VectorStoreService: jest.fn().mockImplementation(() => ({}))
  };
});

jest.mock('../../llm/llm-service', () => {
  return {
    LLMService: jest.fn().mockImplementation(() => ({}))
  };
});

jest.mock('../../rag/query-processor', () => {
  return {
    QueryProcessor: jest.fn().mockImplementation(() => ({}))
  };
});

jest.mock('../../rag/context-assembler', () => {
  return {
    ContextAssembler: jest.fn().mockImplementation(() => ({}))
  };
});

jest.mock('../../rag/conversation-manager', () => {
  return {
    ConversationManager: jest.fn().mockImplementation(() => ({}))
  };
});

describe('API Routes', () => {
  // Health check endpoint
  describe('GET /health', () => {
    it('should return healthy status', async () => {
      const response = await request(app).get('/health');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'healthy');
    });
  });

  // Root endpoint
  describe('GET /', () => {
    it('should return API information', async () => {
      const response = await request(app).get('/');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('endpoints');
    });
  });

  // API documentation endpoint
  describe('GET /api-docs', () => {
    it('should return API documentation', async () => {
      const response = await request(app).get('/api-docs');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('openapi');
      expect(response.body).toHaveProperty('info');
      expect(response.body).toHaveProperty('paths');
    });
  });

  // Scraping endpoints
  describe('Scraping API', () => {
    it('should validate scraping request body', async () => {
      const response = await request(app)
        .post('/api/scraping/start')
        .send({
          // Missing required fields
        });
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Validation Error');
    });

    it('should start a scraping job with valid input', async () => {
      const response = await request(app)
        .post('/api/scraping/start')
        .send({
          baseUrl: 'https://example.com',
          mode: 'recursive',
          maxDepth: 3,
          delay: 1000
        });
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('jobId', 'test-job-id');
    });

    it('should get job status', async () => {
      const response = await request(app)
        .get('/api/scraping/job/test-job-id');
      expect(response.status).toBe(200);
      expect(response.body.job).toHaveProperty('id', 'test-job-id');
    });

    it('should get all jobs', async () => {
      const response = await request(app)
        .get('/api/scraping/jobs');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('jobs');
      expect(Array.isArray(response.body.jobs)).toBe(true);
    });

    it('should cancel a job', async () => {
      const response = await request(app)
        .post('/api/scraping/job/test-job-id/cancel');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'success');
    });

    it('should retry a job', async () => {
      const response = await request(app)
        .post('/api/scraping/job/test-job-id/retry');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'success');
    });

    it('should get queue stats', async () => {
      const response = await request(app)
        .get('/api/scraping/stats');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('stats');
    });
  });

  // Query endpoints
  describe('Query API', () => {
    it('should validate query request body', async () => {
      const response = await request(app)
        .post('/api/query')
        .send({
          // Missing required fields
        });
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Validation Error');
    });

    it('should process a valid query', async () => {
      const response = await request(app)
        .post('/api/query')
        .send({
          query: 'What are the DFSA regulations?'
        });
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('response');
      expect(response.body.response).toHaveProperty('answer');
    });

    it('should create a new conversation', async () => {
      const response = await request(app)
        .post('/api/query/conversation');
      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('conversationId');
    });

    it('should get conversation history', async () => {
      const response = await request(app)
        .get('/api/query/conversation/test-conversation-id');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('conversation');
    });

    it('should get RAG metrics', async () => {
      const response = await request(app)
        .get('/api/query/metrics');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('metrics');
      expect(response.body.metrics).toHaveProperty('totalQueries');
    });

    it('should reset RAG metrics', async () => {
      const response = await request(app)
        .post('/api/query/metrics/reset');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'success');
    });
  });

  // Config endpoints
  describe('Config API', () => {
    it('should validate config request body', async () => {
      const response = await request(app)
        .post('/api/config')
        .send({
          // Missing required fields
        });
      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error', 'Validation Error');
    });

    it('should get all configs', async () => {
      const response = await request(app)
        .get('/api/config');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('configs');
      expect(Array.isArray(response.body.configs)).toBe(true);
    });

    it('should get config by key', async () => {
      const response = await request(app)
        .get('/api/config/test.key');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('config');
      expect(response.body.config).toHaveProperty('key', 'test.key');
    });

    it('should return 404 for non-existent config key', async () => {
      const response = await request(app)
        .get('/api/config/non-existent-key');
      expect(response.status).toBe(404);
    });

    it('should set config value', async () => {
      const response = await request(app)
        .post('/api/config')
        .send({
          key: 'new.key',
          value: 'new-value',
          description: 'New config'
        });
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('config');
    });

    it('should bulk set config values', async () => {
      const response = await request(app)
        .post('/api/config/bulk')
        .send({
          configs: [
            { key: 'bulk.key1', value: 'value1' },
            { key: 'bulk.key2', value: 'value2' }
          ]
        });
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('count', 2);
    });

    it('should delete config by key', async () => {
      const response = await request(app)
        .delete('/api/config/test.key');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'success');
    });

    it('should get scraping config', async () => {
      const response = await request(app)
        .get('/api/config/category/scraping');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('config');
    });

    it('should get RAG config', async () => {
      const response = await request(app)
        .get('/api/config/category/rag');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('config');
    });

    it('should get AI model config', async () => {
      const response = await request(app)
        .get('/api/config/category/ai');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('config');
    });
  });
});