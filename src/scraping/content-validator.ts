import { 
  ScrapedContent, 
  QualityMetrics, 
  ValidationResult, 
  ContentType, 
  QualityIssue 
} from '../types';
import { logger } from '../utils/logger';

export interface ContentValidatorConfig {
  minWordCount: number;
  maxWordCount: number;
  minContentLength: number;
  requiredElements: string[];
  blockedElements: string[];
  qualityThreshold: number;
}

/**
 * ContentValidator - Validates scraped content quality and completeness
 * Implements quality metrics calculation and content type detection
 */
export class ContentValidator {
  private config: ContentValidatorConfig;

  constructor(config: Partial<ContentValidatorConfig> = {}) {
    this.config = {
      minWordCount: 50,
      maxWordCount: 50000,
      minContentLength: 200,
      requiredElements: ['title', 'content'],
      blockedElements: ['script', 'style', 'nav', 'footer'],
      qualityThreshold: 0.7,
      ...config
    };
  }

  /**
   * Validate scraped content and calculate quality metrics
   */
  async validateContent(content: ScrapedContent): Promise<ValidationResult> {
    logger.info('Validating content', { url: content.url });

    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Basic validation checks
      this.validateBasicStructure(content, errors, warnings);
      
      // Content quality checks
      this.validateContentQuality(content, errors, warnings);
      
      // DFSA-specific validation
      this.validateDFSAContent(content, errors, warnings);
      
      // Calculate quality metrics
      const metrics = this.calculateQualityMetrics(content);
      
      const isValid = errors.length === 0 && metrics.extractionConfidence >= this.config.qualityThreshold;

      logger.info('Content validation completed', {
        url: content.url,
        isValid,
        errorCount: errors.length,
        warningCount: warnings.length,
        extractionConfidence: metrics.extractionConfidence
      });

      return {
        isValid,
        errors,
        warnings,
        metrics
      };

    } catch (error) {
      logger.error('Content validation failed', { url: content.url, error });
      return {
        isValid: false,
        errors: [`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings,
        metrics: this.getDefaultMetrics()
      };
    }
  }

  /**
   * Detect content type based on content analysis
   */
  detectContentType(content: string, url: string): ContentType {
    // Check for PDF indicators
    if (url.toLowerCase().includes('.pdf') || content.includes('%PDF-')) {
      return ContentType.PDF;
    }

    // Check for table structures
    if (this.hasTableStructure(content)) {
      return ContentType.TABLE;
    }

    // Check for image content
    if (this.hasImageContent(content)) {
      return ContentType.IMAGE;
    }

    // Check for HTML structure
    if (content.includes('<html') || content.includes('<!DOCTYPE')) {
      return ContentType.HTML;
    }

    // Default to text
    return ContentType.TEXT;
  }

  /**
   * Calculate comprehensive quality metrics
   */
  calculateQualityMetrics(content: ScrapedContent): QualityMetrics {
    const cleanText = this.cleanText(content.content);
    const wordCount = this.getWordCount(cleanText);
    
    return {
      wordCount,
      structureCompleteness: this.calculateStructureCompleteness(content),
      extractionConfidence: this.calculateExtractionConfidence(content),
      contentTypeAccuracy: this.calculateContentTypeAccuracy(content),
      validationErrors: this.getValidationErrors(content)
    };
  }

  /**
   * Validate content against quality gates
   */
  async validateQualityGate(contents: ScrapedContent[]): Promise<{
    passed: boolean;
    overallScore: number;
    issues: QualityIssue[];
    recommendations: string[];
  }> {
    logger.info('Running quality gate validation', { contentCount: contents.length });

    const issues: QualityIssue[] = [];
    const recommendations: string[] = [];
    let totalScore = 0;

    for (const content of contents) {
      const validation = await this.validateContent(content);
      totalScore += validation.metrics.extractionConfidence;

      // Check for critical issues
      if (validation.errors.length > 0) {
        issues.push({
          type: 'extraction',
          severity: 'high',
          description: `Content extraction failed: ${validation.errors.join(', ')}`,
          affectedUrls: [content.url]
        });
      }

      // Check for content quality issues
      if (validation.metrics.wordCount < this.config.minWordCount) {
        issues.push({
          type: 'content',
          severity: 'medium',
          description: `Low word count (${validation.metrics.wordCount} words)`,
          affectedUrls: [content.url]
        });
      }

      // Check for structure issues
      if (validation.metrics.structureCompleteness < 0.5) {
        issues.push({
          type: 'structure',
          severity: 'medium',
          description: 'Poor content structure detected',
          affectedUrls: [content.url]
        });
      }
    }

    const overallScore = contents.length > 0 ? totalScore / contents.length : 0;
    const passed = overallScore >= this.config.qualityThreshold && 
                   issues.filter(i => i.severity === 'high').length === 0;

    // Generate recommendations
    if (!passed) {
      recommendations.push('Consider adjusting scraping parameters');
      recommendations.push('Review content extraction logic');
      
      if (issues.some(i => i.type === 'content')) {
        recommendations.push('Check for content blocking or access restrictions');
      }
      
      if (issues.some(i => i.type === 'structure')) {
        recommendations.push('Verify CSS selectors and content parsing');
      }
    }

    logger.info('Quality gate validation completed', {
      passed,
      overallScore,
      issueCount: issues.length,
      highSeverityIssues: issues.filter(i => i.severity === 'high').length
    });

    return {
      passed,
      overallScore,
      issues,
      recommendations
    };
  }

  // Private helper methods

  private validateBasicStructure(content: ScrapedContent, errors: string[], warnings: string[]): void {
    if (!content.url) {
      errors.push('Missing URL');
    }

    if (!content.title || content.title.trim().length === 0) {
      warnings.push('Missing or empty title');
    }

    if (!content.content || content.content.trim().length < this.config.minContentLength) {
      errors.push(`Content too short (${content.content?.length || 0} characters, minimum ${this.config.minContentLength})`);
    }

    if (content.content && content.content.length > this.config.maxWordCount * 10) {
      warnings.push('Content unusually long, may contain unwanted elements');
    }
  }

  private validateContentQuality(content: ScrapedContent, errors: string[], warnings: string[]): void {
    const cleanText = this.cleanText(content.content);
    const wordCount = this.getWordCount(cleanText);

    if (wordCount < this.config.minWordCount) {
      errors.push(`Word count too low (${wordCount} words, minimum ${this.config.minWordCount})`);
    }

    if (wordCount > this.config.maxWordCount) {
      warnings.push(`Word count very high (${wordCount} words), may contain noise`);
    }

    // Check for blocked elements
    for (const blockedElement of this.config.blockedElements) {
      const regex = new RegExp(`<${blockedElement}[^>]*>`, 'gi');
      if (regex.test(content.content)) {
        warnings.push(`Contains blocked element: ${blockedElement}`);
      }
    }

    // Check for repetitive content
    if (this.hasRepetitiveContent(cleanText)) {
      warnings.push('Content appears repetitive or contains duplicate sections');
    }
  }

  private validateDFSAContent(content: ScrapedContent, errors: string[], warnings: string[]): void {
    const lowerContent = content.content.toLowerCase();
    const lowerTitle = content.title.toLowerCase();

    // Check for DFSA-specific indicators
    const dfsaIndicators = [
      'dfsa', 'dubai financial services', 'rulebook', 'regulation',
      'financial services authority', 'compliance', 'regulatory'
    ];

    const hasIndicators = dfsaIndicators.some(indicator => 
      lowerContent.includes(indicator) || lowerTitle.includes(indicator)
    );

    if (!hasIndicators) {
      warnings.push('Content may not be DFSA-related (no regulatory indicators found)');
    }

    // Check for common DFSA page elements
    const expectedElements = ['rule', 'section', 'chapter', 'module', 'guidance'];
    const hasExpectedElements = expectedElements.some(element => 
      lowerContent.includes(element)
    );

    if (!hasExpectedElements && content.url.includes('dfsaen.thomsonreuters.com')) {
      warnings.push('Missing expected DFSA structural elements');
    }
  }

  private calculateStructureCompleteness(content: ScrapedContent): number {
    let score = 0;
    const maxScore = 10;

    // Check for title
    if (content.title && content.title.trim().length > 0) score += 2;

    // Check for headings
    if (/<h[1-6][^>]*>/i.test(content.content)) score += 2;

    // Check for paragraphs
    if (/<p[^>]*>/i.test(content.content)) score += 1;

    // Check for lists
    if (/<[uo]l[^>]*>/i.test(content.content)) score += 1;

    // Check for sections/divs
    if (/<(section|div)[^>]*>/i.test(content.content)) score += 1;

    // Check for metadata
    if (content.metadata && content.metadata.sectionHierarchy.length > 0) score += 2;

    // Check for proper content length
    if (content.content.length > 1000) score += 1;

    return Math.min(score / maxScore, 1);
  }

  private calculateExtractionConfidence(content: ScrapedContent): number {
    let confidence = 0.5; // Base confidence

    // Boost confidence for good indicators
    if (content.title && content.title.length > 10) confidence += 0.1;
    if (this.getWordCount(content.content) > 100) confidence += 0.1;
    if (/<h[1-6][^>]*>/i.test(content.content)) confidence += 0.1;
    if (content.metadata?.sectionHierarchy.length > 0) confidence += 0.1;

    // Reduce confidence for bad indicators
    if (content.content.includes('404') || content.content.includes('not found')) confidence -= 0.3;
    if (content.content.includes('access denied') || content.content.includes('forbidden')) confidence -= 0.3;
    if (this.getWordCount(content.content) < 50) confidence -= 0.2;

    return Math.max(0, Math.min(1, confidence));
  }

  private calculateContentTypeAccuracy(content: ScrapedContent): number {
    const detectedType = this.detectContentType(content.content, content.url);
    return detectedType === content.contentType ? 1.0 : 0.5;
  }

  private getValidationErrors(content: ScrapedContent): string[] {
    const errors: string[] = [];
    
    if (!content.title) errors.push('Missing title');
    if (content.content.length < 100) errors.push('Content too short');
    if (this.hasRepetitiveContent(content.content)) errors.push('Repetitive content detected');
    
    return errors;
  }

  private hasTableStructure(content: string): boolean {
    return /<table[^>]*>/i.test(content) || 
           /<th[^>]*>/i.test(content) || 
           /<td[^>]*>/i.test(content);
  }

  private hasImageContent(content: string): boolean {
    return /<img[^>]*>/i.test(content) || 
           content.includes('data:image/') ||
           /\.(jpg|jpeg|png|gif|svg|webp)/i.test(content);
  }

  private cleanText(content: string): string {
    return content
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private getWordCount(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  private hasRepetitiveContent(text: string): boolean {
    const words = text.toLowerCase().split(/\s+/);
    if (words.length < 20) return false;

    // Check for repeated phrases
    const phrases = new Map<string, number>();
    for (let i = 0; i < words.length - 2; i++) {
      const phrase = words.slice(i, i + 3).join(' ');
      phrases.set(phrase, (phrases.get(phrase) || 0) + 1);
    }

    // If any 3-word phrase appears more than 3 times, consider it repetitive
    return Array.from(phrases.values()).some(count => count > 3);
  }

  private getDefaultMetrics(): QualityMetrics {
    return {
      wordCount: 0,
      structureCompleteness: 0,
      extractionConfidence: 0,
      contentTypeAccuracy: 0,
      validationErrors: ['Validation failed']
    };
  }
}