import { EventEmitter } from 'events';
import { BrightdataClient } from './brightdata-client';
import { DFSAURLDiscoveryService } from './url-discovery-service';
import { QualityAssurance } from './quality-assurance';
import { ScrapingJobManager, ScrapeJobData } from './scraping-job-manager';
import { 
  ScrapeJob, 
  ScrapeConfig, 
  ScrapedContent, 
  JobStatus,
  JobProgress,
  ScrapeError,
  TestScrapeResult,
  QualityGateResult,
  ContentType,
  ContentStatus,
  ContentMetadata
} from '../types';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';
import { Job } from 'bull';

export interface OrchestratorConfig {
  brightdata: {
    username: string;
    password: string;
    zone?: string;
    country?: string;
  };
  scraping: {
    maxConcurrency: number;
    delayBetweenRequests: number;
    retryAttempts: number;
    qualityGateThreshold: number;
    testScrapeSize: number;
    timeout: number;
  };
  jobManager: {
    concurrency: number;
    retryAttempts: number;
    retryDelay: number;
  };
}

export interface ScrapingProgress {
  totalUrls: number;
  completedUrls: number;
  failedUrls: number;
  currentUrl: string;
  percentage: number;
}

export interface ScrapingResult {
  jobId: string;
  totalUrls: number;
  successfulUrls: number;
  failedUrls: number;
  results: ScrapedContent[];
  errors: ScrapeError[];
  duration: number;
}

/**
 * ScrapingOrchestrator - Manages the complete scraping workflow
 * Coordinates URL discovery, quality assurance, job management, and content extraction
 */
export class ScrapingOrchestrator extends EventEmitter {
  private brightdataClient: BrightdataClient;
  private urlDiscoveryService: DFSAURLDiscoveryService;
  private qualityAssurance: QualityAssurance;
  public jobManager: ScrapingJobManager;
  private config: OrchestratorConfig;

  constructor(config: OrchestratorConfig) {
    super();
    this.config = config;

    // Initialize components
    this.brightdataClient = new BrightdataClient({
      username: config.brightdata.username,
      password: config.brightdata.password,
      zone: config.brightdata.zone || 'scraping_browser2',
      country: config.brightdata.country || 'US',
      timeout: config.scraping.timeout,
      rateLimitDelay: config.scraping.delayBetweenRequests
    });

    this.urlDiscoveryService = new DFSAURLDiscoveryService({
      maxUrls: 10000,
      maxDepth: 5,
      crawlDelay: config.scraping.delayBetweenRequests,
      respectRobotsTxt: true
    });
    
    this.qualityAssurance = new QualityAssurance(this.brightdataClient, {
      testScrapeSize: config.scraping.testScrapeSize,
      qualityThreshold: config.scraping.qualityGateThreshold,
      requireManualConfirmation: true
    });

    this.jobManager = new ScrapingJobManager({
      concurrency: config.jobManager.concurrency,
      retryAttempts: config.jobManager.retryAttempts,
      retryDelay: config.jobManager.retryDelay
    });

    this.setupJobProcessor();
    this.setupJobCallbacks();
  }

  /**
   * Start the complete scraping workflow
   */
  async startScraping(
    baseUrl: string, 
    config: ScrapeConfig
  ): Promise<{ jobId: string; testResult?: TestScrapeResult }> {
    logger.info('Starting scraping workflow', { baseUrl, config });

    try {
      // Step 1: URL Discovery
      logger.info('Step 1: Discovering URLs', { mode: config.mode });
      const urls = await this.discoverUrls(baseUrl, config);
      logger.info('URL discovery completed', { totalUrls: urls.length });

      // Step 2: Test Scraping and Quality Assurance
      logger.info('Step 2: Performing test scraping and quality assurance');
      const qaResult = await this.qualityAssurance.performTestScrapeWorkflow(urls);

      if (!qaResult.approved) {
        logger.warn('Test scraping not approved, stopping workflow');
        return { jobId: '', testResult: qaResult.testResult };
      }

      logger.info('Test scraping approved, proceeding with full scraping');

      // Step 3: Create and Queue Full Scraping Job
      logger.info('Step 3: Creating full scraping job');
      const job = await this.jobManager.createJob(urls, config);

      logger.info('Scraping workflow initiated', {
        jobId: job.id,
        totalUrls: urls.length,
        testQualityScore: qaResult.testResult.qualityScore
      });

      return { jobId: job.id, testResult: qaResult.testResult };

    } catch (error) {
      logger.error('Scraping workflow failed', { error, baseUrl, config });
      throw new Error(`Scraping workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get scraping job status and progress
   */
  async getJobStatus(jobId: string): Promise<ScrapeJob | null> {
    return await this.jobManager.getJob(jobId);
  }

  /**
   * Get all scraping jobs
   */
  async getJobs(status?: JobStatus, limit?: number): Promise<ScrapeJob[]> {
    return await this.jobManager.getJobs(status, limit);
  }

  /**
   * Cancel a scraping job
   */
  async cancelJob(jobId: string): Promise<boolean> {
    const success = await this.jobManager.cancelJob(jobId);
    if (success) {
      this.emit('job:cancelled', { jobId });
    }
    return success;
  }

  /**
   * Retry a failed job
   */
  async retryJob(jobId: string): Promise<boolean> {
    const success = await this.jobManager.retryJob(jobId);
    if (success) {
      this.emit('job:retried', { jobId });
    }
    return success;
  }

  /**
   * Get queue statistics
   */
  async getQueueStats() {
    return await this.jobManager.getQueueStats();
  }

  /**
   * Close the orchestrator and cleanup resources
   */
  async close(): Promise<void> {
    logger.info('Closing scraping orchestrator');
    
    await Promise.all([
      this.brightdataClient.close(),
      this.qualityAssurance.cleanup(),
      this.jobManager.close()
    ]);
    
    logger.info('Scraping orchestrator closed');
  }

  // Private helper methods

  private async discoverUrls(baseUrl: string, config: ScrapeConfig): Promise<string[]> {
    if (config.mode === 'recursive') {
      const urls: string[] = [];
      const urlGenerator = this.urlDiscoveryService.discoverRecursive(baseUrl);

      for await (const url of urlGenerator) {
        urls.push(url);
        logger.debug('Discovered URL', { url, totalDiscovered: urls.length });
      }

      return urls;
    } else {
      return await this.urlDiscoveryService.discoverComprehensive(baseUrl);
    }
  }

  private setupJobProcessor(): void {
    const queue = this.jobManager.getQueue();
    
    queue.process('scrape-urls', this.config.jobManager.concurrency, async (job: Job<ScrapeJobData>) => {
      return await this.processScrapingJob(job);
    });

    logger.info('Job processor setup completed', { 
      concurrency: this.config.jobManager.concurrency 
    });
  }

  private setupJobCallbacks(): void {
    this.jobManager.setCallbacks({
      onProgress: async (jobId: string, progress: JobProgress) => {
        logger.debug('Job progress update', { jobId, progress });
        this.emit('job:progress', { jobId, progress });
      },
      
      onError: async (jobId: string, error: ScrapeError) => {
        logger.warn('Job error occurred', { jobId, error });
        this.emit('job:error', { jobId, error });
      },
      
      onComplete: async (jobId: string, results: ScrapedContent[]) => {
        logger.info('Job completed successfully', { 
          jobId, 
          resultCount: results.length 
        });
        this.emit('job:complete', { jobId, resultCount: results.length });
      },
      
      onFailed: async (jobId: string, error: Error) => {
        logger.error('Job failed completely', { jobId, error: error.message });
        this.emit('job:failed', { jobId, error: error.message });
      }
    });
  }

  private async processScrapingJob(job: Job<ScrapeJobData>): Promise<ScrapingResult> {
    const { jobId, urls, config } = job.data;
    const startTime = Date.now();
    
    logger.info('Processing scraping job', { 
      jobId, 
      urlCount: urls.length,
      config 
    });

    const results: ScrapedContent[] = [];
    const errors: ScrapeError[] = [];
    let completedUrls = 0;
    let failedUrls = 0;

    try {
      // Update job status to running
      const scrapeJob = await this.jobManager.getJob(jobId);
      if (scrapeJob) {
        scrapeJob.status = JobStatus.RUNNING;
      }

      // Process URLs with quality gates
      const qualityGateInterval = this.qualityAssurance.getQualityGateInterval();
      
      for (let i = 0; i < urls.length; i++) {
        const url = urls[i];

        if (!url) {
          continue;
        }

        try {
          // Update progress
          await this.jobManager.updateJobProgress(jobId, {
            totalUrls: urls.length,
            completedUrls,
            failedUrls,
            currentUrl: url,
            percentage: Math.round((i / urls.length) * 100)
          });

          // Scrape URL with retry logic
          const scrapedContent = await this.scrapeUrlWithRetry(url, config);

          if (scrapedContent) {
            results.push(scrapedContent);
            completedUrls++;
          } else {
            failedUrls++;
            const error: ScrapeError = {
              url,
              error: 'Scraping failed after all retries',
              timestamp: new Date(),
              retryCount: config.retryAttempts
            };
            errors.push(error);
            await this.jobManager.addJobError(jobId, error);
          }

          // Quality gate check at intervals
          if ((i + 1) % qualityGateInterval === 0 && results.length > 0) {
            logger.info('Performing quality gate check', { 
              jobId, 
              progress: `${i + 1}/${urls.length}`,
              recentResults: qualityGateInterval
            });

            const recentResults = results.slice(-qualityGateInterval);
            const qualityGateResult = await this.qualityAssurance.performQualityGate(
              recentResults,
              { completed: i + 1, total: urls.length }
            );

            if (!this.qualityAssurance.shouldContinueScraping(qualityGateResult)) {
              logger.warn('Quality gate failed, stopping scraping job', {
                jobId,
                qualityScore: qualityGateResult.score,
                issues: qualityGateResult.issues.length
              });
              
              throw new Error(`Quality gate failed: ${qualityGateResult.issues.map(i => i.description).join(', ')}`);
            }

            logger.info('Quality gate passed, continuing scraping', {
              jobId,
              qualityScore: qualityGateResult.score
            });
          }

          // Add delay between requests
          if (i < urls.length - 1) {
            await new Promise(resolve => setTimeout(resolve, config.delay));
          }

          // Update job progress
          job.progress(Math.round(((i + 1) / urls.length) * 100));

        } catch (urlError) {
          failedUrls++;
          const error: ScrapeError = {
            url: url || 'unknown',
            error: urlError instanceof Error ? urlError.message : 'Unknown error',
            timestamp: new Date(),
            retryCount: 0
          };
          errors.push(error);
          await this.jobManager.addJobError(jobId, error);

          logger.error('URL scraping failed', { jobId, url, error: error.error });
        }
      }

      // Final progress update
      await this.jobManager.updateJobProgress(jobId, {
        totalUrls: urls.length,
        completedUrls,
        failedUrls,
        percentage: 100
      });

      // Complete the job
      await this.jobManager.completeJob(jobId, results);

      const duration = Date.now() - startTime;
      const result: ScrapingResult = {
        jobId,
        totalUrls: urls.length,
        successfulUrls: completedUrls,
        failedUrls,
        results,
        errors,
        duration
      };

      logger.info('Scraping job completed', {
        jobId,
        totalUrls: urls.length,
        successfulUrls: completedUrls,
        failedUrls,
        duration: `${Math.round(duration / 1000)}s`
      });

      return result;

    } catch (error) {
      logger.error('Scraping job failed', { jobId, error });
      await this.jobManager.failJob(jobId, error instanceof Error ? error : new Error('Unknown error'));
      throw error;
    }
  }

  private async scrapeUrlWithRetry(url: string, config: ScrapeConfig): Promise<ScrapedContent | null> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= config.retryAttempts; attempt++) {
      try {
        logger.debug('Scraping URL', { url, attempt, maxAttempts: config.retryAttempts });
        
        const result = await this.brightdataClient.scrapeUrl(url, {
          waitForTimeout: 10000,
          blockResources: ['image', 'stylesheet', 'font'],
          solveCaptcha: true,
          captchaTimeout: 30000
        });

        if (result.error) {
          throw new Error(result.error);
        }

        // Convert to ScrapedContent format
        const scrapedContent: ScrapedContent = {
          id: uuidv4(),
          url: result.url,
          title: result.title,
          content: result.content,
          contentType: this.detectContentType(result.content, result.url),
          metadata: this.extractMetadata(result),
          qualityMetrics: {
            wordCount: this.getWordCount(result.content),
            structureCompleteness: this.calculateStructureCompleteness(result.content),
            extractionConfidence: 0.8, // Will be calculated by quality assurance
            contentTypeAccuracy: 1.0,
            validationErrors: []
          },
          scrapedAt: new Date(),
          status: ContentStatus.SCRAPED
        };

        logger.debug('URL scraped successfully', { 
          url, 
          attempt,
          contentLength: result.content.length,
          title: result.title
        });

        return scrapedContent;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        logger.warn('URL scraping attempt failed', { 
          url, 
          attempt, 
          maxAttempts: config.retryAttempts,
          error: lastError.message
        });

        // Exponential backoff delay
        if (attempt < config.retryAttempts) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    logger.error('URL scraping failed after all retries', { 
      url, 
      attempts: config.retryAttempts,
      lastError: lastError?.message
    });

    return null;
  }

  private detectContentType(content: string, url: string): ContentType {
    if (url.toLowerCase().includes('.pdf') || content.includes('%PDF-')) {
      return ContentType.PDF;
    }
    
    if (/<table[^>]*>/i.test(content)) {
      return ContentType.TABLE;
    }
    
    if (/<img[^>]*>/i.test(content)) {
      return ContentType.IMAGE;
    }
    
    return ContentType.HTML;
  }

  private extractMetadata(result: any): ContentMetadata {
    return {
      url: result.url,
      title: result.title || '',
      sectionHierarchy: this.extractSectionHierarchy(result.content),
      contentLength: result.content.length,
      language: 'en', // Default, could be detected
      extractedAt: new Date()
    };
  }

  private extractSectionHierarchy(content: string): string[] {
    const hierarchy: string[] = [];
    const headingMatches = content.match(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi);
    
    if (headingMatches) {
      headingMatches.forEach(match => {
        const textMatch = match.match(/>([^<]+)</);
        if (textMatch && textMatch[1]) {
          const text = textMatch[1].trim();
          if (text.length > 0) {
            hierarchy.push(text);
          }
        }
      });
    }
    
    return hierarchy.slice(0, 5); // Limit to top 5 headings
  }

  private getWordCount(content: string): number {
    const cleanText = content
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    return cleanText.split(/\s+/).filter(word => word.length > 0).length;
  }

  private calculateStructureCompleteness(content: string): number {
    let score = 0;
    const maxScore = 5;

    if (/<h[1-6][^>]*>/i.test(content)) score += 1;
    if (/<p[^>]*>/i.test(content)) score += 1;
    if (/<[uo]l[^>]*>/i.test(content)) score += 1;
    if (/<(section|div)[^>]*>/i.test(content)) score += 1;
    if (content.length > 1000) score += 1;

    return score / maxScore;
  }
}