import { DFSAURLDiscoveryService } from './url-discovery-service';
import { logger } from '../utils/logger';

/**
 * Example usage of the DFSA URL Discovery System
 */
async function exampleUsage() {
  const baseUrl = 'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa';
  
  // Initialize the discovery service with custom configuration
  const discoveryService = new DFSAURLDiscoveryService({
    maxUrls: 1000,
    maxDepth: 3,
    crawlDelay: 2000, // 2 seconds between requests
    respectRobotsTxt: true
  });

  console.log('🚀 Starting DFSA URL Discovery Examples\n');

  // Example 1: Comprehensive Discovery
  console.log('📋 Example 1: Comprehensive URL Discovery');
  console.log('This discovers all URLs before starting to scrape...\n');
  
  try {
    const startTime = Date.now();
    const urls = await discoveryService.discoverComprehensive(baseUrl);
    const endTime = Date.now();
    
    console.log(`✅ Comprehensive discovery completed in ${endTime - startTime}ms`);
    console.log(`📊 Found ${urls.length} unique URLs`);
    console.log('\n📝 Sample URLs:');
    urls.slice(0, 10).forEach((url, index) => {
      console.log(`  ${index + 1}. ${url}`);
    });
    
    if (urls.length > 10) {
      console.log(`  ... and ${urls.length - 10} more URLs`);
    }
    
  } catch (error) {
    console.error('❌ Error in comprehensive discovery:', error);
  }

  console.log('\n' + '='.repeat(80) + '\n');

  // Example 2: Recursive Discovery
  console.log('🔄 Example 2: Recursive URL Discovery');
  console.log('This discovers URLs progressively as it crawls...\n');
  
  try {
    const discoveredUrls: string[] = [];
    let count = 0;
    
    console.log('🔍 Starting recursive discovery...');
    
    for await (const url of discoveryService.discoverRecursive(baseUrl)) {
      discoveredUrls.push(url);
      count++;
      
      // Log progress every 10 URLs
      if (count % 10 === 0) {
        console.log(`  📈 Discovered ${count} URLs so far...`);
      }
      
      // Limit for example (remove in production)
      if (count >= 50) {
        console.log('  ⏹️  Stopping early for example (discovered 50 URLs)');
        break;
      }
    }
    
    console.log(`\n✅ Recursive discovery completed`);
    console.log(`📊 Total URLs discovered: ${discoveredUrls.length}`);
    console.log('\n📝 Sample URLs:');
    discoveredUrls.slice(0, 10).forEach((url, index) => {
      console.log(`  ${index + 1}. ${url}`);
    });
    
  } catch (error) {
    console.error('❌ Error in recursive discovery:', error);
  }

  console.log('\n' + '='.repeat(80) + '\n');

  // Example 3: URL Validation
  console.log('✅ Example 3: URL Validation');
  console.log('Testing URL validation with various DFSA URLs...\n');
  
  const testUrls = [
    'https://dfsaen.thomsonreuters.com/rulebook/general-module',
    'https://dfsaen.thomsonreuters.com/rules/conduct-of-business',
    'https://dfsaen.thomsonreuters.com/guidance/anti-money-laundering',
    'https://dfsaen.thomsonreuters.com/search?q=test', // Should be invalid
    'https://google.com/dfsa', // Should be invalid
    'https://dfsaen.thomsonreuters.com/images/logo.png', // Should be invalid
  ];

  const { URLValidator } = await import('./url-validator');
  const validator = new URLValidator();
  
  testUrls.forEach(url => {
    const result = validator.validateUrl(url);
    const status = result.isValid ? '✅ Valid' : '❌ Invalid';
    const reason = result.reason ? ` (${result.reason})` : '';
    console.log(`  ${status}: ${url}${reason}`);
  });

  console.log('\n🎉 Examples completed!');
  console.log('\n💡 Next steps:');
  console.log('  1. Use discovered URLs with the scraping system');
  console.log('  2. Implement quality validation for scraped content');
  console.log('  3. Set up the RAG pipeline for intelligent querying');
}

/**
 * Example of using URL discovery in a production workflow
 */
async function productionWorkflowExample() {
  console.log('\n🏭 Production Workflow Example\n');
  
  const discoveryService = new DFSAURLDiscoveryService({
    maxUrls: 5000,
    maxDepth: 4,
    crawlDelay: 1000,
    respectRobotsTxt: true
  });

  const baseUrl = 'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa';

  try {
    // Step 1: Discover URLs
    console.log('1️⃣ Discovering URLs...');
    const urls = await discoveryService.discoverComprehensive(baseUrl);
    console.log(`   Found ${urls.length} URLs to scrape`);

    // Step 2: Categorize URLs (example categorization)
    console.log('\n2️⃣ Categorizing URLs...');
    const categories = {
      rulebook: urls.filter(url => url.includes('/rulebook/')),
      rules: urls.filter(url => url.includes('/rules/')),
      guidance: urls.filter(url => url.includes('/guidance/')),
      consultation: urls.filter(url => url.includes('/consultation/')),
      policy: urls.filter(url => url.includes('/policy/'))
    };

    Object.entries(categories).forEach(([category, categoryUrls]) => {
      console.log(`   📂 ${category}: ${categoryUrls.length} URLs`);
    });

    // Step 3: Prioritize URLs (example prioritization)
    console.log('\n3️⃣ Prioritizing URLs...');
    const prioritizedUrls = [
      ...categories.rulebook.slice(0, 100), // High priority
      ...categories.rules.slice(0, 50),     // Medium priority
      ...categories.guidance.slice(0, 30),  // Lower priority
    ];
    console.log(`   🎯 Prioritized ${prioritizedUrls.length} URLs for immediate scraping`);

    // Step 4: Ready for scraping
    console.log('\n4️⃣ Ready for scraping phase');
    console.log('   Next: Pass URLs to scraping orchestrator');
    console.log('   Next: Implement quality gates and validation');
    console.log('   Next: Process content for RAG pipeline');

  } catch (error) {
    logger.error('Production workflow error:', error);
    console.error('❌ Production workflow failed:', error);
  }
}

// Run examples if this file is executed directly
if (require.main === module) {
  exampleUsage()
    .then(() => productionWorkflowExample())
    .catch(console.error);
}

export { exampleUsage, productionWorkflowExample };