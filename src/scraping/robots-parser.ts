import { logger } from '../utils/logger';
import { RobotsInfo } from './interfaces';

export class RobotsParser {
  private readonly userAgent: string;

  constructor(userAgent = 'DFSA-Scraper/1.0') {
    this.userAgent = userAgent;
  }

  /**
   * Parse robots.txt and extract relevant information
   */
  async parseRobotsTxt(baseUrl: string): Promise<RobotsInfo> {
    try {
      const robotsUrl = new URL('/robots.txt', baseUrl).toString();
      logger.info('Fetching robots.txt', { url: robotsUrl });

      const response = await fetch(robotsUrl);
      
      if (!response.ok) {
        logger.warn('robots.txt not found or inaccessible', { 
          url: robotsUrl, 
          status: response.status 
        });
        return {
          allowed: true, // Default to allowed if robots.txt not found
          sitemapUrls: []
        };
      }

      const robotsText = await response.text();
      return this.parseRobotsContent(robotsText);

    } catch (error) {
      logger.error('Error parsing robots.txt:', error);
      return {
        allowed: true, // Default to allowed on error
        sitemapUrls: []
      };
    }
  }

  /**
   * Parse robots.txt content
   */
  private parseRobotsContent(content: string): RobotsInfo {
    const lines = content.split('\n').map(line => line.trim());
    let currentUserAgent = '';
    let isRelevantSection = false;
    let allowed = true;
    let crawlDelay: number | undefined;
    const sitemapUrls: string[] = [];

    for (const line of lines) {
      if (line.startsWith('#') || line === '') {
        continue; // Skip comments and empty lines
      }

      const [directive, ...valueParts] = line.split(':');
      const value = valueParts.join(':').trim();

      if (!directive) {
        continue;
      }

      switch (directive.toLowerCase()) {
        case 'user-agent':
          currentUserAgent = value;
          isRelevantSection = this.isUserAgentMatch(value);
          break;

        case 'disallow':
          if (isRelevantSection && value === '/') {
            allowed = false;
          }
          // For more granular disallow rules, we'd check specific paths
          break;

        case 'allow':
          if (isRelevantSection && value === '/') {
            allowed = true;
          }
          break;

        case 'crawl-delay':
          if (isRelevantSection) {
            const delay = parseInt(value, 10);
            if (!isNaN(delay)) {
              crawlDelay = delay * 1000; // Convert to milliseconds
            }
          }
          break;

        case 'sitemap':
          if (this.isValidUrl(value)) {
            sitemapUrls.push(value);
          }
          break;
      }
    }

    logger.info('Parsed robots.txt', {
      allowed,
      crawlDelay,
      sitemapCount: sitemapUrls.length,
      userAgent: this.userAgent
    });

    return {
      allowed,
      crawlDelay: crawlDelay || 0,
      sitemapUrls
    };
  }

  /**
   * Check if user agent matches our scraper
   */
  private isUserAgentMatch(userAgent: string): boolean {
    const ua = userAgent.toLowerCase();
    const ourUA = this.userAgent.toLowerCase();
    
    return ua === '*' || 
           ua === ourUA || 
           ourUA.includes(ua) ||
           ua.includes('dfsa') ||
           ua.includes('scraper');
  }

  /**
   * Validate if string is a valid URL
   */
  private isValidUrl(urlString: string): boolean {
    try {
      new URL(urlString);
      return true;
    } catch {
      return false;
    }
  }
}