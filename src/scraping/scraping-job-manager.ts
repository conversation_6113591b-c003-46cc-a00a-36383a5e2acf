import Bull, { Queue, Job, JobOptions } from 'bull';
import {
    <PERSON>rap<PERSON><PERSON>ob,
    JobStatus,
    JobProgress,
    ScrapeConfig,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    ScrapedContent
} from '../types';
import { logger } from '../utils/logger';
import { config } from '../config/environment';
import { v4 as uuidv4 } from 'uuid';

export interface JobManagerConfig {
    redis: {
        host: string;
        port: number;
        password?: string;
    };
    concurrency: number;
    retryAttempts: number;
    retryDelay: number;
    jobTimeout: number;
    cleanupInterval: number;
}

export interface ScrapeJobData {
    jobId: string;
    urls: string[];
    config: ScrapeConfig;
    startIndex?: number;
    batchSize?: number;
}

export interface JobUpdateCallback {
    onProgress?: (jobId: string, progress: JobProgress) => Promise<void>;
    onError?: (jobId: string, error: ScrapeError) => Promise<void>;
    onComplete?: (jobId: string, results: ScrapedContent[]) => Promise<void>;
    onFailed?: (jobId: string, error: Error) => Promise<void>;
}

/**
 * ScrapingJobManager - Manages scraping jobs using Bull Queue and Redis
 * Handles job queuing, retry mechanisms, progress tracking, and persistence
 */
export class ScrapingJobManager {
    private queue: Queue<ScrapeJobData>;
    private jobs: Map<string, ScrapeJob> = new Map();
    private config: JobManagerConfig;
    private callbacks: JobUpdateCallback = {};

    constructor(config: Partial<JobManagerConfig> = {}) {
        this.config = {
            redis: {
                host: 'localhost',
                port: 6379,
                ...config.redis
            },
            concurrency: 3,
            retryAttempts: 3,
            retryDelay: 5000,
            jobTimeout: 300000, // 5 minutes
            cleanupInterval: 3600000, // 1 hour
            ...config
        };

        // Initialize Bull queue
        this.queue = new Bull('scraping-jobs', {
            redis: this.config.redis,
            defaultJobOptions: {
                attempts: this.config.retryAttempts,
                backoff: {
                    type: 'exponential',
                    delay: this.config.retryDelay
                },
                removeOnComplete: 10,
                removeOnFail: 5
            }
        });

        this.setupQueueProcessors();
        this.setupQueueEventHandlers();
        this.startCleanupScheduler();
    }

    /**
     * Create and queue a new scraping job
     */
    async createJob(
        urls: string[],
        config: ScrapeConfig,
        options: Partial<JobOptions> = {}
    ): Promise<ScrapeJob> {
        const jobId = uuidv4();

        logger.info('Creating scraping job', {
            jobId,
            urlCount: urls.length,
            config
        });

        // Create job record
        const scrapeJob: ScrapeJob = {
            id: jobId,
            urls,
            status: JobStatus.PENDING,
            progress: {
                totalUrls: urls.length,
                completedUrls: 0,
                failedUrls: 0,
                percentage: 0
            },
            config,
            createdAt: new Date(),
            updatedAt: new Date(),
            errors: []
        };

        // Store job in memory (in production, this would be in database)
        this.jobs.set(jobId, scrapeJob);

        // Queue job for processing
        const jobData: ScrapeJobData = {
            jobId,
            urls,
            config
        };

        const bullJob = await this.queue.add('scrape-urls', jobData, {
            jobId,
            delay: options.delay || 0,
            priority: options.priority || 0,
            ...options
        });

        logger.info('Scraping job queued', {
            jobId,
            bullJobId: bullJob.id,
            queuePosition: await this.queue.count()
        });

        return scrapeJob;
    }

    /**
     * Get job status and progress
     */
    async getJob(jobId: string): Promise<ScrapeJob | null> {
        const job = this.jobs.get(jobId);
        if (!job) {
            logger.warn('Job not found', { jobId });
            return null;
        }

        // Update with latest Bull job status
        const bullJob = await this.queue.getJob(jobId);
        if (bullJob) {
            job.status = this.mapBullJobStatus(await bullJob.getState());
            job.updatedAt = new Date();
        }

        return job;
    }

    /**
     * Get all jobs with optional filtering
     */
    async getJobs(status?: JobStatus, limit: number = 50): Promise<ScrapeJob[]> {
        let jobs = Array.from(this.jobs.values());

        if (status) {
            jobs = jobs.filter(job => job.status === status);
        }

        return jobs
            .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
            .slice(0, limit);
    }

    /**
     * Cancel a job
     */
    async cancelJob(jobId: string): Promise<boolean> {
        logger.info('Cancelling job', { jobId });

        const bullJob = await this.queue.getJob(jobId);
        if (bullJob) {
            await bullJob.remove();
        }

        const job = this.jobs.get(jobId);
        if (job) {
            job.status = JobStatus.CANCELLED;
            job.updatedAt = new Date();
            this.jobs.set(jobId, job);
        }

        return true;
    }

    /**
     * Retry a failed job
     */
    async retryJob(jobId: string): Promise<boolean> {
        logger.info('Retrying job', { jobId });

        const job = this.jobs.get(jobId);
        if (!job) {
            logger.error('Cannot retry job - not found', { jobId });
            return false;
        }

        if (job.status !== JobStatus.FAILED) {
            logger.error('Cannot retry job - not in failed state', {
                jobId,
                currentStatus: job.status
            });
            return false;
        }

        // Reset job status
        job.status = JobStatus.PENDING;
        job.progress = {
            totalUrls: job.urls.length,
            completedUrls: 0,
            failedUrls: 0,
            percentage: 0
        };
        job.errors = [];
        job.updatedAt = new Date();
        this.jobs.set(jobId, job);

        // Re-queue the job
        const jobData: ScrapeJobData = {
            jobId,
            urls: job.urls,
            config: job.config
        };

        await this.queue.add('scrape-urls', jobData, { jobId });

        return true;
    }

    /**
     * Update job progress
     */
    async updateJobProgress(jobId: string, progress: Partial<JobProgress>): Promise<void> {
        const job = this.jobs.get(jobId);
        if (!job) {
            logger.warn('Cannot update progress - job not found', { jobId });
            return;
        }

        // Update progress
        job.progress = { ...job.progress, ...progress };
        job.progress.percentage = Math.round((job.progress.completedUrls / job.progress.totalUrls) * 100);
        job.updatedAt = new Date();

        this.jobs.set(jobId, job);

        // Notify callback
        if (this.callbacks.onProgress) {
            await this.callbacks.onProgress(jobId, job.progress);
        }

        logger.debug('Job progress updated', {
            jobId,
            progress: job.progress
        });
    }

    /**
     * Add error to job
     */
    async addJobError(jobId: string, error: ScrapeError): Promise<void> {
        const job = this.jobs.get(jobId);
        if (!job) {
            logger.warn('Cannot add error - job not found', { jobId });
            return;
        }

        job.errors.push(error);
        job.progress.failedUrls++;
        job.updatedAt = new Date();

        this.jobs.set(jobId, job);

        // Notify callback
        if (this.callbacks.onError) {
            await this.callbacks.onError(jobId, error);
        }

        logger.warn('Job error added', {
            jobId,
            error: error.error,
            url: error.url,
            totalErrors: job.errors.length
        });
    }

    /**
     * Mark job as completed
     */
    async completeJob(jobId: string, results: ScrapedContent[]): Promise<void> {
        const job = this.jobs.get(jobId);
        if (!job) {
            logger.warn('Cannot complete job - not found', { jobId });
            return;
        }

        job.status = JobStatus.COMPLETED;
        job.completedAt = new Date();
        job.updatedAt = new Date();

        this.jobs.set(jobId, job);

        // Notify callback
        if (this.callbacks.onComplete) {
            await this.callbacks.onComplete(jobId, results);
        }

        logger.info('Job completed', {
            jobId,
            totalUrls: job.progress.totalUrls,
            completedUrls: job.progress.completedUrls,
            failedUrls: job.progress.failedUrls,
            resultCount: results.length
        });
    }

    /**
     * Mark job as failed
     */
    async failJob(jobId: string, error: Error): Promise<void> {
        const job = this.jobs.get(jobId);
        if (!job) {
            logger.warn('Cannot fail job - not found', { jobId });
            return;
        }

        job.status = JobStatus.FAILED;
        job.updatedAt = new Date();

        this.jobs.set(jobId, job);

        // Notify callback
        if (this.callbacks.onFailed) {
            await this.callbacks.onFailed(jobId, error);
        }

        logger.error('Job failed', {
            jobId,
            error: error.message,
            stack: error.stack
        });
    }

    /**
     * Set job update callbacks
     */
    setCallbacks(callbacks: JobUpdateCallback): void {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * Get queue statistics
     */
    async getQueueStats(): Promise<{
        waiting: number;
        active: number;
        completed: number;
        failed: number;
        delayed: number;
    }> {
        const [waiting, active, completed, failed, delayed] = await Promise.all([
            this.queue.getWaiting(),
            this.queue.getActive(),
            this.queue.getCompleted(),
            this.queue.getFailed(),
            this.queue.getDelayed()
        ]);

        return {
            waiting: waiting.length,
            active: active.length,
            completed: completed.length,
            failed: failed.length,
            delayed: delayed.length
        };
    }

    /**
     * Clean up completed and failed jobs
     */
    async cleanup(): Promise<void> {
        logger.info('Starting job cleanup');

        // Clean up Bull queue
        await this.queue.clean(24 * 60 * 60 * 1000, 'completed'); // 24 hours
        await this.queue.clean(24 * 60 * 60 * 1000, 'failed'); // 24 hours

        // Clean up in-memory jobs (older than 24 hours)
        const cutoffTime = Date.now() - (24 * 60 * 60 * 1000);
        let cleanedCount = 0;

        for (const [jobId, job] of this.jobs.entries()) {
            if (job.updatedAt.getTime() < cutoffTime &&
                (job.status === JobStatus.COMPLETED || job.status === JobStatus.FAILED)) {
                this.jobs.delete(jobId);
                cleanedCount++;
            }
        }

        logger.info('Job cleanup completed', {
            cleanedJobs: cleanedCount,
            remainingJobs: this.jobs.size
        });
    }

    /**
     * Close the job manager and cleanup resources
     */
    async close(): Promise<void> {
        logger.info('Closing scraping job manager');

        await this.queue.close();
        this.jobs.clear();

        logger.info('Scraping job manager closed');
    }

    // Private helper methods

    private setupQueueProcessors(): void {
        // This will be implemented by the ScrapingOrchestrator
        // The processor is registered externally to avoid circular dependencies
        logger.info('Queue processors setup completed', {
            concurrency: this.config.concurrency
        });
    }

    private setupQueueEventHandlers(): void {
        this.queue.on('completed', async (job: Job<ScrapeJobData>) => {
            logger.info('Bull job completed', {
                jobId: job.data.jobId,
                bullJobId: job.id
            });
        });

        this.queue.on('failed', async (job: Job<ScrapeJobData>, err: Error) => {
            logger.error('Bull job failed', {
                jobId: job.data.jobId,
                bullJobId: job.id,
                error: err.message
            });

            await this.failJob(job.data.jobId, err);
        });

        this.queue.on('stalled', async (job: Job<ScrapeJobData>) => {
            logger.warn('Bull job stalled', {
                jobId: job.data.jobId,
                bullJobId: job.id
            });
        });

        this.queue.on('progress', async (job: Job<ScrapeJobData>, progress: number) => {
            logger.debug('Bull job progress', {
                jobId: job.data.jobId,
                bullJobId: job.id,
                progress
            });
        });
    }

    private startCleanupScheduler(): void {
        setInterval(async () => {
            try {
                await this.cleanup();
            } catch (error) {
                logger.error('Cleanup scheduler error', { error });
            }
        }, this.config.cleanupInterval);

        logger.info('Cleanup scheduler started', {
            interval: this.config.cleanupInterval
        });
    }

    private mapBullJobStatus(bullStatus: string): JobStatus {
        switch (bullStatus) {
            case 'waiting':
            case 'delayed':
                return JobStatus.PENDING;
            case 'active':
                return JobStatus.RUNNING;
            case 'completed':
                return JobStatus.COMPLETED;
            case 'failed':
                return JobStatus.FAILED;
            default:
                return JobStatus.PENDING;
        }
    }

    /**
     * Get the Bull queue instance for external processors
     */
    getQueue(): Queue<ScrapeJobData> {
        return this.queue;
    }
}