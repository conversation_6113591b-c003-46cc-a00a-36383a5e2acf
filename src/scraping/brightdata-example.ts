import { BrightdataClient } from './brightdata-client';
import { ScrapingOrchestrator } from './scraping-orchestrator';
import { DFSAURLDiscoveryService } from './url-discovery-service';
// Removed unused logger import

/**
 * Example usage of Brightdata Browser API integration
 */
async function brightdataExamples() {
  console.log('🚀 Brightdata Browser API Integration Examples\n');

  // Example 1: Basic Brightdata Client Usage
  console.log('📡 Example 1: Basic Brightdata Client');
  console.log('Testing connection and basic scraping...\n');

  try {
    // Initialize Brightdata client with official configuration format
    const brightdataClient = new BrightdataClient({
      username: process.env.BRIGHTDATA_USERNAME || 'your-zone-username',
      password: process.env.BRIGHTDATA_PASSWORD || 'your-zone-password',
      zone: 'browser_api',
      country: 'US',
      timeout: 120000 // 2 minutes as per official docs
    });

    // Test connection
    console.log('🔍 Testing Brightdata connection...');
    const connectionTest = await brightdataClient.testConnection();
    console.log(`Connection test: ${connectionTest ? '✅ Success' : '❌ Failed'}`);

    if (connectionTest) {
      // Get session info
      const sessionInfo = await brightdataClient.getSessionInfo();
      console.log('📊 Session Info:', sessionInfo);

      // Test scraping a single URL
      console.log('\n🌐 Testing single URL scraping...');
      const testUrl = 'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa';
      
      const scrapingResult = await brightdataClient.scrapeUrl(testUrl, {
        waitForSelector: 'h1, .content',
        waitForTimeout: 5000,
        blockResources: ['image', 'stylesheet', 'font'], // Official resource types
        viewport: { width: 1920, height: 1080 },
        solveCaptcha: true, // Enable CAPTCHA solving
        captchaTimeout: 30000
      });

      console.log('📄 Scraping Result:');
      console.log(`  URL: ${scrapingResult.url}`);
      console.log(`  Title: ${scrapingResult.title}`);
      console.log(`  Status Code: ${scrapingResult.statusCode}`);
      console.log(`  Content Length: ${scrapingResult.content.length} characters`);
      console.log(`  Load Time: ${scrapingResult.loadTime}ms`);
      console.log(`  Error: ${scrapingResult.error || 'None'}`);
    }

    await brightdataClient.close();

  } catch (error) {
    console.error('❌ Brightdata client example failed:', error);
  }

  console.log('\n' + '='.repeat(80) + '\n');

  // Example 2: Scraping Orchestrator with Quality Gates
  console.log('🎭 Example 2: Scraping Orchestrator with Quality Gates');
  console.log('Demonstrating test scraping and quality validation...\n');

  try {
    // Initialize orchestrator
    const orchestrator = new ScrapingOrchestrator({
      brightdata: {
        username: process.env.BRIGHTDATA_USERNAME || 'your-zone-username',
        password: process.env.BRIGHTDATA_PASSWORD || 'your-zone-password',
        zone: 'browser_api'
      },
      scraping: {
        maxConcurrency: 2,
        delayBetweenRequests: 2000,
        retryAttempts: 3,
        qualityGateThreshold: 0.7,
        testScrapeSize: 5,
        timeout: 30000
      },
      jobManager: {
        concurrency: 2,
        retryAttempts: 3,
        retryDelay: 1000
      }
    });

    // Discover some URLs for testing
    const urlDiscovery = new DFSAURLDiscoveryService();
    const baseUrl = 'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa';
    
    console.log('🔍 Discovering URLs for testing...');
    const discoveredUrls = await urlDiscovery.discoverComprehensive(baseUrl);
    console.log(`Found ${discoveredUrls.length} URLs`);

    if (discoveredUrls.length > 0) {
      // Start scraping workflow (includes test scraping)
      console.log('\n🧪 Starting scraping workflow...');
      const workflowResult = await orchestrator.startScraping(baseUrl, {
        mode: 'comprehensive',
        delay: 2000,
        retryAttempts: 2,
        qualityGateInterval: 5,
        testScrapeSize: 5
      });
      
      console.log('📊 Workflow Results:');
      if (workflowResult.testResult) {
        console.log(`  URLs Tested: ${workflowResult.testResult.urls.length}`);
        console.log(`  Quality Score: ${workflowResult.testResult.qualityScore.toFixed(2)}`);
      }
      if (workflowResult.testResult) {
        console.log(`  Successful Scrapes: ${workflowResult.testResult.results.filter((r: any) => r.status === 'scraped').length}`);
        console.log(`  Failed Scrapes: ${workflowResult.testResult.results.filter((r: any) => r.status === 'failed').length}`);

        console.log('\n📝 Recommendations:');
        workflowResult.testResult.recommendations.forEach((rec: string, index: number) => {
          console.log(`  ${index + 1}. ${rec}`);
        });

        console.log('\n📈 Sample Results:');
        workflowResult.testResult.results.slice(0, 3).forEach((result: any, index: number) => {
          console.log(`  ${index + 1}. ${result.url}`);
          console.log(`     Title: ${result.title}`);
          console.log(`     Content Length: ${result.content.length} chars`);
          console.log(`     Word Count: ${result.qualityMetrics.wordCount}`);
          console.log(`     Status: ${result.status}`);
        });
      }

      // Show workflow status
      if (workflowResult.jobId) {
        console.log(`\n✅ Scraping job started: ${workflowResult.jobId}`);
      } else {
        console.log('\n❌ Scraping workflow was not approved');
      }
    }

    await orchestrator.close();

  } catch (error) {
    console.error('❌ Scraping orchestrator example failed:', error);
  }

  console.log('\n' + '='.repeat(80) + '\n');

  // Example 3: Production Workflow Simulation
  console.log('🏭 Example 3: Production Workflow Simulation');
  console.log('Simulating a complete scraping workflow...\n');

  try {
    const brightdataClient = new BrightdataClient({
      username: process.env.BRIGHTDATA_USERNAME || 'your-zone-username',
      password: process.env.BRIGHTDATA_PASSWORD || 'your-zone-password'
    });

    // Health check
    console.log('🏥 Performing health checks...');
    const health = brightdataClient.getHealthStatus();
    console.log('Client Health:', health);

    const connectionOk = await brightdataClient.testConnection();
    console.log(`Connection Status: ${connectionOk ? '✅ Healthy' : '❌ Unhealthy'}`);

    if (connectionOk) {
      // Simulate batch scraping
      console.log('\n📦 Simulating batch scraping...');
      const testUrls = [
        'https://dfsaen.thomsonreuters.com/rulebook/general-module',
        'https://dfsaen.thomsonreuters.com/rulebook/conduct-of-business',
        'https://dfsaen.thomsonreuters.com/rulebook/prudential-investment-business'
      ];

      const batchResults = await brightdataClient.scrapeUrls(testUrls, {
        waitForTimeout: 3000,
        blockResources: ['image', 'stylesheet', 'font', 'media'], // Official resource types
        customHeaders: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5'
        },
        solveCaptcha: true
      }, 2);

      console.log('📊 Batch Scraping Results:');
      console.log(`  Total URLs: ${testUrls.length}`);
      console.log(`  Successful: ${batchResults.filter(r => !r.error).length}`);
      console.log(`  Failed: ${batchResults.filter(r => r.error).length}`);
      
      const avgLoadTime = batchResults
        .filter(r => !r.error)
        .reduce((sum, r) => sum + r.loadTime, 0) / batchResults.filter(r => !r.error).length;
      
      console.log(`  Average Load Time: ${Math.round(avgLoadTime)}ms`);

      // Show sample content analysis
      const successfulResults = batchResults.filter(r => !r.error);
      if (successfulResults.length > 0) {
        console.log('\n📄 Content Analysis Sample:');
        const sample = successfulResults[0];
        if (sample) {
          console.log(`  URL: ${sample.url}`);
          console.log(`  Title: ${sample.title}`);
          console.log(`  Content Preview: ${sample.content.substring(0, 200)}...`);
          console.log(`  Has DFSA Content: ${/dfsa|dubai financial services|rulebook/i.test(sample.content) ? '✅ Yes' : '❌ No'}`);
          console.log(`  Has Headings: ${/<h[1-6][^>]*>/i.test(sample.content) ? '✅ Yes' : '❌ No'}`);
        }
      }
    }

    await brightdataClient.close();

  } catch (error) {
    console.error('❌ Production workflow example failed:', error);
  }

  console.log('\n🎉 Brightdata integration examples completed!');
  console.log('\n💡 Next steps:');
  console.log('  1. Configure your Brightdata API credentials');
  console.log('  2. Test with your specific DFSA URLs');
  console.log('  3. Integrate with the scraping orchestrator');
  console.log('  4. Set up quality gates and monitoring');
}

/**
 * Configuration example for different environments
 */
function showConfigurationExamples() {
  console.log('\n⚙️  Configuration Examples\n');

  console.log('🏢 Production Configuration:');
  console.log(`
const productionConfig = {
  username: process.env.BRIGHTDATA_USERNAME, // Your zone username
  password: process.env.BRIGHTDATA_PASSWORD, // Your zone password
  zone: 'browser_api',
  country: 'US',
  timeout: 120000, // 2 minutes (official recommendation)
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
};
  `);

  console.log('🧪 Development Configuration:');
  console.log(`
const developmentConfig = {
  username: process.env.BRIGHTDATA_USERNAME,
  password: process.env.BRIGHTDATA_PASSWORD,
  zone: 'browser_api',
  country: 'US',
  timeout: 60000,
  sessionId: 'dev-session-' + Date.now()
};
  `);

  console.log('🌍 Residential Proxy Configuration:');
  console.log(`
const residentialConfig = {
  username: process.env.BRIGHTDATA_RESIDENTIAL_USERNAME, // Residential zone username
  password: process.env.BRIGHTDATA_RESIDENTIAL_PASSWORD, // Residential zone password
  zone: 'residential_browser', // Browser API with residential IPs
  country: 'AE', // UAE for DFSA content
  timeout: 120000
};
  `);

  console.log('📱 Mobile Proxy Configuration:');
  console.log(`
const mobileConfig = {
  username: process.env.BRIGHTDATA_MOBILE_USERNAME, // Mobile zone username
  password: process.env.BRIGHTDATA_MOBILE_PASSWORD, // Mobile zone password
  zone: 'mobile_browser', // Browser API with mobile IPs
  country: 'AE',
  userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15'
};
  `);
}

// Run examples if this file is executed directly
if (require.main === module) {
  brightdataExamples()
    .then(() => showConfigurationExamples())
    .catch(console.error);
}

export { brightdataExamples, showConfigurationExamples };