import { URL } from 'url';
import { logger } from '../utils/logger';
import { URLValidationResult } from './interfaces';

export class URLValidator {
  private readonly dfsaDomainPattern = /^https?:\/\/(?:www\.)?dfsaen\.thomsonreuters\.com/;
  private readonly validPathPatterns = [
    /\/rulebook\//,
    /\/rules\//,
    /\/guidance\//,
    /\/consultation\//,
    /\/policy\//
  ];
  
  private readonly excludePatterns = [
    /\/search\?/,
    /\/print\//,
    /\/download\//,
    /\.pdf$/,
    /\.doc$/,
    /\.docx$/,
    /\.xls$/,
    /\.xlsx$/,
    /\/images?\//,
    /\/css\//,
    /\/js\//,
    /\/assets\//,
    /#/,
    /\?.*page=/,
    /\/login/,
    /\/logout/,
    /\/admin/
  ];

  /**
   * Validates if a URL should be scraped based on DFSA-specific rules
   */
  validateUrl(url: string): URLValidationResult {
    try {
      // Basic URL validation
      const parsedUrl = new URL(url);
      
      // Must be DFSA domain
      if (!this.dfsaDomainPattern.test(url)) {
        return {
          isValid: false,
          reason: 'Not a DFSA domain URL'
        };
      }

      // Check for excluded patterns
      for (const pattern of this.excludePatterns) {
        if (pattern.test(url)) {
          return {
            isValid: false,
            reason: `Matches excluded pattern: ${pattern.source}`
          };
        }
      }

      // Normalize URL (remove fragments, sort query params)
      const normalizedUrl = this.normalizeUrl(parsedUrl);

      // Check if it's a valid DFSA content path
      const hasValidPath = this.validPathPatterns.some(pattern => 
        pattern.test(parsedUrl.pathname)
      );

      if (!hasValidPath) {
        return {
          isValid: false,
          reason: 'Does not match valid DFSA content patterns'
        };
      }

      return {
        isValid: true,
        normalizedUrl
      };

    } catch (error) {
      logger.error('URL validation error:', { url, error });
      return {
        isValid: false,
        reason: 'Invalid URL format'
      };
    }
  }

  /**
   * Normalizes URL for deduplication
   */
  private normalizeUrl(parsedUrl: URL): string {
    // Remove fragment
    parsedUrl.hash = '';
    
    // Sort query parameters for consistent URLs
    const params = new URLSearchParams(parsedUrl.search);
    const sortedParams = new URLSearchParams();
    
    // Sort parameters alphabetically
    Array.from(params.keys()).sort().forEach(key => {
      sortedParams.append(key, params.get(key) || '');
    });
    
    parsedUrl.search = sortedParams.toString();
    
    // Remove trailing slash for consistency
    if (parsedUrl.pathname.endsWith('/') && parsedUrl.pathname.length > 1) {
      parsedUrl.pathname = parsedUrl.pathname.slice(0, -1);
    }
    
    return parsedUrl.toString();
  }

  /**
   * Batch validate multiple URLs
   */
  validateUrls(urls: string[]): { valid: string[]; invalid: Array<{ url: string; reason: string }> } {
    const valid: string[] = [];
    const invalid: Array<{ url: string; reason: string }> = [];

    for (const url of urls) {
      const result = this.validateUrl(url);
      if (result.isValid && result.normalizedUrl) {
        valid.push(result.normalizedUrl);
      } else {
        invalid.push({
          url,
          reason: result.reason || 'Unknown validation error'
        });
      }
    }

    return { valid, invalid };
  }

  /**
   * Remove duplicate URLs from array
   */
  deduplicateUrls(urls: string[]): { unique: string[]; duplicatesRemoved: number } {
    const seen = new Set<string>();
    const unique: string[] = [];

    for (const url of urls) {
      const validation = this.validateUrl(url);
      const normalizedUrl = validation.normalizedUrl || url;
      
      if (!seen.has(normalizedUrl)) {
        seen.add(normalizedUrl);
        unique.push(normalizedUrl);
      }
    }

    return {
      unique,
      duplicatesRemoved: urls.length - unique.length
    };
  }
}