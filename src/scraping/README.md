# Brightdata Browser API Integration

This module provides a comprehensive integration with Brightdata's Browser API for web scraping, following official documentation patterns and best practices.

## Overview

The Brightdata Browser API integration enables:
- High-performance web scraping using real browsers
- Automatic CAPTCHA solving
- Proxy rotation and IP management
- Resource blocking for bandwidth optimization
- Rate limiting and error handling
- Quality assurance and monitoring

### ⚠️ Important: Single Navigation Limitation

**Brightdata Browser API allows only ONE navigation per browser session.** This means:
- Each URL requires a new browser connection
- You cannot navigate to multiple pages in the same session
- The client automatically creates new sessions for each `scrapeUrl()` call
- This is handled transparently by the implementation

## Quick Start

### 1. Setup Brightdata Account

1. Sign up at [Brightdata](https://brightdata.com/)
2. Create a Browser API zone in your dashboard
3. Get your zone username and password

### 2. Configuration

```typescript
import { BrightdataClient } from './brightdata-client';

const client = new BrightdataClient({
  username: 'your-zone-username',    // From Brightdata dashboard
  password: 'your-zone-password',    // From Brightdata dashboard
  zone: 'browser_api',               // Your zone name
  country: 'US',                     // Target country
  timeout: 120000,                   // 2 minutes (recommended)
  rateLimitDelay: 1000              // 1 second between requests
});
```

### 3. Basic Usage

```typescript
// Test connection
const isConnected = await client.testConnection();
console.log('Connected:', isConnected);

// Scrape a single URL
const result = await client.scrapeUrl('https://example.com', {
  waitForSelector: 'h1',
  waitForTimeout: 5000,
  blockResources: ['image', 'stylesheet', 'font'],
  solveCaptcha: true,
  takeScreenshot: true
});

console.log('Title:', result.title);
console.log('Content length:', result.content.length);

// Scrape multiple URLs
const results = await client.scrapeUrls([
  'https://example1.com',
  'https://example2.com'
], {
  blockResources: ['image', 'media'],
  solveCaptcha: true
}, 2); // Concurrency of 2

// Clean up
await client.close();
```

## Configuration Options

### BrightdataConfig

| Option | Type | Required | Description |
|--------|------|----------|-------------|
| `username` | string | ✅ | Your Brightdata zone username |
| `password` | string | ✅ | Your Brightdata zone password |
| `zone` | string | ❌ | Zone name (default: 'browser_api') |
| `country` | string | ❌ | Target country code (default: 'US') |
| `timeout` | number | ❌ | Request timeout in ms (default: 120000) |
| `userAgent` | string | ❌ | Custom user agent |
| `rateLimitDelay` | number | ❌ | Delay between requests in ms (default: 1000) |

### ScrapingOptions

| Option | Type | Description |
|--------|------|-------------|
| `waitForSelector` | string | CSS selector to wait for |
| `waitForTimeout` | number | Additional wait time in ms |
| `takeScreenshot` | boolean | Capture page screenshot |
| `blockResources` | string[] | Resource types to block |
| `customHeaders` | object | Custom HTTP headers |
| `viewport` | object | Browser viewport size |
| `solveCaptcha` | boolean | Enable CAPTCHA solving |
| `captchaTimeout` | number | CAPTCHA detection timeout |

## Resource Blocking

Block unnecessary resources to save bandwidth and improve performance:

```typescript
const options = {
  blockResources: [
    'image',      // Block images
    'stylesheet', // Block CSS files
    'font',       // Block web fonts
    'media',      // Block videos/audio
    'svg'         // Block SVG files
  ]
};
```

## CAPTCHA Solving

Enable automatic CAPTCHA solving for protected sites:

```typescript
const result = await client.scrapeUrl('https://protected-site.com', {
  solveCaptcha: true,
  captchaTimeout: 30000 // 30 seconds to detect and solve
});
```

## Error Handling

The client provides comprehensive error handling:

```typescript
const result = await client.scrapeUrl('https://example.com');

if (result.error) {
  console.error('Scraping failed:', result.error);
  // Handle error (retry, log, etc.)
} else {
  console.log('Success:', result.title);
  // Process successful result
}
```

## Rate Limiting

Respect website rate limits and Brightdata usage policies:

```typescript
const client = new BrightdataClient({
  username: 'your-username',
  password: 'your-password',
  rateLimitDelay: 2000 // 2 seconds between requests
});

// Batch scraping with controlled concurrency
const results = await client.scrapeUrls(urls, options, 3); // Max 3 concurrent
```

## Environment Variables

Set up your environment variables:

```bash
# Required
BRIGHTDATA_USERNAME=your-zone-username
BRIGHTDATA_PASSWORD=your-zone-password

# Optional
BRIGHTDATA_ZONE=browser_api
BRIGHTDATA_COUNTRY=US
BRIGHTDATA_TIMEOUT=120000
```

## Testing

### Unit Tests

```bash
npm test src/test/scraping/brightdata-client.test.ts
```

### Integration Tests

```bash
# Set credentials first
export BRIGHTDATA_USERNAME=your-username
export BRIGHTDATA_PASSWORD=your-password
export RUN_INTEGRATION_TESTS=true

# Run integration tests
npm test src/test/scraping/brightdata-integration.test.ts
```

### Manual Testing

```bash
# Run example script
npm run dev src/scraping/brightdata-example.ts
```

## Best Practices

### 1. Connection Management

```typescript
// Initialize once, reuse for multiple requests
// Note: Each scrapeUrl() creates its own browser session
const client = new BrightdataClient(config);

try {
  // Each URL gets its own browser session automatically
  const results = await Promise.all([
    client.scrapeUrl('https://site1.com'), // New session
    client.scrapeUrl('https://site2.com')  // New session
  ]);
} finally {
  // Cleanup (individual sessions are closed automatically)
  await client.close();
}
```

### 2. Resource Optimization

```typescript
// Block unnecessary resources for better performance
const options = {
  blockResources: ['image', 'stylesheet', 'font'],
  viewport: { width: 1280, height: 720 }, // Smaller viewport
  waitForTimeout: 5000 // Don't wait too long
};
```

### 3. Error Recovery

```typescript
async function scrapeWithRetry(url: string, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    const result = await client.scrapeUrl(url);
    
    if (!result.error) {
      return result;
    }
    
    if (i < maxRetries - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
  
  throw new Error(`Failed to scrape ${url} after ${maxRetries} attempts`);
}
```

### 4. Quality Validation

```typescript
function validateScrapingResult(result: ScrapingResult): boolean {
  return (
    !result.error &&
    result.statusCode === 200 &&
    result.content.length > 100 &&
    result.title.length > 0
  );
}
```

## DFSA-Specific Configuration

For scraping DFSA rulebook content:

```typescript
const dfsaClient = new BrightdataClient({
  username: process.env.BRIGHTDATA_USERNAME!,
  password: process.env.BRIGHTDATA_PASSWORD!,
  zone: 'browser_api',
  country: 'AE', // UAE for better DFSA access
  timeout: 120000,
  rateLimitDelay: 2000 // Respectful rate limiting
});

const dfsaOptions = {
  waitForSelector: 'h1, .content, .main',
  waitForTimeout: 10000,
  blockResources: ['image', 'stylesheet', 'font'],
  solveCaptcha: true,
  captchaTimeout: 30000,
  customHeaders: {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5'
  }
};
```

## Troubleshooting

### Common Issues

1. **"Page.navigate limit reached" Error**
   - This occurs when trying to navigate multiple times in one session
   - **Solution**: The client now automatically creates new sessions per URL
   - Each `scrapeUrl()` call uses a fresh browser connection

2. **Connection Timeout**
   - Increase timeout value
   - Check network connectivity
   - Verify credentials

3. **CAPTCHA Not Solving**
   - Increase captchaTimeout
   - Check if site supports CAPTCHA solving
   - Try different user agent

4. **Rate Limiting**
   - Increase rateLimitDelay
   - Reduce concurrency
   - Check Brightdata usage limits

5. **Content Not Loading**
   - Increase waitForTimeout
   - Use waitForSelector for specific elements
   - Check if JavaScript is required

### Debug Mode

Enable detailed logging:

```typescript
import { logger } from '../utils/logger';

// Set log level to debug
logger.level = 'debug';

// Client will now log detailed information
const result = await client.scrapeUrl(url);
```

## API Reference

### BrightdataClient

#### Methods

- `initialize()`: Connect to Brightdata Browser API
- `scrapeUrl(url, options)`: Scrape a single URL
- `scrapeUrls(urls, options, concurrency)`: Scrape multiple URLs
- `testConnection()`: Test API connection
- `getSessionInfo()`: Get current session information
- `getHealthStatus()`: Get client health status
- `close()`: Close browser and cleanup

#### Events

The client uses structured logging for monitoring:

```typescript
// Monitor scraping events
logger.on('info', (info) => {
  if (info.message.includes('Scrape request completed')) {
    console.log('Scraping completed:', info);
  }
});
```

## Support

- [Brightdata Documentation](https://docs.brightdata.com/scraping-automation/scraping-browser/)
- [Brightdata Support](https://help.brightdata.com/)
- Internal documentation: See `src/scraping/brightdata-example.ts` for examples

## License

This integration follows the same license as the main project.