import { BrightdataClient } from './brightdata-client';
import { ContentValidator } from './content-validator';
import { TestScraper } from './test-scraper';
import { ManualConfirmation } from './manual-confirmation';
import { 
  TestScrapeResult, 
  QualityGateResult, 
  ScrapedContent,
  QualityIssue 
} from '../types';
import { logger } from '../utils/logger';

export interface QualityAssuranceConfig {
  testScrapeSize: number;
  qualityThreshold: number;
  qualityGateInterval: number;
  requireManualConfirmation: boolean;
  autoApproveThreshold: number;
}

export interface QualityAssuranceResult {
  testScrapeApproved: boolean;
  qualityGatesPassed: number;
  qualityGatesFailed: number;
  overallQualityScore: number;
  recommendations: string[];
  issues: QualityIssue[];
}

/**
 * QualityAssurance - Orchestrates the complete quality assurance workflow
 * including test scraping, manual confirmation, and ongoing quality gates
 */
export class QualityAssurance {
  private brightdataClient: BrightdataClient;
  private contentValidator: ContentValidator;
  private testScraper: TestScraper;
  private manualConfirmation: ManualConfirmation;
  private config: QualityAssuranceConfig;

  constructor(
    brightdataClient: BrightdataClient,
    config: Partial<QualityAssuranceConfig> = {}
  ) {
    this.brightdataClient = brightdataClient;
    this.contentValidator = new ContentValidator();
    this.testScraper = new TestScraper(brightdataClient, this.contentValidator);
    this.manualConfirmation = new ManualConfirmation();
    
    this.config = {
      testScrapeSize: 5,
      qualityThreshold: 0.7,
      qualityGateInterval: 50, // Check every 50 URLs
      requireManualConfirmation: true,
      autoApproveThreshold: 0.9, // Auto-approve if quality score is above 90%
      ...config
    };
  }

  /**
   * Perform complete test scraping workflow with manual confirmation
   */
  async performTestScrapeWorkflow(urls: string[]): Promise<{
    approved: boolean;
    testResult: TestScrapeResult;
    confirmationReport?: string;
  }> {
    logger.info('Starting test scrape workflow', { 
      totalUrls: urls.length,
      testScrapeSize: this.config.testScrapeSize 
    });

    try {
      // Step 1: Perform test scraping
      const testResult = await this.testScraper.performTestScrape(urls);
      
      // Step 2: Validate test results
      const validation = await this.testScraper.validateTestResults(testResult);
      
      logger.info('Test scrape validation completed', {
        shouldProceed: validation.shouldProceed,
        criticalIssues: validation.criticalIssues.length,
        qualityScore: testResult.qualityScore
      });

      // Step 3: Check if manual confirmation is needed
      let approved = false;
      let confirmationReport: string | undefined;

      if (this.shouldAutoApprove(testResult, validation)) {
        // Auto-approve if quality is exceptionally high
        approved = true;
        logger.info('Test scrape auto-approved due to high quality score', {
          qualityScore: testResult.qualityScore
        });
      } else if (this.config.requireManualConfirmation) {
        // Request manual confirmation
        console.log('\n📋 Displaying detailed test results for review...');
        this.manualConfirmation.displayDetailedResults(testResult);
        
        const confirmation = await this.manualConfirmation.confirmTestScrapeResults(testResult);
        approved = confirmation.approved;
        
        // Generate confirmation report
        confirmationReport = this.manualConfirmation.generateConfirmationReport(testResult, confirmation);
        
        if (!approved) {
          logger.info('Test scrape rejected by user', {
            feedback: confirmation.feedback,
            adjustments: confirmation.adjustments
          });
        }
      } else {
        // Auto-approve based on validation
        approved = validation.shouldProceed;
      }

      // Update test result approval status
      testResult.approved = approved;

      const result: {
        approved: boolean;
        testResult: TestScrapeResult;
        confirmationReport?: string;
      } = {
        approved,
        testResult
      };

      if (confirmationReport !== undefined) {
        result.confirmationReport = confirmationReport;
      }

      return result;

    } catch (error) {
      logger.error('Test scrape workflow failed', { error });
      throw new Error(`Test scrape workflow failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Perform quality gate validation during scraping
   */
  async performQualityGate(
    scrapedContents: ScrapedContent[],
    currentProgress: { completed: number; total: number }
  ): Promise<QualityGateResult> {
    logger.info('Performing quality gate validation', {
      contentCount: scrapedContents.length,
      progress: `${currentProgress.completed}/${currentProgress.total}`
    });

    try {
      // Validate content quality
      const validationResult = await this.contentValidator.validateQualityGate(scrapedContents);

      // Map to QualityGateResult format
      const qualityResult: QualityGateResult = {
        passed: validationResult.passed,
        score: validationResult.overallScore,
        issues: validationResult.issues,
        recommendations: validationResult.recommendations
      };

      // Log quality gate results
      logger.info('Quality gate validation completed', {
        passed: qualityResult.passed,
        score: qualityResult.score,
        issueCount: qualityResult.issues.length,
        highSeverityIssues: qualityResult.issues.filter(i => i.severity === 'high').length
      });

      // If manual confirmation is required and quality gate failed
      if (!qualityResult.passed && this.config.requireManualConfirmation) {
        const confirmation = await this.manualConfirmation.confirmQualityGate(qualityResult);
        
        if (!confirmation.approved) {
          logger.warn('Quality gate manually rejected', {
            feedback: confirmation.feedback,
            adjustments: confirmation.adjustments
          });
          
          // Override the quality gate result based on manual decision
          qualityResult.passed = false;
        } else {
          logger.info('Quality gate manually approved despite issues');
          qualityResult.passed = true;
        }
      }

      return qualityResult;

    } catch (error) {
      logger.error('Quality gate validation failed', { error });
      
      return {
        passed: false,
        score: 0,
        issues: [{
          type: 'extraction',
          severity: 'high',
          description: `Quality gate validation error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          affectedUrls: scrapedContents.map(c => c.url)
        }],
        recommendations: ['Review quality gate configuration and retry']
      };
    }
  }

  /**
   * Generate comprehensive quality assurance report
   */
  generateQualityReport(
    testResult: TestScrapeResult,
    qualityGateResults: QualityGateResult[]
  ): QualityAssuranceResult {
    const qualityGatesPassed = qualityGateResults.filter(r => r.passed).length;
    const qualityGatesFailed = qualityGateResults.filter(r => !r.passed).length;
    
    // Calculate overall quality score
    const allScores = [
      testResult.qualityScore,
      ...qualityGateResults.map(r => r.score)
    ].filter(score => score > 0);
    
    const overallQualityScore = allScores.length > 0 
      ? allScores.reduce((sum, score) => sum + score, 0) / allScores.length
      : 0;

    // Collect all issues
    const allIssues = qualityGateResults.reduce((issues, result) => {
      return issues.concat(result.issues);
    }, [] as QualityIssue[]);

    // Generate recommendations
    const recommendations = this.generateOverallRecommendations(
      testResult,
      qualityGateResults,
      overallQualityScore
    );

    return {
      testScrapeApproved: testResult.approved,
      qualityGatesPassed,
      qualityGatesFailed,
      overallQualityScore,
      recommendations,
      issues: allIssues
    };
  }

  /**
   * Check if scraping should continue based on quality metrics
   */
  shouldContinueScraping(qualityResult: QualityGateResult): boolean {
    // Stop if there are high-severity issues
    const highSeverityIssues = qualityResult.issues.filter(i => i.severity === 'high');
    if (highSeverityIssues.length > 0) {
      logger.warn('High severity issues detected, recommending to stop scraping', {
        highSeverityCount: highSeverityIssues.length
      });
      return false;
    }

    // Stop if overall quality is too low
    if (qualityResult.score < this.config.qualityThreshold * 0.5) {
      logger.warn('Quality score critically low, recommending to stop scraping', {
        score: qualityResult.score,
        threshold: this.config.qualityThreshold * 0.5
      });
      return false;
    }

    return qualityResult.passed;
  }

  /**
   * Get quality gate interval (how often to check quality)
   */
  getQualityGateInterval(): number {
    return this.config.qualityGateInterval;
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    this.manualConfirmation.close();
    logger.info('Quality assurance cleanup completed');
  }

  // Private helper methods

  private shouldAutoApprove(
    testResult: TestScrapeResult, 
    validation: { shouldProceed: boolean; criticalIssues: string[] }
  ): boolean {
    return (
      testResult.qualityScore >= this.config.autoApproveThreshold &&
      validation.shouldProceed &&
      validation.criticalIssues.length === 0
    );
  }

  private generateOverallRecommendations(
    testResult: TestScrapeResult,
    qualityGateResults: QualityGateResult[],
    overallScore: number
  ): string[] {
    const recommendations: string[] = [];

    // Test scrape recommendations
    recommendations.push(...testResult.recommendations);

    // Quality gate recommendations
    qualityGateResults.forEach(result => {
      recommendations.push(...result.recommendations);
    });

    // Overall recommendations based on score
    if (overallScore < 0.5) {
      recommendations.push('Overall quality is critically low - consider major configuration changes');
    } else if (overallScore < 0.7) {
      recommendations.push('Overall quality is below optimal - review and adjust scraping parameters');
    } else if (overallScore >= 0.9) {
      recommendations.push('Excellent quality detected - current configuration is optimal');
    }

    // Remove duplicates
    return [...new Set(recommendations)];
  }
}