import { BrightdataClient } from './brightdata-client';
import { ContentValidator } from './content-validator';
import { 
  ScrapedContent, 
  TestScrapeResult, 
  ContentType, 
  ContentStatus,
  ContentMetadata,
  QualityMetrics 
} from '../types';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

export interface TestScrapingConfig {
  sampleSize: number;
  timeout: number;
  qualityThreshold: number;
  includeVariedPages: boolean;
  maxRetries: number;
}

/**
 * TestScraper - Performs test scraping on a sample of URLs to validate scraping quality
 * before running full-scale scraping operations
 */
export class TestScraper {
  private brightdataClient: BrightdataClient;
  private contentValidator: ContentValidator;
  private config: TestScrapingConfig;

  constructor(
    brightdataClient: BrightdataClient,
    contentValidator: ContentValidator,
    config: Partial<TestScrapingConfig> = {}
  ) {
    this.brightdataClient = brightdataClient;
    this.contentValidator = contentValidator;
    this.config = {
      sampleSize: 5,
      timeout: 30000,
      qualityThreshold: 0.7,
      includeVariedPages: true,
      maxRetries: 2,
      ...config
    };
  }

  /**
   * Perform test scraping on a sample of URLs
   */
  async performTestScrape(urls: string[]): Promise<TestScrapeResult> {
    logger.info('Starting test scrape', { 
      totalUrls: urls.length, 
      sampleSize: this.config.sampleSize 
    });

    try {
      // Select sample URLs
      const sampleUrls = this.selectSampleUrls(urls);
      logger.info('Selected sample URLs for testing', { sampleUrls });

      // Scrape sample URLs
      const scrapedResults = await this.scrapeSampleUrls(sampleUrls);
      
      // Convert to ScrapedContent format
      const scrapedContents = await this.convertToScrapedContent(scrapedResults);
      
      // Validate content quality
      const qualityResults = await this.validateSampleContent(scrapedContents);
      
      // Calculate overall quality score
      const qualityScore = this.calculateOverallQualityScore(qualityResults);
      
      // Generate recommendations
      const recommendations = this.generateRecommendations(qualityResults, qualityScore);
      
      const testResult: TestScrapeResult = {
        urls: sampleUrls,
        results: scrapedContents,
        qualityScore,
        recommendations,
        approved: false // Requires manual approval
      };

      logger.info('Test scrape completed', {
        sampleSize: sampleUrls.length,
        successfulScrapes: scrapedContents.filter(c => c.status === ContentStatus.SCRAPED).length,
        qualityScore,
        recommendationCount: recommendations.length
      });

      return testResult;

    } catch (error) {
      logger.error('Test scrape failed', { error });
      throw new Error(`Test scraping failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate test scrape results and provide manual confirmation interface
   */
  async validateTestResults(testResult: TestScrapeResult): Promise<{
    validationSummary: string;
    criticalIssues: string[];
    recommendations: string[];
    shouldProceed: boolean;
  }> {
    logger.info('Validating test scrape results', { 
      resultCount: testResult.results.length,
      qualityScore: testResult.qualityScore 
    });

    const criticalIssues: string[] = [];
    const recommendations: string[] = [...testResult.recommendations];

    // Check for critical failures
    const failedScrapes = testResult.results.filter(r => r.status === ContentStatus.FAILED);
    if (failedScrapes.length > 0) {
      criticalIssues.push(`${failedScrapes.length} out of ${testResult.results.length} test scrapes failed`);
    }

    // Check quality threshold
    if (testResult.qualityScore < this.config.qualityThreshold) {
      criticalIssues.push(`Quality score (${testResult.qualityScore.toFixed(2)}) below threshold (${this.config.qualityThreshold})`);
    }

    // Check for content extraction issues
    const lowQualityContent = testResult.results.filter(r => 
      r.qualityMetrics.extractionConfidence < 0.5
    );
    if (lowQualityContent.length > 0) {
      criticalIssues.push(`${lowQualityContent.length} pages have low extraction confidence`);
    }

    // Check for DFSA-specific content
    const nonDFSAContent = testResult.results.filter(r => 
      !this.containsDFSAContent(r.content, r.title)
    );
    if (nonDFSAContent.length > testResult.results.length * 0.5) {
      criticalIssues.push('More than 50% of scraped content may not be DFSA-related');
    }

    // Generate validation summary
    const validationSummary = this.generateValidationSummary(testResult, criticalIssues);
    
    // Determine if we should proceed
    const shouldProceed = criticalIssues.length === 0 && testResult.qualityScore >= this.config.qualityThreshold;

    if (!shouldProceed) {
      recommendations.push('Review and adjust scraping parameters before proceeding');
      recommendations.push('Consider testing with different URLs or configurations');
    }

    return {
      validationSummary,
      criticalIssues,
      recommendations,
      shouldProceed
    };
  }

  /**
   * Generate detailed test report for manual review
   */
  generateTestReport(testResult: TestScrapeResult): string {
    const report = [];
    
    report.push('# DFSA Rulebook Test Scraping Report');
    report.push('');
    report.push(`**Test Date:** ${new Date().toISOString()}`);
    report.push(`**Sample Size:** ${testResult.urls.length} URLs`);
    report.push(`**Overall Quality Score:** ${(testResult.qualityScore * 100).toFixed(1)}%`);
    report.push('');
    
    // Summary statistics
    const successful = testResult.results.filter(r => r.status === ContentStatus.SCRAPED).length;
    const failed = testResult.results.filter(r => r.status === ContentStatus.FAILED).length;
    
    report.push('## Summary Statistics');
    report.push(`- Successful scrapes: ${successful}/${testResult.results.length}`);
    report.push(`- Failed scrapes: ${failed}/${testResult.results.length}`);
    report.push(`- Average word count: ${this.calculateAverageWordCount(testResult.results)}`);
    report.push(`- Average extraction confidence: ${(this.calculateAverageConfidence(testResult.results) * 100).toFixed(1)}%`);
    report.push('');
    
    // Individual results
    report.push('## Individual Results');
    testResult.results.forEach((result, index) => {
      report.push(`### ${index + 1}. ${result.title || 'Untitled'}`);
      report.push(`**URL:** ${result.url}`);
      report.push(`**Status:** ${result.status}`);
      report.push(`**Word Count:** ${result.qualityMetrics.wordCount}`);
      report.push(`**Extraction Confidence:** ${(result.qualityMetrics.extractionConfidence * 100).toFixed(1)}%`);
      report.push(`**Content Type:** ${result.contentType}`);
      
      if (result.qualityMetrics.validationErrors.length > 0) {
        report.push(`**Issues:** ${result.qualityMetrics.validationErrors.join(', ')}`);
      }
      
      report.push('');
    });
    
    // Recommendations
    if (testResult.recommendations.length > 0) {
      report.push('## Recommendations');
      testResult.recommendations.forEach(rec => {
        report.push(`- ${rec}`);
      });
      report.push('');
    }
    
    return report.join('\n');
  }

  // Private helper methods

  private selectSampleUrls(urls: string[]): string[] {
    if (urls.length <= this.config.sampleSize) {
      return [...urls];
    }

    const sampleUrls: string[] = [];
    
    if (this.config.includeVariedPages) {
      // Try to include varied page types
      const mainPages = urls.filter(url => 
        url.includes('dubai-financial-services-authority-dfsa') ||
        url.includes('general-module') ||
        url.includes('conduct-of-business')
      );
      
      const otherPages = urls.filter(url => !mainPages.includes(url));
      
      // Include some main pages
      const mainSample = mainPages.slice(0, Math.min(2, this.config.sampleSize));
      sampleUrls.push(...mainSample);
      
      // Fill remaining with other pages
      const remainingSlots = this.config.sampleSize - mainSample.length;
      if (remainingSlots > 0) {
        const otherSample = this.getRandomSample(otherPages, remainingSlots);
        sampleUrls.push(...otherSample);
      }
    } else {
      // Random sampling
      sampleUrls.push(...this.getRandomSample(urls, this.config.sampleSize));
    }

    return sampleUrls;
  }

  private getRandomSample<T>(array: T[], size: number): T[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, size);
  }

  private async scrapeSampleUrls(urls: string[]) {
    const results = [];
    
    for (const url of urls) {
      try {
        logger.info('Test scraping URL', { url });
        
        const result = await this.brightdataClient.scrapeUrl(url, {
          waitForTimeout: 10000,
          blockResources: ['image', 'stylesheet', 'font'],
          solveCaptcha: true,
          captchaTimeout: 30000
        });
        
        results.push(result);
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        logger.error('Test scrape failed for URL', { url, error });
        results.push({
          url,
          content: '',
          title: '',
          statusCode: 0,
          headers: {},
          loadTime: 0,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    return results;
  }

  private async convertToScrapedContent(scrapedResults: any[]): Promise<ScrapedContent[]> {
    const contents: ScrapedContent[] = [];
    
    for (const result of scrapedResults) {
      const contentType = this.contentValidator.detectContentType(result.content, result.url);
      
      const metadata: ContentMetadata = {
        url: result.url,
        title: result.title || '',
        sectionHierarchy: this.extractSectionHierarchy(result.content),
        contentLength: result.content.length,
        language: 'en',
        extractedAt: new Date()
      };
      
      const scrapedContent: ScrapedContent = {
        id: uuidv4(),
        url: result.url,
        title: result.title || '',
        content: result.content,
        contentType,
        metadata,
        qualityMetrics: {
          wordCount: 0,
          structureCompleteness: 0,
          extractionConfidence: 0,
          contentTypeAccuracy: 0,
          validationErrors: []
        },
        scrapedAt: new Date(),
        status: result.error ? ContentStatus.FAILED : ContentStatus.SCRAPED
      };
      
      contents.push(scrapedContent);
    }
    
    return contents;
  }

  private async validateSampleContent(contents: ScrapedContent[]) {
    const validationResults = [];
    
    for (const content of contents) {
      const validation = await this.contentValidator.validateContent(content);
      content.qualityMetrics = validation.metrics;
      validationResults.push(validation);
    }
    
    return validationResults;
  }

  private calculateOverallQualityScore(validationResults: any[]): number {
    if (validationResults.length === 0) return 0;
    
    const totalScore = validationResults.reduce((sum, result) => 
      sum + result.metrics.extractionConfidence, 0
    );
    
    return totalScore / validationResults.length;
  }

  private generateRecommendations(validationResults: any[], qualityScore: number): string[] {
    const recommendations: string[] = [];
    
    if (qualityScore < 0.5) {
      recommendations.push('Quality score is very low - review scraping configuration');
    } else if (qualityScore < 0.7) {
      recommendations.push('Quality score is below optimal - consider adjustments');
    }
    
    const lowWordCountResults = validationResults.filter(r => r.metrics.wordCount < 100);
    if (lowWordCountResults.length > 0) {
      recommendations.push('Some pages have very low word counts - check for content blocking');
    }
    
    const structureIssues = validationResults.filter(r => r.metrics.structureCompleteness < 0.5);
    if (structureIssues.length > 0) {
      recommendations.push('Some pages have poor structure - verify CSS selectors');
    }
    
    return recommendations;
  }

  private containsDFSAContent(content: string, title: string): boolean {
    const text = (content + ' ' + title).toLowerCase();
    const indicators = ['dfsa', 'dubai financial services', 'rulebook', 'regulation', 'compliance'];
    return indicators.some(indicator => text.includes(indicator));
  }

  private generateValidationSummary(testResult: TestScrapeResult, criticalIssues: string[]): string {
    const summary = [];
    
    summary.push(`Test scraping completed for ${testResult.results.length} URLs`);
    summary.push(`Overall quality score: ${(testResult.qualityScore * 100).toFixed(1)}%`);
    
    const successful = testResult.results.filter(r => r.status === ContentStatus.SCRAPED).length;
    summary.push(`Successful scrapes: ${successful}/${testResult.results.length}`);
    
    if (criticalIssues.length > 0) {
      summary.push(`Critical issues found: ${criticalIssues.length}`);
    } else {
      summary.push('No critical issues detected');
    }
    
    return summary.join('\n');
  }

  private calculateAverageWordCount(results: ScrapedContent[]): number {
    if (results.length === 0) return 0;
    const total = results.reduce((sum, r) => sum + r.qualityMetrics.wordCount, 0);
    return Math.round(total / results.length);
  }

  private calculateAverageConfidence(results: ScrapedContent[]): number {
    if (results.length === 0) return 0;
    const total = results.reduce((sum, r) => sum + r.qualityMetrics.extractionConfidence, 0);
    return total / results.length;
  }

  private extractSectionHierarchy(content: string): string[] {
    const hierarchy: string[] = [];
    const headingMatches = content.match(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi);
    
    if (headingMatches) {
      headingMatches.forEach(match => {
        const textMatch = match.match(/>([^<]+)</);
        if (textMatch && textMatch[1]) {
          const text = textMatch[1].trim();
          if (text.length > 0) {
            hierarchy.push(text);
          }
        }
      });
    }
    
    return hierarchy.slice(0, 5); // Limit to top 5 headings
  }
}