import { TestScrapeResult, QualityGateResult } from '../types';
import { logger } from '../utils/logger';
import * as readline from 'readline';

export interface ConfirmationResult {
  approved: boolean;
  feedback: string;
  adjustments: string[];
  timestamp: Date;
}

export interface ConfirmationPrompt {
  title: string;
  summary: string;
  details: string[];
  recommendations: string[];
  criticalIssues: string[];
}

/**
 * ManualConfirmation - Handles manual confirmation workflows for test scraping
 * and quality gate validation before proceeding with full-scale operations
 */
export class ManualConfirmation {
  private rl: readline.Interface;

  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  /**
   * Request manual confirmation for test scrape results
   */
  async confirmTestScrapeResults(testResult: TestScrapeResult): Promise<ConfirmationResult> {
    logger.info('Requesting manual confirmation for test scrape results');

    const prompt = this.createTestScrapePrompt(testResult);
    
    console.log('\n' + '='.repeat(80));
    console.log('🧪 TEST SCRAPING RESULTS - MANUAL CONFIRMATION REQUIRED');
    console.log('='.repeat(80));
    
    console.log(`\n📊 ${prompt.title}`);
    console.log(`\n${prompt.summary}`);
    
    if (prompt.details.length > 0) {
      console.log('\n📋 Details:');
      prompt.details.forEach(detail => console.log(`  • ${detail}`));
    }
    
    if (prompt.criticalIssues.length > 0) {
      console.log('\n⚠️  Critical Issues:');
      prompt.criticalIssues.forEach(issue => console.log(`  ❌ ${issue}`));
    }
    
    if (prompt.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      prompt.recommendations.forEach(rec => console.log(`  • ${rec}`));
    }
    
    console.log('\n' + '-'.repeat(80));
    
    // Get user decision
    const approved = await this.getUserApproval();
    
    let feedback = '';
    let adjustments: string[] = [];
    
    if (!approved) {
      feedback = await this.getUserFeedback();
      adjustments = await this.getUserAdjustments();
    }
    
    const result: ConfirmationResult = {
      approved,
      feedback,
      adjustments,
      timestamp: new Date()
    };
    
    logger.info('Manual confirmation completed', { 
      approved, 
      hasFeedback: feedback.length > 0,
      adjustmentCount: adjustments.length 
    });
    
    return result;
  }

  /**
   * Request manual confirmation for quality gate results
   */
  async confirmQualityGate(qualityResult: QualityGateResult): Promise<ConfirmationResult> {
    logger.info('Requesting manual confirmation for quality gate results');

    console.log('\n' + '='.repeat(80));
    console.log('🚦 QUALITY GATE VALIDATION - MANUAL CONFIRMATION REQUIRED');
    console.log('='.repeat(80));
    
    console.log(`\n📊 Quality Gate Status: ${qualityResult.passed ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`📈 Overall Score: ${(qualityResult.score * 100).toFixed(1)}%`);
    console.log(`🔍 Issues Found: ${qualityResult.issues.length}`);
    
    if (qualityResult.issues.length > 0) {
      console.log('\n⚠️  Quality Issues:');
      qualityResult.issues.forEach(issue => {
        const severity = issue.severity === 'high' ? '🔴' : 
                        issue.severity === 'medium' ? '🟡' : '🟢';
        console.log(`  ${severity} ${issue.type.toUpperCase()}: ${issue.description}`);
        console.log(`     Affected URLs: ${issue.affectedUrls.length}`);
      });
    }
    
    if (qualityResult.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      qualityResult.recommendations.forEach(rec => console.log(`  • ${rec}`));
    }
    
    console.log('\n' + '-'.repeat(80));
    
    const approved = await this.getUserApproval();
    
    let feedback = '';
    let adjustments: string[] = [];
    
    if (!approved) {
      feedback = await this.getUserFeedback();
      adjustments = await this.getUserAdjustments();
    }
    
    return {
      approved,
      feedback,
      adjustments,
      timestamp: new Date()
    };
  }

  /**
   * Display detailed test results for review
   */
  displayDetailedResults(testResult: TestScrapeResult): void {
    console.log('\n' + '='.repeat(80));
    console.log('📄 DETAILED TEST SCRAPING RESULTS');
    console.log('='.repeat(80));
    
    testResult.results.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.title || 'Untitled Page'}`);
      console.log(`   URL: ${result.url}`);
      console.log(`   Status: ${result.status}`);
      console.log(`   Word Count: ${result.qualityMetrics.wordCount}`);
      console.log(`   Extraction Confidence: ${(result.qualityMetrics.extractionConfidence * 100).toFixed(1)}%`);
      console.log(`   Content Type: ${result.contentType}`);
      
      if (result.qualityMetrics.validationErrors.length > 0) {
        console.log(`   Issues: ${result.qualityMetrics.validationErrors.join(', ')}`);
      }
      
      // Show content preview
      const preview = result.content
        .replace(/<[^>]*>/g, ' ')
        .replace(/\s+/g, ' ')
        .trim()
        .substring(0, 200);
      console.log(`   Preview: ${preview}...`);
    });
  }

  /**
   * Generate confirmation report
   */
  generateConfirmationReport(
    testResult: TestScrapeResult, 
    confirmation: ConfirmationResult
  ): string {
    const report = [];
    
    report.push('# Manual Confirmation Report');
    report.push('');
    report.push(`**Confirmation Date:** ${confirmation.timestamp.toISOString()}`);
    report.push(`**Decision:** ${confirmation.approved ? 'APPROVED' : 'REJECTED'}`);
    report.push(`**Test Quality Score:** ${(testResult.qualityScore * 100).toFixed(1)}%`);
    report.push('');
    
    if (!confirmation.approved) {
      report.push('## Rejection Feedback');
      report.push(confirmation.feedback);
      report.push('');
      
      if (confirmation.adjustments.length > 0) {
        report.push('## Requested Adjustments');
        confirmation.adjustments.forEach(adj => {
          report.push(`- ${adj}`);
        });
        report.push('');
      }
    }
    
    report.push('## Test Results Summary');
    report.push(`- Total URLs tested: ${testResult.results.length}`);
    report.push(`- Successful scrapes: ${testResult.results.filter(r => r.status === 'scraped').length}`);
    report.push(`- Failed scrapes: ${testResult.results.filter(r => r.status === 'failed').length}`);
    report.push(`- Average word count: ${this.calculateAverageWordCount(testResult.results)}`);
    report.push('');
    
    if (testResult.recommendations.length > 0) {
      report.push('## System Recommendations');
      testResult.recommendations.forEach(rec => {
        report.push(`- ${rec}`);
      });
    }
    
    return report.join('\n');
  }

  /**
   * Close the confirmation interface
   */
  close(): void {
    this.rl.close();
  }

  // Private helper methods

  private createTestScrapePrompt(testResult: TestScrapeResult): ConfirmationPrompt {
    const successful = testResult.results.filter(r => r.status === 'scraped').length;
    const failed = testResult.results.filter(r => r.status === 'failed').length;
    
    const title = `Test Scraping Results (${testResult.results.length} URLs)`;
    
    const summary = [
      `Quality Score: ${(testResult.qualityScore * 100).toFixed(1)}%`,
      `Successful: ${successful}/${testResult.results.length}`,
      `Failed: ${failed}/${testResult.results.length}`
    ].join(' | ');
    
    const details = [
      `Average word count: ${this.calculateAverageWordCount(testResult.results)}`,
      `Average extraction confidence: ${(this.calculateAverageConfidence(testResult.results) * 100).toFixed(1)}%`,
      `Content types detected: ${this.getUniqueContentTypes(testResult.results).join(', ')}`
    ];
    
    const criticalIssues = [];
    if (failed > 0) {
      criticalIssues.push(`${failed} scraping failures detected`);
    }
    if (testResult.qualityScore < 0.5) {
      criticalIssues.push('Quality score is critically low');
    }
    
    return {
      title,
      summary,
      details,
      recommendations: testResult.recommendations,
      criticalIssues
    };
  }

  private async getUserApproval(): Promise<boolean> {
    return new Promise((resolve) => {
      const askApproval = () => {
        this.rl.question('\n❓ Do you approve these results and want to proceed with full scraping? (y/n): ', (answer) => {
          const response = answer.toLowerCase().trim();
          if (response === 'y' || response === 'yes') {
            resolve(true);
          } else if (response === 'n' || response === 'no') {
            resolve(false);
          } else {
            console.log('Please answer with y/yes or n/no');
            askApproval();
          }
        });
      };
      askApproval();
    });
  }

  private async getUserFeedback(): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question('\n💬 Please provide feedback on why you rejected the results: ', (feedback) => {
        resolve(feedback.trim());
      });
    });
  }

  private async getUserAdjustments(): Promise<string[]> {
    return new Promise((resolve) => {
      this.rl.question('\n🔧 What adjustments would you like to make? (comma-separated, or press Enter to skip): ', (adjustments) => {
        if (!adjustments.trim()) {
          resolve([]);
        } else {
          const adjustmentList = adjustments
            .split(',')
            .map(adj => adj.trim())
            .filter(adj => adj.length > 0);
          resolve(adjustmentList);
        }
      });
    });
  }

  private calculateAverageWordCount(results: any[]): number {
    if (results.length === 0) return 0;
    const total = results.reduce((sum, r) => sum + (r.qualityMetrics?.wordCount || 0), 0);
    return Math.round(total / results.length);
  }

  private calculateAverageConfidence(results: any[]): number {
    if (results.length === 0) return 0;
    const total = results.reduce((sum, r) => sum + (r.qualityMetrics?.extractionConfidence || 0), 0);
    return total / results.length;
  }

  private getUniqueContentTypes(results: any[]): string[] {
    const types = new Set(results.map(r => r.contentType));
    return Array.from(types);
  }
}