import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { logger } from '../utils/logger';
import { URLDiscoveryService, URLDiscoveryResult, URLDiscoveryConfig } from './interfaces';
import { URLValidator } from './url-validator';
import { RobotsParser } from './robots-parser';
import { SitemapParser } from './sitemap-parser';

export class DFSAURLDiscoveryService implements URLDiscoveryService {
  private readonly validator: URLValidator;
  private readonly robotsParser: RobotsParser;
  private readonly sitemapParser: SitemapParser;
  private readonly config: URLDiscoveryConfig;

  constructor(config?: Partial<URLDiscoveryConfig>) {
    this.validator = new URLValidator();
    this.robotsParser = new RobotsParser();
    this.sitemapParser = new SitemapParser();
    
    this.config = {
      maxDepth: 5,
      maxUrls: 10000,
      respectRobotsTxt: true,
      crawlDelay: 1000,
      userAgent: 'DFSA-Scraper/1.0',
      includePatterns: [
        /\/rulebook\//,
        /\/rules\//,
        /\/guidance\//,
        /\/consultation\//,
        /\/policy\//
      ],
      excludePatterns: [
        /\/search\?/,
        /\/print\//,
        /\.pdf$/,
        /\/images?\//,
        /\/css\//,
        /\/js\//
      ],
      followExternalLinks: false,
      ...config
    };
  }

  /**
   * Comprehensive URL discovery - discovers all URLs before scraping
   */
  async discoverComprehensive(baseUrl: string): Promise<string[]> {
    const startTime = Date.now();
    logger.info('Starting comprehensive URL discovery', { baseUrl });

    try {
      // Step 1: Check robots.txt
      const robotsInfo = await this.robotsParser.parseRobotsTxt(baseUrl);
      
      if (!robotsInfo.allowed) {
        logger.warn('Robots.txt disallows crawling', { baseUrl });
        return [];
      }

      // Step 2: Parse sitemaps if available
      let sitemapUrls: string[] = [];
      if (robotsInfo.sitemapUrls.length > 0) {
        logger.info('Found sitemaps in robots.txt', { count: robotsInfo.sitemapUrls.length });
        const sitemapEntries = await this.sitemapParser.parseSitemaps(robotsInfo.sitemapUrls);
        sitemapUrls = sitemapEntries.map(entry => entry.url);
      }

      // Step 3: Crawl for additional URLs
      const crawledUrls = await this.crawlForUrls(baseUrl, robotsInfo.crawlDelay);

      // Step 4: Combine and validate all URLs
      const allUrls = [...new Set([...sitemapUrls, ...crawledUrls])];
      const { valid: validUrls, invalid: invalidUrls } = this.validator.validateUrls(allUrls);

      // Step 5: Deduplicate
      const { unique: uniqueUrls, duplicatesRemoved } = this.validator.deduplicateUrls(validUrls);

      const discoveryTime = Date.now() - startTime;
      
      logger.info('Comprehensive URL discovery completed', {
        baseUrl,
        totalFound: allUrls.length,
        validUrls: validUrls.length,
        uniqueUrls: uniqueUrls.length,
        duplicatesRemoved,
        invalidUrls: invalidUrls.length,
        discoveryTime
      });

      return uniqueUrls;

    } catch (error) {
      logger.error('Error in comprehensive URL discovery:', { baseUrl, error });
      throw error;
    }
  }

  /**
   * Recursive URL discovery - discovers URLs progressively while scraping
   */
  async *discoverRecursive(baseUrl: string): AsyncGenerator<string> {
    logger.info('Starting recursive URL discovery', { baseUrl });

    const visited = new Set<string>();
    const queue: Array<{ url: string; depth: number }> = [{ url: baseUrl, depth: 0 }];
    
    // Check robots.txt first
    const robotsInfo = await this.robotsParser.parseRobotsTxt(baseUrl);
    if (!robotsInfo.allowed) {
      logger.warn('Robots.txt disallows crawling', { baseUrl });
      return;
    }

    const crawlDelay = robotsInfo.crawlDelay || this.config.crawlDelay;
    let browser: Browser | null = null;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      while (queue.length > 0 && visited.size < this.config.maxUrls) {
        const { url, depth } = queue.shift()!;

        if (visited.has(url) || depth > this.config.maxDepth) {
          continue;
        }

        visited.add(url);

        // Validate URL before yielding
        const validation = this.validator.validateUrl(url);
        if (!validation.isValid) {
          continue;
        }

        yield validation.normalizedUrl || url;

        // If we haven't reached max depth, discover more URLs from this page
        if (depth < this.config.maxDepth) {
          try {
            const newUrls = await this.extractUrlsFromPage(browser, url);
            
            for (const newUrl of newUrls) {
              if (!visited.has(newUrl)) {
                queue.push({ url: newUrl, depth: depth + 1 });
              }
            }

            // Respect crawl delay
            if (crawlDelay > 0) {
              await this.delay(crawlDelay);
            }

          } catch (error) {
            logger.error('Error extracting URLs from page:', { url, error });
          }
        }
      }

    } finally {
      if (browser) {
        await browser.close();
      }
    }

    logger.info('Recursive URL discovery completed', {
      baseUrl,
      totalDiscovered: visited.size
    });
  }

  /**
   * Crawl website to discover URLs
   */
  private async crawlForUrls(baseUrl: string, crawlDelay?: number): Promise<string[]> {
    const allUrls: string[] = [];
    const visited = new Set<string>();
    const queue = [baseUrl];
    
    let browser: Browser | null = null;

    try {
      browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      while (queue.length > 0 && visited.size < this.config.maxUrls) {
        const url = queue.shift()!;
        
        if (visited.has(url)) {
          continue;
        }

        visited.add(url);
        
        try {
          const pageUrls = await this.extractUrlsFromPage(browser, url);
          allUrls.push(...pageUrls);
          
          // Add new URLs to queue for further crawling
          for (const pageUrl of pageUrls) {
            if (!visited.has(pageUrl) && this.shouldCrawlUrl(pageUrl)) {
              queue.push(pageUrl);
            }
          }

          // Respect crawl delay
          const delay = crawlDelay || this.config.crawlDelay;
          if (delay > 0) {
            await this.delay(delay);
          }

        } catch (error) {
          logger.error('Error crawling URL:', { url, error });
        }
      }

    } finally {
      if (browser) {
        await browser.close();
      }
    }

    return allUrls;
  }

  /**
   * Extract URLs from a single page
   */
  private async extractUrlsFromPage(browser: Browser, url: string): Promise<string[]> {
    const page = await browser.newPage();
    
    try {
      await page.setUserAgent(this.config.userAgent);
      
      // Set timeout and wait for network idle
      await page.goto(url, { 
        waitUntil: 'networkidle2', 
        timeout: 30000 
      });

      // Extract all links from the page
      const urls = await page.evaluate(() => {
        const links = Array.from(document.querySelectorAll('a[href]'));
        return links.map(link => {
          const href = (link as HTMLAnchorElement).href;
          return href;
        }).filter(href => href && href.startsWith('http'));
      });

      logger.debug('Extracted URLs from page', { url, count: urls.length });
      return urls;

    } catch (error) {
      logger.error('Error extracting URLs from page:', { url, error });
      return [];
    } finally {
      await page.close();
    }
  }

  /**
   * Check if URL should be crawled for more links
   */
  private shouldCrawlUrl(url: string): boolean {
    // Only crawl DFSA domain URLs
    if (!/^https?:\/\/(?:www\.)?dfsaen\.thomsonreuters\.com/.test(url)) {
      return false;
    }

    // Check include patterns
    const hasIncludePattern = this.config.includePatterns.some(pattern => 
      pattern.test(url)
    );

    if (!hasIncludePattern) {
      return false;
    }

    // Check exclude patterns
    const hasExcludePattern = this.config.excludePatterns.some(pattern => 
      pattern.test(url)
    );

    return !hasExcludePattern;
  }

  /**
   * Utility function to add delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}