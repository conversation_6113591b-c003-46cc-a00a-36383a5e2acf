import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer-core';
import { logger } from '../utils/logger';

export interface BrightdataConfig {
  username: string;
  password: string;
  zone?: string;
  sessionId?: string;
  country?: string;
  timeout?: number;
  userAgent?: string;
  rateLimitDelay?: number; // Delay between requests in ms
}

export interface ScrapingResult {
  url: string;
  content: string;
  title: string;
  statusCode: number;
  headers: Record<string, string>;
  loadTime: number;
  screenshot?: Buffer;
  error?: string;
}

export interface ScrapingOptions {
  waitForSelector?: string;
  waitForTimeout?: number;
  takeScreenshot?: boolean;
  blockResources?: string[];
  customHeaders?: Record<string, string>;
  viewport?: {
    width: number;
    height: number;
  };
  solveCaptcha?: boolean;
  captchaTimeout?: number;
}

/**
 * Brightdata Browser API Client
 * Based on official Bright Data Browser API documentation
 * https://docs.brightdata.com/scraping-automation/scraping-browser/quickstart
 */
export class BrightdataClient {
  private config: BrightdataConfig;
  private browser: Browser | null = null;

  constructor(config: BrightdataConfig) {
    if (!config.username || !config.password) {
      throw new Error('Brightdata username and password are required');
    }

    this.config = {
      zone: 'browser_api',
      country: 'US',
      timeout: 120000, // 2 minutes as per official docs
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      rateLimitDelay: 1000, // 1 second between requests by default
      ...config
    };
  }

  /**
   * Initialize browser connection using WebSocket (official Bright Data pattern)
   * Following official documentation: https://docs.brightdata.com/scraping-automation/scraping-browser/configuration
   * Note: Due to single navigation limit, connections are created per request
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Brightdata client initialized', {
        zone: this.config.zone,
        country: this.config.country,
        note: 'Browser connections will be created per request due to single navigation limit'
      });

      // Test connection to validate credentials
      const testResult = await this.testConnection();
      if (!testResult) {
        throw new Error('Failed to validate Brightdata credentials');
      }
      
      logger.info('Brightdata credentials validated successfully');
    } catch (error) {
      logger.error('Failed to initialize Brightdata client:', error);
      throw error;
    }
  }

  /**
   * Scrape a single URL using Brightdata proxy
   * Note: Each URL requires a new browser session due to Brightdata's single navigation limit
   */
  async scrapeUrl(url: string, options: ScrapingOptions = {}): Promise<ScrapingResult> {
    const startTime = Date.now();
    let browser: Browser | null = null;
    let page: Page | null = null;

    try {
      logger.info('Starting scrape request', { url, options });

      // Create new browser connection for each URL (Brightdata requirement)
      const auth = `${this.config.username}:${this.config.password}`;
      const browserWSEndpoint = `wss://${auth}@brd.superproxy.io:9222`;
      
      browser = await puppeteer.connect({ 
        browserWSEndpoint
      });

      page = await browser.newPage();

      // Set viewport if specified
      if (options.viewport) {
        await page.setViewport(options.viewport);
      }

      // Set user agent
      await page.setUserAgent(this.config.userAgent!);

      // Set custom headers if provided
      if (options.customHeaders) {
        await page.setExtraHTTPHeaders(options.customHeaders);
      }

      // Block resources if specified (following Brightdata best practices)
      if (options.blockResources && options.blockResources.length > 0) {
        await page.setRequestInterception(true);
        
        page.on('request', (request) => {
          const resourceType = request.resourceType();
          const url = request.url();
          
          const shouldBlock = options.blockResources!.some(pattern => {
            if (pattern.includes('*')) {
              const regex = new RegExp(pattern.replace(/\*/g, '.*'));
              return regex.test(url) || regex.test(resourceType);
            }
            return url.includes(pattern) || resourceType === pattern;
          });

          if (shouldBlock) {
            request.abort();
          } else {
            request.continue();
          }
        });
      }

      // Navigate to URL with timeout (following official docs pattern)
      const response = await page.goto(url, {
        waitUntil: 'networkidle2',
        timeout: this.config.timeout || 120000 // 2 minutes default as per docs
      });

      if (!response) {
        throw new Error('Failed to get response from page');
      }

      // Wait for specific selector if provided
      if (options.waitForSelector) {
        await page.waitForSelector(options.waitForSelector, {
          timeout: options.waitForTimeout || 10000
        });
      }

      // Additional wait if specified
      if (options.waitForTimeout) {
        await page.waitForTimeout(options.waitForTimeout);
      }

      // Extract page content and metadata
      const [content, title] = await Promise.all([
        page.content(),
        page.title()
      ]);

      // Handle CAPTCHA solving if requested (following official docs)
      if (options.solveCaptcha) {
        try {
          const client = await page.target().createCDPSession();
          const result = await client.send('Runtime.evaluate', {
            expression: `
              (async () => {
                try {
                  const response = await fetch('/captcha/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ detectTimeout: ${options.captchaTimeout || 30000} })
                  });
                  return await response.json();
                } catch (e) {
                  return { status: 'error', message: e.message };
                }
              })()
            `,
            awaitPromise: true
          });
          logger.info('CAPTCHA solve attempt:', { result: result.result?.value, url });
        } catch (captchaError) {
          logger.warn('CAPTCHA solving failed:', { error: captchaError, url });
        }
      }

      // Take screenshot if requested
      let screenshot: Buffer | undefined;
      if (options.takeScreenshot) {
        screenshot = await page.screenshot({ 
          fullPage: true,
          type: 'png'
        });
      }

      const loadTime = Date.now() - startTime;

      const result: ScrapingResult = {
        url,
        content,
        title,
        statusCode: response.status(),
        headers: response.headers(),
        loadTime,
        ...(screenshot && { screenshot })
      };

      logger.info('Scrape request completed successfully', {
        url,
        statusCode: result.statusCode,
        loadTime: result.loadTime,
        contentLength: content.length
      });

      return result;

    } catch (error) {
      const loadTime = Date.now() - startTime;
      logger.error('Scrape request failed:', { url, error, loadTime });

      return {
        url,
        content: '',
        title: '',
        statusCode: 0,
        headers: {},
        loadTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      if (page) {
        await page.close();
      }
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Scrape multiple URLs concurrently
   * Note: Each URL gets its own browser session due to Brightdata's single navigation limit
   */
  async scrapeUrls(
    urls: string[], 
    options: ScrapingOptions = {},
    concurrency: number = 3
  ): Promise<ScrapingResult[]> {
    logger.info('Starting batch scrape request', { 
      urlCount: urls.length, 
      concurrency,
      note: 'Each URL will use a separate browser session'
    });

    const results: ScrapingResult[] = [];
    
    // Process URLs in batches to respect concurrency limits
    // Each scrapeUrl call will create its own browser session
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency);
      
      const batchPromises = batch.map(url => 
        this.scrapeUrl(url, options)
      );

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // Add delay between batches to respect rate limits
      if (i + concurrency < urls.length) {
        await this.delay(this.config.rateLimitDelay || 1000);
      }
    }

    logger.info('Batch scrape request completed', {
      totalUrls: urls.length,
      successfulScrapes: results.filter(r => !r.error).length,
      failedScrapes: results.filter(r => r.error).length
    });

    return results;
  }

  /**
   * Test connection to Brightdata service using official test endpoint
   * Following official documentation patterns
   */
  async testConnection(): Promise<boolean> {
    let browser: Browser | null = null;
    let page: Page | null = null;

    try {
      logger.info('Testing Brightdata Browser API connection');
      
      // Create browser connection for test
      const auth = `${this.config.username}:${this.config.password}`;
      const browserWSEndpoint = `wss://${auth}@brd.superproxy.io:9222`;
      
      browser = await puppeteer.connect({ 
        browserWSEndpoint
      });

      page = await browser.newPage();
      
      // Navigate to official test URL
      const response = await page.goto('https://geo.brdtest.com/mygeo.json', {
        waitUntil: 'networkidle2',
        timeout: this.config.timeout || 30000
      });

      if (!response) {
        throw new Error('Failed to get response from test endpoint');
      }

      const content = await page.content();
      const statusCode = response.status();

      if (statusCode !== 200) {
        logger.error('Brightdata connection test failed:', { statusCode });
        return false;
      }

      // Parse geo response to verify proxy is working
      try {
        // Extract JSON from the response body
        const bodyMatch = content.match(/<body[^>]*>(.*?)<\/body>/s);
        const bodyContent = bodyMatch?.[1]?.trim() || content;
        
        // Try to parse as JSON
        const geoData = JSON.parse(bodyContent);
        logger.info('Brightdata connection test successful', { 
          country: geoData.country,
          ip: geoData.ip,
          statusCode 
        });
        return true;
      } catch (parseError) {
        logger.warn('Could not parse geo response, but connection appears successful', {
          statusCode,
          contentPreview: content.substring(0, 200)
        });
        return statusCode === 200;
      }
    } catch (error) {
      logger.error('Brightdata connection test error:', error);
      return false;
    } finally {
      if (page) {
        await page.close();
      }
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Get current session information using official geo endpoint
   * Note: Creates a new session for this request
   */
  async getSessionInfo(): Promise<{ sessionId?: string; proxyIp?: string; country?: string }> {
    try {
      const result = await this.scrapeUrl('https://geo.brdtest.com/mygeo.json');
      if (result.error) {
        return {};
      }

      // Extract JSON from the response body
      const bodyMatch = result.content.match(/<body[^>]*>(.*?)<\/body>/s);
      const bodyContent = bodyMatch?.[1]?.trim() || result.content;
      
      const geoData = JSON.parse(bodyContent);
      return {
        ...(this.config.sessionId && { sessionId: this.config.sessionId }),
        ...(geoData.ip && { proxyIp: geoData.ip }),
        ...(geoData.country && { country: geoData.country })
      };
    } catch (error) {
      logger.error('Failed to get session info:', error);
      return {};
    }
  }

  /**
   * Close browser and cleanup resources
   * Note: Individual browser sessions are closed per request
   */
  async close(): Promise<void> {
    // No persistent browser connection to close
    // Individual sessions are closed after each request
    logger.info('Brightdata client cleanup completed');
  }



  /**
   * Utility function to add delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get health status of the client
   */
  getHealthStatus(): {
    initialized: boolean;
    config: Partial<BrightdataConfig>;
  } {
    return {
      initialized: true, // Always initialized since we create connections per request
      config: {
        ...(this.config.zone && { zone: this.config.zone }),
        ...(this.config.country && { country: this.config.country }),
        ...(this.config.timeout && { timeout: this.config.timeout })
        // Exclude sensitive data like username/password
      }
    };
  }
}