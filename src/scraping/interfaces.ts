import { ScrapeConfig } from '../types';

export interface URLDiscoveryService {
  discoverRecursive(baseUrl: string): AsyncGenerator<string>;
  discoverComprehensive(baseUrl: string): Promise<string[]>;
}

export interface URLDiscoveryResult {
  urls: string[];
  totalFound: number;
  duplicatesRemoved: number;
  invalidUrls: string[];
  discoveryTime: number;
}

export interface URLValidationResult {
  isValid: boolean;
  reason?: string;
  normalizedUrl?: string;
}

export interface SitemapEntry {
  url: string;
  lastModified?: Date;
  changeFrequency?: string;
  priority?: number;
}

export interface RobotsInfo {
  allowed: boolean;
  crawlDelay?: number;
  sitemapUrls: string[];
}

export interface URLDiscoveryConfig {
  maxDepth: number;
  maxUrls: number;
  respectRobotsTxt: boolean;
  crawlDelay: number;
  userAgent: string;
  includePatterns: RegExp[];
  excludePatterns: RegExp[];
  followExternalLinks: boolean;
}