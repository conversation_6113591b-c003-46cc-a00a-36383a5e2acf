import { logger } from '../utils/logger';
import { SitemapEntry } from './interfaces';

export class SitemapParser {
  /**
   * Parse sitemap XML and extract URLs
   */
  async parseSitemap(sitemapUrl: string): Promise<SitemapEntry[]> {
    try {
      logger.info('Parsing sitemap', { url: sitemapUrl });

      const response = await fetch(sitemapUrl);
      
      if (!response.ok) {
        logger.warn('Sitemap not accessible', { 
          url: sitemapUrl, 
          status: response.status 
        });
        return [];
      }

      const xmlContent = await response.text();
      
      // Check if this is a sitemap index or regular sitemap
      if (xmlContent.includes('<sitemapindex')) {
        return await this.parseSitemapIndex(xmlContent);
      } else {
        return this.parseSitemapXml(xmlContent);
      }

    } catch (error) {
      logger.error('Error parsing sitemap:', { url: sitemapUrl, error });
      return [];
    }
  }

  /**
   * Parse sitemap index XML (contains references to other sitemaps)
   */
  private async parseSitemapIndex(xmlContent: string): Promise<SitemapEntry[]> {
    const sitemapUrls = this.extractSitemapUrls(xmlContent);
    const allEntries: SitemapEntry[] = [];

    logger.info('Found sitemap index with sitemaps', { count: sitemapUrls.length });

    // Parse each individual sitemap
    for (const sitemapUrl of sitemapUrls) {
      try {
        const entries = await this.parseSitemap(sitemapUrl);
        allEntries.push(...entries);
      } catch (error) {
        logger.error('Error parsing individual sitemap:', { url: sitemapUrl, error });
      }
    }

    return allEntries;
  }

  /**
   * Extract sitemap URLs from sitemap index
   */
  private extractSitemapUrls(xmlContent: string): string[] {
    const sitemapUrls: string[] = [];
    
    // Simple regex-based extraction (for production, consider using a proper XML parser)
    const sitemapRegex = /<sitemap>[\s\S]*?<loc>(.*?)<\/loc>[\s\S]*?<\/sitemap>/g;
    let match;

    while ((match = sitemapRegex.exec(xmlContent)) !== null) {
      if (match[1]) {
        const url = match[1].trim();
        if (this.isValidUrl(url)) {
          sitemapUrls.push(url);
        }
      }
    }

    return sitemapUrls;
  }

  /**
   * Parse regular sitemap XML
   */
  private parseSitemapXml(xmlContent: string): SitemapEntry[] {
    const entries: SitemapEntry[] = [];
    
    // Simple regex-based extraction (for production, consider using a proper XML parser)
    const urlRegex = /<url>([\s\S]*?)<\/url>/g;
    let match;

    while ((match = urlRegex.exec(xmlContent)) !== null) {
      const urlBlock = match[1];
      if (urlBlock) {
        const entry = this.parseUrlBlock(urlBlock);

        if (entry) {
          entries.push(entry);
        }
      }
    }

    logger.info('Parsed sitemap entries', { count: entries.length });
    return entries;
  }

  /**
   * Parse individual URL block from sitemap
   */
  private parseUrlBlock(urlBlock: string): SitemapEntry | null {
    const locMatch = urlBlock.match(/<loc>(.*?)<\/loc>/);
    if (!locMatch) {
      return null;
    }

    if (!locMatch[1]) {
      return null;
    }

    const url = locMatch[1].trim();
    if (!this.isValidUrl(url)) {
      return null;
    }

    const entry: SitemapEntry = { url };

    // Extract optional fields
    const lastModMatch = urlBlock.match(/<lastmod>(.*?)<\/lastmod>/);
    if (lastModMatch && lastModMatch[1]) {
      const dateStr = lastModMatch[1].trim();
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        entry.lastModified = date;
      }
    }

    const changeFreqMatch = urlBlock.match(/<changefreq>(.*?)<\/changefreq>/);
    if (changeFreqMatch && changeFreqMatch[1]) {
      entry.changeFrequency = changeFreqMatch[1].trim();
    }

    const priorityMatch = urlBlock.match(/<priority>(.*?)<\/priority>/);
    if (priorityMatch && priorityMatch[1]) {
      const priority = parseFloat(priorityMatch[1].trim());
      if (!isNaN(priority)) {
        entry.priority = priority;
      }
    }

    return entry;
  }

  /**
   * Parse multiple sitemaps from URLs
   */
  async parseSitemaps(sitemapUrls: string[]): Promise<SitemapEntry[]> {
    const allEntries: SitemapEntry[] = [];

    for (const sitemapUrl of sitemapUrls) {
      try {
        const entries = await this.parseSitemap(sitemapUrl);
        allEntries.push(...entries);
      } catch (error) {
        logger.error('Error parsing sitemap:', { url: sitemapUrl, error });
      }
    }

    // Remove duplicates based on URL
    const uniqueEntries = new Map<string, SitemapEntry>();
    for (const entry of allEntries) {
      uniqueEntries.set(entry.url, entry);
    }

    const result = Array.from(uniqueEntries.values());
    logger.info('Consolidated sitemap entries', { 
      total: allEntries.length, 
      unique: result.length 
    });

    return result;
  }

  /**
   * Validate if string is a valid URL
   */
  private isValidUrl(urlString: string): boolean {
    try {
      new URL(urlString);
      return true;
    } catch {
      return false;
    }
  }
}