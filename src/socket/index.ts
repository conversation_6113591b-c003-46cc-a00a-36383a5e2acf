/**
 * Socket.IO Handlers
 * 
 * Sets up Socket.IO event handlers for real-time communication
 */

import { Server as SocketIOServer, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { ScrapingOrchestrator } from '../scraping/scraping-orchestrator';
import { config } from '../config/environment';
import { JobStatus, ScrapeJob, JobProgress } from '../types';

// Type alias for backward compatibility
type ScrapingProgress = JobProgress;

// Create scraping orchestrator instance
const orchestrator = new ScrapingOrchestrator({
  brightdata: {
    username: config.brightdata.username,
    password: config.brightdata.password,
    zone: config.brightdata.zone
  },
  scraping: {
    maxConcurrency: config.scraping.maxConcurrent,
    delayBetweenRequests: config.scraping.defaultDelay,
    retryAttempts: 3,
    qualityGateThreshold: config.scraping.qualityGateThreshold,
    testScrapeSize: 5,
    timeout: 60000
  },
  jobManager: {
    concurrency: config.scraping.maxConcurrent,
    retryAttempts: 3,
    retryDelay: 5000
  }
});

// Active connections
const activeConnections = new Map<string, Socket>();

// Setup Socket.IO handlers
export function setupSocketHandlers(io: SocketIOServer): void {
  // Set up job callbacks
  orchestrator.jobManager.setCallbacks({
    onProgress: async (jobId: string, progress: ScrapingProgress) => {
      logger.debug('Job progress update', { jobId, progress });
      io.to(`job:${jobId}`).emit('job:progress', { jobId, progress });
    },
    
    onError: async (jobId: string, error: any) => {
      logger.warn('Job error occurred', { jobId, error });
      io.to(`job:${jobId}`).emit('job:error', { jobId, error });
    },
    
    onComplete: async (jobId: string, results: any[]) => {
      logger.info('Job completed successfully', { 
        jobId, 
        resultCount: results.length 
      });
      io.to(`job:${jobId}`).emit('job:complete', { 
        jobId, 
        resultCount: results.length 
      });
    },
    
    onFailed: async (jobId: string, error: Error) => {
      logger.error('Job failed completely', { jobId, error: error.message });
      io.to(`job:${jobId}`).emit('job:failed', { 
        jobId, 
        error: error.message 
      });
    }
  });

  // Connection handler
  io.on('connection', (socket: Socket) => {
    const clientId = socket.id;
    logger.info('Client connected', { clientId });
    activeConnections.set(clientId, socket);

    // Subscribe to job updates
    socket.on('subscribe:job', async (jobId: string) => {
      logger.info('Client subscribed to job updates', { clientId, jobId });
      socket.join(`job:${jobId}`);
      
      // Send initial job status
      try {
        const job = await orchestrator.getJobStatus(jobId);
        if (job) {
          socket.emit('job:status', job);
        } else {
          socket.emit('job:not_found', { jobId });
        }
      } catch (error) {
        logger.error('Error getting job status', { error, jobId });
        socket.emit('job:error', { 
          jobId, 
          error: 'Failed to get job status' 
        });
      }
    });

    // Unsubscribe from job updates
    socket.on('unsubscribe:job', (jobId: string) => {
      logger.info('Client unsubscribed from job updates', { clientId, jobId });
      socket.leave(`job:${jobId}`);
    });

    // Get all jobs
    socket.on('get:jobs', async (data: { status?: JobStatus, limit?: number }, callback) => {
      try {
        const jobs = await orchestrator.getJobs(data.status, data.limit);
        callback({ success: true, jobs });
      } catch (error) {
        logger.error('Error getting jobs', { error });
        callback({ 
          success: false, 
          error: 'Failed to get jobs' 
        });
      }
    });

    // Get job status
    socket.on('get:job', async (jobId: string, callback) => {
      try {
        const job = await orchestrator.getJobStatus(jobId);
        if (job) {
          callback({ success: true, job });
        } else {
          callback({ 
            success: false, 
            error: 'Job not found' 
          });
        }
      } catch (error) {
        logger.error('Error getting job status', { error, jobId });
        callback({ 
          success: false, 
          error: 'Failed to get job status' 
        });
      }
    });

    // Cancel job
    socket.on('cancel:job', async (jobId: string, callback) => {
      try {
        const success = await orchestrator.cancelJob(jobId);
        if (success) {
          callback({ success: true });
          io.to(`job:${jobId}`).emit('job:cancelled', { jobId });
        } else {
          callback({ 
            success: false, 
            error: 'Failed to cancel job' 
          });
        }
      } catch (error) {
        logger.error('Error cancelling job', { error, jobId });
        callback({ 
          success: false, 
          error: 'Failed to cancel job' 
        });
      }
    });

    // Retry job
    socket.on('retry:job', async (jobId: string, callback) => {
      try {
        const success = await orchestrator.retryJob(jobId);
        if (success) {
          callback({ success: true });
          io.to(`job:${jobId}`).emit('job:retried', { jobId });
        } else {
          callback({ 
            success: false, 
            error: 'Failed to retry job' 
          });
        }
      } catch (error) {
        logger.error('Error retrying job', { error, jobId });
        callback({ 
          success: false, 
          error: 'Failed to retry job' 
        });
      }
    });

    // Get queue stats
    socket.on('get:stats', async (callback) => {
      try {
        const stats = await orchestrator.getQueueStats();
        callback({ success: true, stats });
      } catch (error) {
        logger.error('Error getting queue stats', { error });
        callback({ 
          success: false, 
          error: 'Failed to get queue stats' 
        });
      }
    });

    // Disconnect handler
    socket.on('disconnect', () => {
      logger.info('Client disconnected', { clientId });
      activeConnections.delete(clientId);
    });
  });

  // Log active connections count every minute
  setInterval(() => {
    logger.debug('Active socket connections', { 
      count: activeConnections.size 
    });
  }, 60000);
}

// Export for testing
export const getActiveConnections = () => activeConnections;