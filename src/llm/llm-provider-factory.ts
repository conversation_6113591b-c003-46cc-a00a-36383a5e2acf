/**
 * LLM Provider Factory
 * 
 * Factory for creating LLM providers
 */

import { LLMProvider as AbstractLLMProvider, LLMProviderConfig } from './llm-provider';
import { LLMServiceConfig, LLMProvider } from './llm-service';
import { OpenAIProvider, OpenAIProviderConfig } from './providers/openai-provider';
import { AnthropicProvider, AnthropicProviderConfig } from './providers/anthropic-provider';
import { LocalModelProvider, LocalModelProviderConfig } from './providers/local-model-provider';

export interface ProviderRecommendation {
  provider: string;
  model: string;
  description: string;
  strengths: string[];
  weaknesses: string[];
  costEstimate: string;
  contextWindow: number;
}

export interface ProviderRequirements {
  provider: string;
  requiredEnvVars: string[];
  requiredConfig: string[];
  setupInstructions: string;
  documentationUrl: string;
}

/**
 * Factory for creating LLM providers
 */
export class LLMProviderFactory {
  /**
   * Create a new LLM provider
   * @param config Provider configuration
   * @returns LLM provider
   */
  public static create(config: LLMProviderConfig): AbstractLLMProvider {
    // Validate configuration
    this.validateConfig(config);

    // Create provider based on type
    switch (config.provider) {
      case 'openai':
        return new OpenAIProvider(config as OpenAIProviderConfig);
      case 'anthropic':
        return new AnthropicProvider(config as AnthropicProviderConfig);
      case 'local':
        return new LocalModelProvider(config as LocalModelProviderConfig);
      default:
        throw new Error(`Unsupported provider: ${config.provider}`);
    }
  }

  /**
   * Get supported providers
   * @returns List of supported providers
   */
  public static getSupportedProviders(): string[] {
    return ['openai', 'anthropic', 'local'];
  }

  /**
   * Get provider requirements
   * @param provider Provider name
   * @returns Provider requirements
   */
  public static getProviderRequirements(provider: string): ProviderRequirements {
    switch (provider) {
      case 'openai':
        return {
          provider: 'openai',
          requiredEnvVars: ['OPENAI_API_KEY'],
          requiredConfig: ['model'],
          setupInstructions: 'Get an API key from https://platform.openai.com/account/api-keys',
          documentationUrl: 'https://platform.openai.com/docs/api-reference'
        };
      case 'anthropic':
        return {
          provider: 'anthropic',
          requiredEnvVars: ['ANTHROPIC_API_KEY'],
          requiredConfig: ['model'],
          setupInstructions: 'Get an API key from https://console.anthropic.com/account/keys',
          documentationUrl: 'https://docs.anthropic.com/claude/reference'
        };
      case 'local':
        return {
          provider: 'local',
          requiredEnvVars: [],
          requiredConfig: ['model'],
          setupInstructions: 'Install Ollama from https://ollama.ai/ and run it locally',
          documentationUrl: 'https://github.com/ollama/ollama'
        };
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Get recommended models for a provider
   * @param provider Provider name
   * @returns Recommended models
   */
  public static getRecommendedModels(provider: string): Array<{
    id: string;
    description: string;
    contextWindow: number;
    costEstimate: string;
    useCase: string;
  }> {
    switch (provider) {
      case 'openai':
        return [
          {
            id: 'gpt-4o',
            description: 'Most capable OpenAI model with vision and optimal performance',
            contextWindow: 128000,
            costEstimate: '$0.005/1K input tokens, $0.015/1K output tokens',
            useCase: 'Complex reasoning, code generation, creative content'
          },
          {
            id: 'gpt-4-turbo',
            description: 'Powerful model with large context window',
            contextWindow: 128000,
            costEstimate: '$0.01/1K input tokens, $0.03/1K output tokens',
            useCase: 'Long-form content, complex reasoning'
          },
          {
            id: 'gpt-3.5-turbo',
            description: 'Fast and cost-effective model',
            contextWindow: 16385,
            costEstimate: '$0.0015/1K input tokens, $0.002/1K output tokens',
            useCase: 'General purpose, high-volume applications'
          }
        ];
      case 'anthropic':
        return [
          {
            id: 'claude-3-opus-20240229',
            description: 'Most capable Anthropic model',
            contextWindow: 200000,
            costEstimate: '$0.015/1K input tokens, $0.075/1K output tokens',
            useCase: 'Complex reasoning, research, creative content'
          },
          {
            id: 'claude-3-sonnet-20240229',
            description: 'Balanced performance and cost',
            contextWindow: 200000,
            costEstimate: '$0.003/1K input tokens, $0.015/1K output tokens',
            useCase: 'General purpose, content generation'
          },
          {
            id: 'claude-3-haiku-20240307',
            description: 'Fast and cost-effective model',
            contextWindow: 200000,
            costEstimate: '$0.00025/1K input tokens, $0.00125/1K output tokens',
            useCase: 'High-volume applications, simple tasks'
          }
        ];
      case 'local':
        return [
          {
            id: 'llama3',
            description: 'Meta\'s Llama 3 model running locally',
            contextWindow: 8192,
            costEstimate: 'Free (local)',
            useCase: 'General purpose, offline use'
          },
          {
            id: 'mistral',
            description: 'Mistral AI\'s model running locally',
            contextWindow: 8192,
            costEstimate: 'Free (local)',
            useCase: 'General purpose, offline use'
          },
          {
            id: 'mixtral',
            description: 'Mixtral 8x7B model with large context window',
            contextWindow: 32768,
            costEstimate: 'Free (local)',
            useCase: 'Complex reasoning, long-form content'
          }
        ];
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Get provider recommendations based on use case
   * @param useCase Use case description
   * @returns Provider recommendations
   */
  public static getProviderRecommendations(useCase: string): ProviderRecommendation[] {
    // Parse use case to determine requirements
    const needsHighAccuracy = useCase.includes('accuracy') || useCase.includes('precise');
    const needsLongContext = useCase.includes('long') || useCase.includes('document');
    const needsLowCost = useCase.includes('cost') || useCase.includes('budget');
    const needsOffline = useCase.includes('offline') || useCase.includes('local');
    const needsSpeed = useCase.includes('fast') || useCase.includes('speed');
    
    const recommendations: ProviderRecommendation[] = [];
    
    // Add recommendations based on requirements
    if (needsHighAccuracy) {
      recommendations.push({
        provider: 'openai',
        model: 'gpt-4o',
        description: 'Most capable OpenAI model with excellent accuracy',
        strengths: ['High accuracy', 'Large context window', 'Multimodal capabilities'],
        weaknesses: ['Higher cost', 'API key required'],
        costEstimate: 'Medium-High',
        contextWindow: 128000
      });
      
      recommendations.push({
        provider: 'anthropic',
        model: 'claude-3-opus-20240229',
        description: 'Most capable Anthropic model with excellent accuracy',
        strengths: ['High accuracy', 'Very large context window', 'Strong reasoning'],
        weaknesses: ['Higher cost', 'API key required'],
        costEstimate: 'High',
        contextWindow: 200000
      });
    }
    
    if (needsLongContext) {
      recommendations.push({
        provider: 'anthropic',
        model: 'claude-3-sonnet-20240229',
        description: 'Anthropic model with very large context window',
        strengths: ['Very large context window', 'Good accuracy', 'Balanced cost'],
        weaknesses: ['API key required'],
        costEstimate: 'Medium',
        contextWindow: 200000
      });
      
      recommendations.push({
        provider: 'openai',
        model: 'gpt-4-turbo',
        description: 'OpenAI model with large context window',
        strengths: ['Large context window', 'Good accuracy'],
        weaknesses: ['Higher cost', 'API key required'],
        costEstimate: 'Medium-High',
        contextWindow: 128000
      });
    }
    
    if (needsLowCost) {
      recommendations.push({
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        description: 'Cost-effective OpenAI model',
        strengths: ['Low cost', 'Fast', 'Good for general tasks'],
        weaknesses: ['Lower accuracy than GPT-4', 'Smaller context window'],
        costEstimate: 'Low',
        contextWindow: 16385
      });
      
      recommendations.push({
        provider: 'anthropic',
        model: 'claude-3-haiku-20240307',
        description: 'Cost-effective Anthropic model',
        strengths: ['Low cost', 'Fast', 'Large context window'],
        weaknesses: ['Lower accuracy than Claude Opus/Sonnet'],
        costEstimate: 'Low',
        contextWindow: 200000
      });
      
      recommendations.push({
        provider: 'local',
        model: 'llama3',
        description: 'Free local model',
        strengths: ['No API cost', 'Privacy', 'No internet required'],
        weaknesses: ['Requires local hardware', 'Lower accuracy than cloud models'],
        costEstimate: 'Free',
        contextWindow: 8192
      });
    }
    
    if (needsOffline) {
      recommendations.push({
        provider: 'local',
        model: 'llama3',
        description: 'Offline-capable model',
        strengths: ['No internet required', 'Privacy', 'No API cost'],
        weaknesses: ['Requires local hardware', 'Lower accuracy than cloud models'],
        costEstimate: 'Free',
        contextWindow: 8192
      });
      
      recommendations.push({
        provider: 'local',
        model: 'mistral',
        description: 'Efficient offline-capable model',
        strengths: ['No internet required', 'Privacy', 'Lower hardware requirements'],
        weaknesses: ['Lower accuracy than cloud models'],
        costEstimate: 'Free',
        contextWindow: 8192
      });
    }
    
    if (needsSpeed) {
      recommendations.push({
        provider: 'anthropic',
        model: 'claude-3-haiku-20240307',
        description: 'Fast Anthropic model',
        strengths: ['Fast response time', 'Low cost', 'Large context window'],
        weaknesses: ['Lower accuracy than Claude Opus/Sonnet'],
        costEstimate: 'Low',
        contextWindow: 200000
      });
      
      recommendations.push({
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        description: 'Fast OpenAI model',
        strengths: ['Fast response time', 'Low cost'],
        weaknesses: ['Lower accuracy than GPT-4', 'Smaller context window'],
        costEstimate: 'Low',
        contextWindow: 16385
      });
      
      recommendations.push({
        provider: 'local',
        model: 'phi3:mini',
        description: 'Fast local model',
        strengths: ['Fast response time', 'Low hardware requirements', 'No API cost'],
        weaknesses: ['Lower accuracy', 'Smaller context window'],
        costEstimate: 'Free',
        contextWindow: 4096
      });
    }
    
    // If no specific requirements, provide general recommendations
    if (recommendations.length === 0) {
      recommendations.push({
        provider: 'openai',
        model: 'gpt-4o',
        description: 'Recommended for most use cases',
        strengths: ['High accuracy', 'Large context window', 'Multimodal capabilities'],
        weaknesses: ['Higher cost', 'API key required'],
        costEstimate: 'Medium-High',
        contextWindow: 128000
      });
      
      recommendations.push({
        provider: 'anthropic',
        model: 'claude-3-sonnet-20240229',
        description: 'Good balance of performance and cost',
        strengths: ['Very large context window', 'Good accuracy', 'Balanced cost'],
        weaknesses: ['API key required'],
        costEstimate: 'Medium',
        contextWindow: 200000
      });
      
      recommendations.push({
        provider: 'local',
        model: 'llama3',
        description: 'Good offline option',
        strengths: ['No API cost', 'Privacy', 'No internet required'],
        weaknesses: ['Requires local hardware', 'Lower accuracy than cloud models'],
        costEstimate: 'Free',
        contextWindow: 8192
      });
    }
    
    return recommendations;
  }

  /**
   * Get provider comparison
   * @returns Provider comparison
   */
  public static getProviderComparison(): Record<string, {
    strengths: string[];
    weaknesses: string[];
    bestModels: string[];
    pricing: string;
    apiAccess: string;
    documentation: string;
  }> {
    return {
      openai: {
        strengths: [
          'High accuracy and reasoning capabilities',
          'Extensive API features',
          'Good documentation and community support',
          'Multimodal capabilities (vision, audio)',
          'Function calling and tool use'
        ],
        weaknesses: [
          'Higher cost for advanced models',
          'Rate limiting on free tier',
          'Less transparent about model training'
        ],
        bestModels: ['gpt-4o', 'gpt-4-turbo', 'gpt-3.5-turbo'],
        pricing: 'Pay-per-token, varies by model',
        apiAccess: 'API key required, free tier available with limits',
        documentation: 'https://platform.openai.com/docs/api-reference'
      },
      anthropic: {
        strengths: [
          'Very large context windows',
          'Strong reasoning capabilities',
          'Good at following instructions',
          'Transparent about safety and limitations',
          'Tool use capabilities'
        ],
        weaknesses: [
          'Higher cost for advanced models',
          'Fewer features than OpenAI',
          'Less community support'
        ],
        bestModels: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
        pricing: 'Pay-per-token, varies by model',
        apiAccess: 'API key required, no free tier',
        documentation: 'https://docs.anthropic.com/claude/reference'
      },
      local: {
        strengths: [
          'No API costs',
          'Privacy and data security',
          'No internet required',
          'Full control over deployment',
          'Customizable models'
        ],
        weaknesses: [
          'Requires local hardware',
          'Lower accuracy than cloud models',
          'Limited context windows',
          'Requires technical setup'
        ],
        bestModels: ['llama3', 'mistral', 'mixtral'],
        pricing: 'Free (hardware costs only)',
        apiAccess: 'Local API via Ollama or similar',
        documentation: 'https://github.com/ollama/ollama'
      }
    };
  }

  /**
   * Get migration guide between providers
   * @param fromProvider Source provider
   * @param toProvider Target provider
   * @returns Migration guide
   */
  public static getMigrationGuide(fromProvider: string, toProvider: string): {
    steps: string[];
    codeChanges: string;
    configChanges: Record<string, any>;
    challenges: string[];
    recommendations: string[];
  } {
    // Validate providers
    if (!this.getSupportedProviders().includes(fromProvider)) {
      throw new Error(`Unsupported source provider: ${fromProvider}`);
    }
    
    if (!this.getSupportedProviders().includes(toProvider)) {
      throw new Error(`Unsupported target provider: ${toProvider}`);
    }
    
    // OpenAI to Anthropic
    if (fromProvider === 'openai' && toProvider === 'anthropic') {
      return {
        steps: [
          '1. Sign up for Anthropic API access at https://console.anthropic.com/',
          '2. Get an API key from the Anthropic console',
          '3. Update environment variables to include ANTHROPIC_API_KEY',
          '4. Update provider configuration to use Anthropic models',
          '5. Adjust prompt templates to work well with Claude models'
        ],
        codeChanges: `
// Before (OpenAI)
const provider = new OpenAIProvider({
  apiKey: process.env.OPENAI_API_KEY,
  model: 'gpt-4o'
});

// After (Anthropic)
const provider = new AnthropicProvider({
  apiKey: process.env.ANTHROPIC_API_KEY,
  model: 'claude-3-opus-20240229'
});`,
        configChanges: {
          provider: 'anthropic',
          model: 'claude-3-opus-20240229',
          apiKey: 'process.env.ANTHROPIC_API_KEY'
        },
        challenges: [
          'Different prompt formatting between providers',
          'Different token counting methods',
          'Different rate limits and quotas',
          'Different model capabilities and limitations'
        ],
        recommendations: [
          'Start with Claude 3 Opus for highest accuracy',
          'Use Claude 3 Sonnet for a balance of performance and cost',
          'Use Claude 3 Haiku for high-volume, cost-sensitive applications',
          'Test prompts with both providers to ensure consistent results'
        ]
      };
    }
    
    // Anthropic to OpenAI
    if (fromProvider === 'anthropic' && toProvider === 'openai') {
      return {
        steps: [
          '1. Sign up for OpenAI API access at https://platform.openai.com/',
          '2. Get an API key from the OpenAI dashboard',
          '3. Update environment variables to include OPENAI_API_KEY',
          '4. Update provider configuration to use OpenAI models',
          '5. Adjust prompt templates to work well with GPT models'
        ],
        codeChanges: `
// Before (Anthropic)
const provider = new AnthropicProvider({
  apiKey: process.env.ANTHROPIC_API_KEY,
  model: 'claude-3-opus-20240229'
});

// After (OpenAI)
const provider = new OpenAIProvider({
  apiKey: process.env.OPENAI_API_KEY,
  model: 'gpt-4o'
});`,
        configChanges: {
          provider: 'openai',
          model: 'gpt-4o',
          apiKey: 'process.env.OPENAI_API_KEY'
        },
        challenges: [
          'Different prompt formatting between providers',
          'Different token counting methods',
          'Different rate limits and quotas',
          'Different model capabilities and limitations'
        ],
        recommendations: [
          'Start with GPT-4o for highest accuracy',
          'Use GPT-4-turbo for long context applications',
          'Use GPT-3.5-turbo for cost-sensitive applications',
          'Test prompts with both providers to ensure consistent results'
        ]
      };
    }
    
    // Cloud to Local
    if ((fromProvider === 'openai' || fromProvider === 'anthropic') && toProvider === 'local') {
      return {
        steps: [
          '1. Install Ollama from https://ollama.ai/',
          '2. Pull the desired model (e.g., `ollama pull llama3`)',
          '3. Start the Ollama server',
          '4. Update provider configuration to use local models',
          '5. Adjust expectations for accuracy and performance'
        ],
        codeChanges: `
// Before (Cloud Provider)
const provider = new ${fromProvider === 'openai' ? 'OpenAIProvider' : 'AnthropicProvider'}({
  apiKey: process.env.${fromProvider === 'openai' ? 'OPENAI_API_KEY' : 'ANTHROPIC_API_KEY'},
  model: '${fromProvider === 'openai' ? 'gpt-4o' : 'claude-3-opus-20240229'}'
});

// After (Local)
const provider = new LocalModelProvider({
  model: 'llama3',
  baseUrl: 'http://localhost:11434'
});`,
        configChanges: {
          provider: 'local',
          model: 'llama3',
          baseUrl: 'http://localhost:11434'
        },
        challenges: [
          'Lower accuracy compared to cloud models',
          'Hardware requirements for running models locally',
          'Limited context window for some models',
          'Different prompt formatting requirements'
        ],
        recommendations: [
          'Use Llama 3 for general purpose applications',
          'Use Mixtral for applications requiring larger context windows',
          'Consider quantized models for lower hardware requirements',
          'Test prompts thoroughly to ensure acceptable quality'
        ]
      };
    }
    
    // Local to Cloud
    if (fromProvider === 'local' && (toProvider === 'openai' || toProvider === 'anthropic')) {
      return {
        steps: [
          `1. Sign up for ${toProvider === 'openai' ? 'OpenAI' : 'Anthropic'} API access`,
          `2. Get an API key from the ${toProvider === 'openai' ? 'OpenAI dashboard' : 'Anthropic console'}`,
          `3. Update environment variables to include ${toProvider === 'openai' ? 'OPENAI_API_KEY' : 'ANTHROPIC_API_KEY'}`,
          `4. Update provider configuration to use ${toProvider === 'openai' ? 'OpenAI' : 'Anthropic'} models`,
          '5. Set up cost monitoring and limits'
        ],
        codeChanges: `
// Before (Local)
const provider = new LocalModelProvider({
  model: 'llama3',
  baseUrl: 'http://localhost:11434'
});

// After (Cloud Provider)
const provider = new ${toProvider === 'openai' ? 'OpenAIProvider' : 'AnthropicProvider'}({
  apiKey: process.env.${toProvider === 'openai' ? 'OPENAI_API_KEY' : 'ANTHROPIC_API_KEY'},
  model: '${toProvider === 'openai' ? 'gpt-4o' : 'claude-3-opus-20240229'}'
});`,
        configChanges: {
          provider: toProvider,
          model: toProvider === 'openai' ? 'gpt-4o' : 'claude-3-opus-20240229',
          apiKey: `process.env.${toProvider === 'openai' ? 'OPENAI_API_KEY' : 'ANTHROPIC_API_KEY'}`
        },
        challenges: [
          'API costs and budget management',
          'Rate limits and quotas',
          'Internet connectivity requirement',
          'Different prompt formatting requirements'
        ],
        recommendations: [
          `Start with ${toProvider === 'openai' ? 'GPT-4o' : 'Claude 3 Opus'} for highest accuracy`,
          `Use ${toProvider === 'openai' ? 'GPT-3.5-turbo' : 'Claude 3 Haiku'} for cost-sensitive applications`,
          'Implement cost monitoring and alerts',
          'Test prompts thoroughly to ensure consistent results'
        ]
      };
    }
    
    // Same provider (configuration changes)
    return {
      steps: [
        `1. Review current ${fromProvider} configuration`,
        '2. Select appropriate model for your use case',
        '3. Update provider configuration',
        '4. Test with new configuration'
      ],
      codeChanges: `
// Update configuration only
const provider = new ${fromProvider === 'openai' ? 'OpenAIProvider' : fromProvider === 'anthropic' ? 'AnthropicProvider' : 'LocalModelProvider'}({
  ${fromProvider !== 'local' ? `apiKey: process.env.${fromProvider === 'openai' ? 'OPENAI_API_KEY' : 'ANTHROPIC_API_KEY'},` : ''}
  model: '${fromProvider === 'openai' ? 'gpt-4o' : fromProvider === 'anthropic' ? 'claude-3-opus-20240229' : 'llama3'}'
});`,
      configChanges: {
        provider: fromProvider,
        model: fromProvider === 'openai' ? 'gpt-4o' : fromProvider === 'anthropic' ? 'claude-3-opus-20240229' : 'llama3'
      },
      challenges: [
        'Ensuring consistent performance with new configuration',
        'Optimizing cost and performance balance'
      ],
      recommendations: [
        'Review provider documentation for latest models and features',
        'Test with different models to find optimal performance/cost balance',
        'Consider fallback mechanisms for high availability'
      ]
    };
  }

  /**
   * Create default configuration for a provider
   * @param provider Provider name
   * @param useCase Use case description
   * @returns Default configuration
   */
  public static createDefaultConfig(provider: string, useCase?: string): LLMProviderConfig {
    // Get recommendations based on use case
    const recommendations = useCase 
      ? this.getProviderRecommendations(useCase).filter(rec => rec.provider === provider)
      : [];
    
    // Use recommended model if available, otherwise use default
    const model = (recommendations && recommendations.length > 0) 
      ? recommendations[0]?.model || 'gpt-4o'
      : provider === 'openai' 
        ? 'gpt-4o' 
        : provider === 'anthropic' 
          ? 'claude-3-sonnet-20240229' 
          : 'llama3';
    
    // Create configuration based on provider
    switch (provider) {
      case 'openai':
        return {
          provider: 'openai',
          model,
          timeout: 60000,
          maxRetries: 3
        };
      case 'anthropic':
        return {
          provider: 'anthropic',
          model,
          timeout: 60000,
          maxRetries: 3
        };
      case 'local':
        return {
          provider: 'local',
          model,
          baseUrl: 'http://localhost:11434',
          timeout: 60000
        };
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Validate provider configuration
   * @param config Provider configuration
   */
  private static validateConfig(config: LLMProviderConfig): void {
    // Check if provider is supported
    if (!config.provider || !this.getSupportedProviders().includes(config.provider)) {
      throw new Error(`Unsupported provider: ${config.provider}`);
    }

    // Check if model is specified
    if (!config.model) {
      throw new Error('Model is required');
    }

    // Check provider-specific requirements
    switch (config.provider) {
      case 'openai':
        if (!config.apiKey && !process.env.OPENAI_API_KEY) {
          throw new Error('OpenAI API key is required');
        }
        break;
      case 'anthropic':
        if (!config.apiKey && !process.env.ANTHROPIC_API_KEY) {
          throw new Error('Anthropic API key is required');
        }
        break;
      case 'local':
        // No specific requirements for local provider
        break;
    }
  }

  /**
   * Create a service configuration with primary and fallback providers
   * @param primaryProvider Primary provider configuration
   * @param fallbackProviders Fallback provider configurations
   * @param options Additional options
   * @returns LLM service configuration
   */
  public static createServiceConfig(
    primaryProvider: LLMProviderConfig,
    fallbackProviders?: LLMProviderConfig[],
    options?: {
      enableFallback?: boolean;
      maxRetries?: number;
      retryDelay?: number;
      minConfidence?: number;
      enableCache?: boolean;
      cacheTTL?: number;
      maxCacheSize?: number;
    }
  ): LLMServiceConfig {
    // Create provider instances from configurations and adapt them to service interface
    const providers = [
      this.adaptToServiceInterface(this.create(primaryProvider)),
      ...(fallbackProviders || []).map(config => this.adaptToServiceInterface(this.create(config)))
    ];

    return {
      providers,
      defaultProviderIndex: 0,
      enableFallbacks: (fallbackProviders && fallbackProviders.length > 0) || false,
      ...options
    };
  }

  /**
   * Adapt abstract provider to service interface
   */
  private static adaptToServiceInterface(provider: AbstractLLMProvider): LLMProvider {
    return {
      async initialize() {
        // Abstract providers don't have initialize method
      },
      async generateResponse(prompt: string, options?: any) {
        // This would need to be implemented based on the abstract provider
        throw new Error('Not implemented - use RAG context');
      },
      getName() {
        return provider.name;
      },
      getAvailableModels() {
        return provider.models;
      },
      getDefaultModel() {
        return provider.models[0] || '';
      }
    };
  }

  /**
   * Create provider based on name
   * @param name Provider name
   * @param config Provider configuration
   * @returns LLM provider
   */
  public static createProvider(name: string, config: LLMProviderConfig): LLMProvider {
    switch (name) {
      case 'openai':
        return this.adaptToServiceInterface(new OpenAIProvider(config as OpenAIProviderConfig));
      case 'anthropic':
        return this.adaptToServiceInterface(new AnthropicProvider(config as AnthropicProviderConfig));
      case 'local':
        return this.adaptToServiceInterface(new LocalModelProvider(config as LocalModelProviderConfig));
      default:
        throw new Error(`Unsupported provider: ${name}`);
    }
  }

  /**
   * Create provider based on config name
   * @param config Configuration with name property
   * @returns LLM provider
   */
  public static createProviderByName(config: { name: string } & LLMProviderConfig): LLMProvider {
    const { name, ...providerConfig } = config;
    switch (name) {
      case 'openai':
        return this.adaptToServiceInterface(new OpenAIProvider({ ...providerConfig, provider: 'openai' } as OpenAIProviderConfig));
      case 'anthropic':
        return this.adaptToServiceInterface(new AnthropicProvider({ ...providerConfig, provider: 'anthropic' } as AnthropicProviderConfig));
      case 'local':
        return this.adaptToServiceInterface(new LocalModelProvider({ ...providerConfig, provider: 'local' } as LocalModelProviderConfig));
      default:
        throw new Error(`Unsupported provider: ${name}`);
    }
  }
}