/**
 * Local Model Provider
 * 
 * Implementation of LLM provider for local models using Ollama
 */

import { LLMProvider, LLMProviderConfig, TokenizerOptions } from '../llm-provider';
import { RAGContext, RAGConfig, LLMResponse } from '../../rag/types';

export interface LocalModelProviderConfig extends LLMProviderConfig {
  /**
   * Model to use
   */
  model: string;

  /**
   * Base URL for Ollama API
   */
  baseUrl?: string;

  /**
   * Request timeout in milliseconds
   */
  timeout?: number;
}

/**
 * Local LLM provider using Ollama
 */
export class LocalModelProvider extends LLMProvider {
  public readonly name = 'local';
  public readonly models = [
    'llama3',
    'llama3:8b',
    'llama3:70b',
    'mistral',
    'mistral:7b',
    'mixtral',
    'phi3',
    'phi3:mini',
    'gemma',
    'gemma:7b'
  ];

  private baseUrl: string;
  private modelInfo: Record<string, { contextWindow: number, maxOutputTokens: number }> = {
    'llama3': { contextWindow: 8192, maxOutputTokens: 2048 },
    'llama3:8b': { contextWindow: 8192, maxOutputTokens: 2048 },
    'llama3:70b': { contextWindow: 8192, maxOutputTokens: 2048 },
    'mistral': { contextWindow: 8192, maxOutputTokens: 2048 },
    'mistral:7b': { contextWindow: 8192, maxOutputTokens: 2048 },
    'mixtral': { contextWindow: 32768, maxOutputTokens: 2048 },
    'phi3': { contextWindow: 4096, maxOutputTokens: 2048 },
    'phi3:mini': { contextWindow: 4096, maxOutputTokens: 2048 },
    'gemma': { contextWindow: 8192, maxOutputTokens: 2048 },
    'gemma:7b': { contextWindow: 8192, maxOutputTokens: 2048 }
  };

  /**
   * Create a new local model provider
   * @param config Provider configuration
   */
  constructor(config: LocalModelProviderConfig) {
    super(config);
    this.baseUrl = config.baseUrl || 'http://localhost:11434';
  }

  /**
   * Check if the provider is available
   * @returns True if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      // Check if Ollama is running
      const response = await fetch(`${this.baseUrl}/api/tags`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate a response using local model
   * @param prompt Prompt to send to the model
   * @param context RAG context
   * @param config RAG configuration
   * @returns LLM response
   */
  public async generateResponse(
    prompt: string,
    context: RAGContext,
    config: RAGConfig
  ): Promise<LLMResponse> {
    const startTime = Date.now();

    try {
      // Prepare system prompt
      const systemPrompt = config.generation?.systemPrompt || 
        'You are a helpful assistant that answers questions based on the provided context.';

      // Count input tokens (approximate)
      const inputTokens = this.countTokens(systemPrompt + prompt);

      // Make request to Ollama
      const response = await this.executeWithRetry(async () => {
        return await fetch(`${this.baseUrl}/api/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: this.config.model,
            messages: [
              {
                role: 'system',
                content: systemPrompt
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            options: {
              temperature: config.generation?.temperature || 0.7,
              num_predict: config.generation?.maxTokens || 1000
            }
          }),
          signal: AbortSignal.timeout(this.config.timeout || 60000)
        });
      }, this.config.maxRetries || 3);

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      const responseTime = Date.now() - startTime;

      // Extract content
      const content = result.message?.content || '';
      
      // Count output tokens (approximate)
      const outputTokens = this.countTokens(content);

      return {
        answer: content,
        sources: [], // Will be populated by the RAG orchestrator
        confidence: 0.7, // Default confidence for local models
        provider: 'local',
        model: this.config.model,
        responseTime
      };
    } catch (error: any) {
      // Transform error
      const ragError = new Error(`Local model error: ${error?.message || 'Unknown error'}`);
      ragError.name = 'LocalModelProviderError';
      ragError.stack = error?.stack;
      
      // Add provider-specific information
      (ragError as any).provider = 'local';
      (ragError as any).retryable = error?.name === 'AbortError' || error?.name === 'TimeoutError';
      (ragError as any).code = error?.name === 'AbortError' ? 'TIMEOUT' : 'LOCAL_MODEL_ERROR';
      
      throw ragError;
    }
  }

  /**
   * Count tokens in a text
   * @param text Text to count tokens in
   * @param options Tokenizer options
   * @returns Token count
   */
  public countTokens(text: string, options?: TokenizerOptions): number {
    // Simple approximation for local models
    // In production, you would use a proper tokenizer for the specific model
    return Math.ceil(text.length / 4);
  }

  /**
   * Estimate cost for a request
   * @param inputTokens Number of input tokens
   * @param outputTokens Number of output tokens
   * @param model Model to use
   * @returns Estimated cost in USD
   */
  public estimateCost(
    inputTokens: number,
    outputTokens: number,
    model?: string
  ): number {
    // Local models have no API cost
    return 0;
  }

  /**
   * Get provider information
   * @returns Provider information
   */
  public getProviderInfo(): {
    name: string;
    models: Array<{
      id: string;
      contextWindow: number;
      trainingCutoff?: string;
      maxOutputTokens: number;
    }>;
    features: string[];
  } {
    return {
      name: this.name,
      models: Object.entries(this.modelInfo).map(([id, info]) => ({
        id,
        ...info
      })),
      features: [
        'chat',
        'offline',
        'local-deployment',
        'customizable'
      ]
    };
  }

  /**
   * Validate provider configuration
   * @param config Provider configuration
   * @returns Validated configuration
   */
  protected validateConfig(config: LLMProviderConfig): LLMProviderConfig {
    // Check if model is supported or follows expected pattern
    if (!config.model || (!this.isValidModel(config.model) && !config.model.includes(':'))) {
      throw new Error(`Unsupported local model: ${config.model}`);
    }

    return config;
  }

  /**
   * Check if a model is valid
   * @param model Model to check
   * @returns True if the model is valid
   */
  private isValidModel(model: string): boolean {
    // Defensive check in case this is called during construction
    if (!this.models) {
      return true; // Allow any model for local provider during construction
    }
    return this.models.includes(model);
  }
  
  /**
   * Execute a function with retry logic
   * @param fn Function to execute
   * @param maxRetries Maximum number of retries
   * @returns Function result
   */
  private async executeWithRetry<T>(fn: () => Promise<T>, maxRetries: number): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error: any) {
        lastError = error;
        
        // Check if error is retryable
        if (error?.retryable === false) {
          throw error;
        }
        
        // Last attempt, throw error
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Wait before retry with exponential backoff
        const delay = 1000 * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // This should never happen, but TypeScript needs it
    throw lastError || new Error('Unknown error during retry');
  }
}