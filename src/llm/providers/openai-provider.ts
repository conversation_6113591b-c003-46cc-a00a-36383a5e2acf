/**
 * OpenAI Provider
 * 
 * Implementation of LLM provider for OpenAI models
 */

import OpenAI from 'openai';
import { encode } from 'gpt-tokenizer';
import { LLMProvider, LLMProviderConfig, TokenizerOptions } from '../llm-provider';
import { RAGContext, RAGConfig, LLMResponse } from '../../rag/types';

export interface OpenAIProviderConfig extends LLMProviderConfig {
  /**
   * OpenAI API key
   */
  apiKey?: string;

  /**
   * OpenAI model to use
   */
  model: string;

  /**
   * OpenAI organization ID
   */
  organizationId?: string;

  /**
   * Base URL for the API
   */
  baseUrl?: string;

  /**
   * Request timeout in milliseconds
   */
  timeout?: number;

  /**
   * Maximum number of retry attempts
   */
  maxRetries?: number;
}

/**
 * OpenAI LLM provider
 */
export class OpenAIProvider extends LLMProvider {
  public readonly name = 'openai';
  public readonly models = [
    'gpt-4o',
    'gpt-4-turbo',
    'gpt-4',
    'gpt-4-32k',
    'gpt-3.5-turbo',
    'gpt-3.5-turbo-16k'
  ];

  private client: OpenAI;
  private modelInfo: Record<string, { contextWindow: number, trainingCutoff: string, maxOutputTokens: number }> = {
    'gpt-4o': { contextWindow: 128000, trainingCutoff: '2023-12', maxOutputTokens: 4096 },
    'gpt-4-turbo': { contextWindow: 128000, trainingCutoff: '2023-12', maxOutputTokens: 4096 },
    'gpt-4': { contextWindow: 8192, trainingCutoff: '2023-04', maxOutputTokens: 4096 },
    'gpt-4-32k': { contextWindow: 32768, trainingCutoff: '2023-04', maxOutputTokens: 4096 },
    'gpt-3.5-turbo': { contextWindow: 16385, trainingCutoff: '2023-04', maxOutputTokens: 4096 },
    'gpt-3.5-turbo-16k': { contextWindow: 16385, trainingCutoff: '2023-04', maxOutputTokens: 4096 }
  };

  private costPerToken: Record<string, { input: number, output: number }> = {
    'gpt-4o': { input: 0.000005, output: 0.000015 },
    'gpt-4-turbo': { input: 0.00001, output: 0.00003 },
    'gpt-4': { input: 0.00003, output: 0.00006 },
    'gpt-4-32k': { input: 0.00006, output: 0.00012 },
    'gpt-3.5-turbo': { input: 0.0000015, output: 0.000002 },
    'gpt-3.5-turbo-16k': { input: 0.000003, output: 0.000004 }
  };

  /**
   * Create a new OpenAI provider
   * @param config Provider configuration
   */
  constructor(config: OpenAIProviderConfig) {
    super(config);

    this.client = new OpenAI({
      apiKey: config.apiKey || process.env.OPENAI_API_KEY,
      organization: config.organizationId || process.env.OPENAI_ORGANIZATION_ID,
      baseURL: config.baseUrl,
      timeout: config.timeout || 60000,
      maxRetries: config.maxRetries || 3
    });
  }

  /**
   * Check if the provider is available
   * @returns True if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      // Simple model list request to check if API is accessible
      await this.client.models.list();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate a response using OpenAI
   * @param prompt Prompt to send to OpenAI
   * @param context RAG context
   * @param config RAG configuration
   * @returns LLM response
   */
  public async generateResponse(
    prompt: string,
    context: RAGContext,
    config: RAGConfig
  ): Promise<LLMResponse> {
    const startTime = Date.now();

    try {
      // Prepare messages
      const messages = [
        {
          role: 'system',
          content: config.generation?.systemPrompt || 
            'You are a helpful assistant that answers questions based on the provided context.'
        },
        {
          role: 'user',
          content: prompt
        }
      ];

      // Count input tokens
      const inputTokens = this.countTokens(messages.map(m => m.content).join(' '));

      // Prepare request parameters
      const params: OpenAI.Chat.ChatCompletionCreateParams = {
        model: this.config.model,
        messages: messages as any,
        temperature: config.generation?.temperature || 0.7,
        max_tokens: config.generation?.maxTokens || 1000,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
      };

      // Make request
      const response = await this.executeWithRetry(async () => {
        return await this.client.chat.completions.create(params);
      }, this.config.maxRetries || 3);
      
      const responseTime = Date.now() - startTime;

      // Extract content
      const content = response.choices[0]?.message?.content || '';
      
      // Count output tokens
      const outputTokens = this.countTokens(content);
      
      // Calculate cost
      const model = this.config.model;
      const rates = this.costPerToken[model] || { input: 0, output: 0 };
      const cost = (inputTokens * rates.input) + (outputTokens * rates.output);

      return {
        answer: content,
        sources: [], // Will be populated by the RAG orchestrator
        confidence: 0.8, // Default confidence, can be improved with actual scoring
        provider: 'openai',
        model: this.config.model,
        responseTime
      };
    } catch (error: any) {
      // Transform OpenAI error
      const ragError = new Error(`OpenAI error: ${error?.message || 'Unknown error'}`);
      ragError.name = 'OpenAIProviderError';
      ragError.stack = error?.stack;
      
      // Add provider-specific information
      (ragError as any).provider = 'openai';
      (ragError as any).retryable = error?.status === 429 || error?.status >= 500;
      (ragError as any).code = error?.code || 'OPENAI_ERROR';
      
      throw ragError;
    }
  }

  /**
   * Count tokens in a text
   * @param text Text to count tokens in
   * @param options Tokenizer options
   * @returns Token count
   */
  public countTokens(text: string, options?: TokenizerOptions): number {
    // Use gpt-tokenizer for OpenAI models
    return encode(text).length;
  }

  /**
   * Estimate cost for a request
   * @param inputTokens Number of input tokens
   * @param outputTokens Number of output tokens
   * @param model Model to use
   * @returns Estimated cost in USD
   */
  public estimateCost(
    inputTokens: number,
    outputTokens: number,
    model?: string
  ): number {
    const modelToUse = model || this.config.model;
    const rates = this.costPerToken[modelToUse] || { input: 0, output: 0 };
    
    return (inputTokens * rates.input) + (outputTokens * rates.output);
  }

  /**
   * Get provider information
   * @returns Provider information
   */
  public getProviderInfo(): {
    name: string;
    models: Array<{
      id: string;
      contextWindow: number;
      trainingCutoff?: string;
      maxOutputTokens: number;
    }>;
    features: string[];
  } {
    return {
      name: this.name,
      models: Object.entries(this.modelInfo).map(([id, info]) => ({
        id,
        ...info
      })),
      features: [
        'chat',
        'function-calling',
        'json-mode',
        'vision',
        'streaming'
      ]
    };
  }

  /**
   * Validate provider configuration
   * @param config Provider configuration
   * @returns Validated configuration
   */
  protected validateConfig(config: LLMProviderConfig): LLMProviderConfig {
    // Check if API key is available
    if (!config.apiKey && !process.env.OPENAI_API_KEY) {
      throw new Error('OpenAI API key is required');
    }

    // Check if model is supported
    if (!config.model || !this.isValidModel(config.model)) {
      throw new Error(`Unsupported OpenAI model: ${config.model}`);
    }

    return config;
  }

  /**
   * Check if a model is valid
   * @param model Model to check
   * @returns True if the model is valid
   */
  private isValidModel(model: string): boolean {
    // Defensive check in case this is called during construction
    if (!this.models) {
      return model.startsWith('gpt-');
    }
    return this.models.includes(model) || model.startsWith('gpt-');
  }
  
  /**
   * Execute a function with retry logic
   * @param fn Function to execute
   * @param maxRetries Maximum number of retries
   * @returns Function result
   */
  private async executeWithRetry<T>(fn: () => Promise<T>, maxRetries: number): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error: any) {
        lastError = error;
        
        // Check if error is retryable
        if (error?.retryable === false) {
          throw error;
        }
        
        // Last attempt, throw error
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Wait before retry with exponential backoff
        const delay = 1000 * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // This should never happen, but TypeScript needs it
    throw lastError || new Error('Unknown error during retry');
  }
}