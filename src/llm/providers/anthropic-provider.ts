/**
 * Anthropic Provider
 * 
 * Implementation of LLM provider for Anthropic Claude models
 */

import Anthropic from '@anthropic-ai/sdk';
import { LLMProvider, LLMProviderConfig, TokenizerOptions } from '../llm-provider';
import { RAGContext, RAGConfig, LLMResponse } from '../../rag/types';

export interface AnthropicProviderConfig extends LLMProviderConfig {
  /**
   * Anthropic API key
   */
  apiKey?: string;

  /**
   * Anthropic model to use
   */
  model: string;

  /**
   * Base URL for the API
   */
  baseUrl?: string;

  /**
   * Request timeout in milliseconds
   */
  timeout?: number;

  /**
   * Maximum number of retry attempts
   */
  maxRetries?: number;
}

/**
 * Anthropic Claude LLM provider
 */
export class AnthropicProvider extends LLMProvider {
  public readonly name = 'anthropic';
  public readonly models = [
    'claude-3-opus-20240229',
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307',
    'claude-2.1',
    'claude-2.0',
    'claude-instant-1.2'
  ];

  private client: Anthropic;
  private modelInfo: Record<string, { contextWindow: number, trainingCutoff: string, maxOutputTokens: number }> = {
    'claude-3-opus-20240229': { contextWindow: 200000, trainingCutoff: '2023-10', maxOutputTokens: 4096 },
    'claude-3-sonnet-20240229': { contextWindow: 200000, trainingCutoff: '2023-10', maxOutputTokens: 4096 },
    'claude-3-haiku-20240307': { contextWindow: 200000, trainingCutoff: '2023-10', maxOutputTokens: 4096 },
    'claude-2.1': { contextWindow: 100000, trainingCutoff: '2023-08', maxOutputTokens: 4096 },
    'claude-2.0': { contextWindow: 100000, trainingCutoff: '2023-07', maxOutputTokens: 4096 },
    'claude-instant-1.2': { contextWindow: 100000, trainingCutoff: '2023-05', maxOutputTokens: 4096 }
  };

  private costPerToken: Record<string, { input: number, output: number }> = {
    'claude-3-opus-20240229': { input: 0.000015, output: 0.000075 },
    'claude-3-sonnet-20240229': { input: 0.000003, output: 0.000015 },
    'claude-3-haiku-20240307': { input: 0.00000025, output: 0.00000125 },
    'claude-2.1': { input: 0.000008, output: 0.000024 },
    'claude-2.0': { input: 0.000008, output: 0.000024 },
    'claude-instant-1.2': { input: 0.0000008, output: 0.0000024 }
  };

  /**
   * Create a new Anthropic provider
   * @param config Provider configuration
   */
  constructor(config: AnthropicProviderConfig) {
    super(config);

    this.client = new Anthropic({
      apiKey: config.apiKey || process.env.ANTHROPIC_API_KEY,
      baseURL: config.baseUrl
    });
  }

  /**
   * Check if the provider is available
   * @returns True if the provider is available
   */
  public async isAvailable(): Promise<boolean> {
    try {
      // Simple model check to see if API is accessible
      // Anthropic doesn't have a simple health check endpoint, so we'll use a minimal completion
      await this.client.messages.create({
        model: this.config.model,
        max_tokens: 1,
        messages: [{ role: 'user', content: 'Hello' }]
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate a response using Anthropic Claude
   * @param prompt Prompt to send to Claude
   * @param context RAG context
   * @param config RAG configuration
   * @returns LLM response
   */
  public async generateResponse(
    prompt: string,
    context: RAGContext,
    config: RAGConfig
  ): Promise<LLMResponse> {
    const startTime = Date.now();

    try {
      // Prepare system prompt
      const systemPrompt = config.generation?.systemPrompt || 
        'You are a helpful assistant that answers questions based on the provided context.';

      // Count input tokens (approximate)
      const inputTokens = this.countTokens(systemPrompt + prompt);

      // Make request
      const response = await this.executeWithRetry(async () => {
        return await this.client.messages.create({
          model: this.config.model,
          max_tokens: config.generation?.maxTokens || 1000,
          temperature: config.generation?.temperature || 0.7,
          system: systemPrompt,
          messages: [
            { role: 'user', content: prompt }
          ]
        });
      }, this.config.maxRetries || 3);

      const responseTime = Date.now() - startTime;

      // Extract content
      const firstContent = response.content[0];
      const content = (firstContent && 'text' in firstContent) ? firstContent.text : '';
      
      // Count output tokens (approximate)
      const outputTokens = this.countTokens(content);
      
      // Calculate cost
      const model = this.config.model;
      const rates = this.costPerToken[model] || { input: 0, output: 0 };
      const cost = (inputTokens * rates.input) + (outputTokens * rates.output);

      return {
        answer: content,
        sources: [], // Will be populated by the RAG orchestrator
        confidence: 0.8, // Default confidence, can be improved with actual scoring
        provider: 'anthropic',
        model: this.config.model,
        responseTime
      };
    } catch (error: any) {
      // Transform Anthropic error
      const ragError = new Error(`Anthropic error: ${error?.message || 'Unknown error'}`);
      ragError.name = 'AnthropicProviderError';
      ragError.stack = error?.stack;
      
      // Add provider-specific information
      (ragError as any).provider = 'anthropic';
      (ragError as any).retryable = error?.status === 429 || error?.status >= 500;
      (ragError as any).code = error?.code || 'ANTHROPIC_ERROR';
      
      throw ragError;
    }
  }

  /**
   * Count tokens in a text
   * @param text Text to count tokens in
   * @param options Tokenizer options
   * @returns Token count
   */
  public countTokens(text: string, options?: TokenizerOptions): number {
    // Anthropic's tokenizer isn't available as a standalone package
    // This is a rough approximation based on GPT tokenizer behavior
    // In production, you would use Anthropic's tokenizer API or a more accurate method
    return Math.ceil(text.length / 4);
  }

  /**
   * Estimate cost for a request
   * @param inputTokens Number of input tokens
   * @param outputTokens Number of output tokens
   * @param model Model to use
   * @returns Estimated cost in USD
   */
  public estimateCost(
    inputTokens: number,
    outputTokens: number,
    model?: string
  ): number {
    const modelToUse = model || this.config.model;
    const rates = this.costPerToken[modelToUse] || { input: 0, output: 0 };
    
    return (inputTokens * rates.input) + (outputTokens * rates.output);
  }

  /**
   * Get provider information
   * @returns Provider information
   */
  public getProviderInfo(): {
    name: string;
    models: Array<{
      id: string;
      contextWindow: number;
      trainingCutoff?: string;
      maxOutputTokens: number;
    }>;
    features: string[];
  } {
    return {
      name: this.name,
      models: Object.entries(this.modelInfo).map(([id, info]) => ({
        id,
        ...info
      })),
      features: [
        'chat',
        'vision',
        'streaming',
        'tool-use'
      ]
    };
  }

  /**
   * Validate provider configuration
   * @param config Provider configuration
   * @returns Validated configuration
   */
  protected validateConfig(config: LLMProviderConfig): LLMProviderConfig {
    // Check if API key is available
    if (!config.apiKey && !process.env.ANTHROPIC_API_KEY) {
      throw new Error('Anthropic API key is required');
    }

    // Check if model is supported
    if (!config.model || !this.isValidModel(config.model)) {
      throw new Error(`Unsupported Anthropic model: ${config.model}`);
    }

    return config;
  }

  /**
   * Check if a model is valid
   * @param model Model to check
   * @returns True if the model is valid
   */
  private isValidModel(model: string): boolean {
    // Defensive check in case this is called during construction
    if (!this.models) {
      return model.startsWith('claude-');
    }
    return this.models.includes(model) || model.startsWith('claude-');
  }
  
  /**
   * Execute a function with retry logic
   * @param fn Function to execute
   * @param maxRetries Maximum number of retries
   * @returns Function result
   */
  private async executeWithRetry<T>(fn: () => Promise<T>, maxRetries: number): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error: any) {
        lastError = error;
        
        // Check if error is retryable
        if (error?.retryable === false) {
          throw error;
        }
        
        // Last attempt, throw error
        if (attempt === maxRetries) {
          throw error;
        }
        
        // Wait before retry with exponential backoff
        const delay = 1000 * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // This should never happen, but TypeScript needs it
    throw lastError || new Error('Unknown error during retry');
  }
}