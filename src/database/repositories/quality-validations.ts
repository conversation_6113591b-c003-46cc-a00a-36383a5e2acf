import { BaseRepository } from './base';
import { logger } from '../../utils/logger';
import type { Tables, TablesInsert } from '../types';

export class QualityValidationsRepository extends BaseRepository<'quality_validations'> {
  constructor() {
    super('quality_validations');
  }

  async findByContentId(contentId: string): Promise<Tables<'quality_validations'>[]> {
    try {
      const { data, error } = await this.table
        .select('*')
        .eq('content_id', contentId)
        .order('validated_at', { ascending: false });

      if (error) {
        logger.error(`Error finding validations by content ID ${contentId}:`, error);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Repository error in findByContentId:`, error);
      throw error;
    }
  }

  async findByValidationType(validationType: string, limit = 100): Promise<Tables<'quality_validations'>[]> {
    try {
      const { data, error } = await this.table
        .select('*')
        .eq('validation_type', validationType)
        .limit(limit)
        .order('validated_at', { ascending: false });

      if (error) {
        logger.error(`Error finding validations by type ${validationType}:`, error);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Repository error in findByValidationType:`, error);
      throw error;
    }
  }

  async findFailedValidations(limit = 50): Promise<Tables<'quality_validations'>[]> {
    try {
      const { data, error } = await this.table
        .select('*')
        .eq('passed', false)
        .limit(limit)
        .order('validated_at', { ascending: false });

      if (error) {
        logger.error('Error finding failed validations:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Repository error in findFailedValidations:`, error);
      throw error;
    }
  }

  async getValidationStats(): Promise<{
    total: number;
    passed: number;
    failed: number;
    byType: Record<string, { total: number; passed: number; failed: number }>;
    averageScore: number;
  }> {
    try {
      const totalCount = await this.count();

      // Get all validations for stats
      const { data: validations, error } = await this.table
        .select('validation_type, passed, score')
        .order('validated_at');

      if (error) {
        logger.error('Error getting validation stats:', error);
        throw error;
      }

      let passed = 0;
      let totalScore = 0;
      const byType: Record<string, { total: number; passed: number; failed: number }> = {};

      validations?.forEach(validation => {
        if (validation.passed) passed++;
        totalScore += validation.score;

        if (!byType[validation.validation_type]) {
          byType[validation.validation_type] = { total: 0, passed: 0, failed: 0 };
        }
        
        if (!byType[validation.validation_type]) {
          byType[validation.validation_type] = { total: 0, passed: 0, failed: 0 };
        }
        const typeStats = byType[validation.validation_type];
        if (typeStats) {
          typeStats.total++;
          if (validation.passed) {
            typeStats.passed++;
          } else {
            typeStats.failed++;
          }
        }
      });

      const failed = totalCount - passed;
      const averageScore = validations && validations.length > 0 ? totalScore / validations.length : 0;

      return {
        total: totalCount,
        passed,
        failed,
        byType,
        averageScore
      };
    } catch (error) {
      logger.error(`Repository error in getValidationStats:`, error);
      throw error;
    }
  }

  async bulkCreate(validations: TablesInsert<'quality_validations'>[]): Promise<Tables<'quality_validations'>[]> {
    try {
      const { data, error } = await this.table
        .insert(validations)
        .select();

      if (error) {
        logger.error('Error bulk creating quality validations:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      logger.error(`Repository error in bulkCreate:`, error);
      throw error;
    }
  }

  async getLatestValidationByContentAndType(
    contentId: string, 
    validationType: string
  ): Promise<Tables<'quality_validations'> | null> {
    try {
      const { data, error } = await this.table
        .select('*')
        .eq('content_id', contentId)
        .eq('validation_type', validationType)
        .order('validated_at', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "not found"
        logger.error(`Error finding latest validation:`, error);
        throw error;
      }

      return data || null;
    } catch (error) {
      logger.error(`Repository error in getLatestValidationByContentAndType:`, error);
      throw error;
    }
  }
}