import { logger } from '../utils/logger';
import { ScrapedContent } from '../types';
import { ContentProcessor, ProcessedContent, ContentProcessorConfig } from './content-processor';
import { MetadataExtractor, ExtractedMetadata, MetadataExtractorConfig } from './metadata-extractor';
import { DocumentChunker, DocumentChunk, ChunkingResult, DocumentChunkerConfig } from './document-chunker';
import { StructurePreserver, PreservedStructure, StructurePreserverConfig } from './structure-preserver';

/**
 * Configuration for the complete processing pipeline
 */
export interface ProcessingPipelineConfig {
  contentProcessor: Partial<ContentProcessorConfig>;
  metadataExtractor: Partial<MetadataExtractorConfig>;
  documentChunker: Partial<DocumentChunkerConfig>;
  structurePreserver: Partial<StructurePreserverConfig>;
  
  // Pipeline settings
  enableParallelProcessing: boolean;
  maxConcurrency: number;
  enableQualityChecks: boolean;
  qualityThreshold: number;
  
  // Error handling
  continueOnError: boolean;
  maxRetries: number;
  retryDelay: number;
}

/**
 * Processing pipeline result
 */
export interface ProcessingResult {
  // Input information
  originalUrl: string;
  inputContentLength: number;
  
  // Processing stages
  processedContent: ProcessedContent;
  extractedMetadata: ExtractedMetadata;
  chunkingResult: ChunkingResult;
  preservedStructure: PreservedStructure;
  
  // Quality metrics
  overallQuality: number;
  stageQualities: {
    contentProcessing: number;
    metadataExtraction: number;
    documentChunking: number;
    structurePreservation: number;
  };
  
  // Performance metrics
  totalProcessingTime: number;
  stageTimings: {
    contentProcessing: number;
    metadataExtraction: number;
    documentChunking: number;
    structurePreservation: number;
  };
  
  // Error information
  errors: ProcessingError[];
  warnings: ProcessingWarning[];
  
  // Final output
  finalChunks: DocumentChunk[];
  
  // Processing metadata
  processedAt: Date;
  pipelineVersion: string;
}

/**
 * Processing error interface
 */
export interface ProcessingError {
  stage: string;
  error: string;
  timestamp: Date;
  recoverable: boolean;
}

/**
 * Processing warning interface
 */
export interface ProcessingWarning {
  stage: string;
  warning: string;
  timestamp: Date;
  impact: 'low' | 'medium' | 'high';
}

/**
 * Batch processing result
 */
export interface BatchProcessingResult {
  results: ProcessingResult[];
  totalProcessed: number;
  successful: number;
  failed: number;
  totalProcessingTime: number;
  averageProcessingTime: number;
  overallQuality: number;
  errors: Array<{ url: string; error: string }>;
}

/**
 * ProcessingPipeline - Orchestrates the complete content processing workflow
 * Coordinates content processing, metadata extraction, chunking, and structure preservation
 */
export class ProcessingPipeline {
  private contentProcessor: ContentProcessor;
  private metadataExtractor: MetadataExtractor;
  private documentChunker: DocumentChunker;
  private structurePreserver: StructurePreserver;
  private config: ProcessingPipelineConfig;

  constructor(config: Partial<ProcessingPipelineConfig> = {}) {
    this.config = {
      contentProcessor: {},
      metadataExtractor: {},
      documentChunker: {},
      structurePreserver: {},
      enableParallelProcessing: true,
      maxConcurrency: 3,
      enableQualityChecks: true,
      qualityThreshold: 0.6,
      continueOnError: true,
      maxRetries: 2,
      retryDelay: 1000,
      ...config
    };

    // Initialize processing components
    this.contentProcessor = new ContentProcessor(this.config.contentProcessor);
    this.metadataExtractor = new MetadataExtractor(this.config.metadataExtractor);
    this.documentChunker = new DocumentChunker(this.config.documentChunker);
    this.structurePreserver = new StructurePreserver(this.config.structurePreserver);

    logger.info('ProcessingPipeline initialized', { config: this.config });
  }

  /**
   * Process a single scraped content through the complete pipeline
   */
  async processContent(scrapedContent: ScrapedContent): Promise<ProcessingResult> {
    const startTime = Date.now();
    const errors: ProcessingError[] = [];
    const warnings: ProcessingWarning[] = [];
    
    logger.info('Starting content processing pipeline', { 
      url: scrapedContent.url,
      contentLength: scrapedContent.content.length 
    });

    try {
      // Stage 1: Content Processing
      const contentProcessingStart = Date.now();
      let processedContent: ProcessedContent;
      
      try {
        processedContent = await this.executeWithRetry(
          () => this.contentProcessor.processContent(scrapedContent),
          'content-processing'
        );
        
        logger.info('Content processing completed', { 
          url: scrapedContent.url,
          wordCount: processedContent.wordCount,
          duration: Date.now() - contentProcessingStart
        });
      } catch (error) {
        const processingError: ProcessingError = {
          stage: 'content-processing',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date(),
          recoverable: false
        };
        errors.push(processingError);
        throw error;
      }

      // Stage 2: Metadata Extraction
      const metadataExtractionStart = Date.now();
      let extractedMetadata: ExtractedMetadata;
      
      try {
        extractedMetadata = await this.executeWithRetry(
          () => this.metadataExtractor.extractMetadata(processedContent),
          'metadata-extraction'
        );
        
        logger.info('Metadata extraction completed', { 
          url: scrapedContent.url,
          hierarchyLevels: extractedMetadata.hierarchy.levels.length,
          sections: extractedMetadata.sections.length,
          duration: Date.now() - metadataExtractionStart
        });
      } catch (error) {
        const processingError: ProcessingError = {
          stage: 'metadata-extraction',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date(),
          recoverable: this.config.continueOnError
        };
        errors.push(processingError);
        
        if (!this.config.continueOnError) {
          throw error;
        }
        
        // Create minimal metadata to continue processing
        extractedMetadata = this.createMinimalMetadata();
        warnings.push({
          stage: 'metadata-extraction',
          warning: 'Using minimal metadata due to extraction failure',
          timestamp: new Date(),
          impact: 'medium'
        });
      }

      // Stage 3: Document Chunking
      const chunkingStart = Date.now();
      let chunkingResult: ChunkingResult;
      
      try {
        chunkingResult = await this.executeWithRetry(
          () => this.documentChunker.chunkDocument(processedContent, extractedMetadata),
          'document-chunking'
        );
        
        logger.info('Document chunking completed', { 
          url: scrapedContent.url,
          totalChunks: chunkingResult.totalChunks,
          averageChunkSize: chunkingResult.averageChunkSize,
          duration: Date.now() - chunkingStart
        });
      } catch (error) {
        const processingError: ProcessingError = {
          stage: 'document-chunking',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date(),
          recoverable: this.config.continueOnError
        };
        errors.push(processingError);
        
        if (!this.config.continueOnError) {
          throw error;
        }
        
        // Create basic chunks to continue processing
        chunkingResult = this.createBasicChunks(processedContent);
        warnings.push({
          stage: 'document-chunking',
          warning: 'Using basic chunking due to chunking failure',
          timestamp: new Date(),
          impact: 'high'
        });
      }

      // Stage 4: Structure Preservation
      const structurePreservationStart = Date.now();
      let preservedStructure: PreservedStructure;
      
      try {
        preservedStructure = await this.executeWithRetry(
          () => this.structurePreserver.preserveStructure(
            chunkingResult.chunks, 
            extractedMetadata, 
            processedContent.cleanedContent
          ),
          'structure-preservation'
        );
        
        logger.info('Structure preservation completed', { 
          url: scrapedContent.url,
          totalRelationships: preservedStructure.totalRelationships,
          preservationQuality: preservedStructure.preservationQuality,
          duration: Date.now() - structurePreservationStart
        });
      } catch (error) {
        const processingError: ProcessingError = {
          stage: 'structure-preservation',
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date(),
          recoverable: true
        };
        errors.push(processingError);
        
        // Use chunks without structure preservation
        preservedStructure = this.createMinimalStructure(chunkingResult.chunks);
        warnings.push({
          stage: 'structure-preservation',
          warning: 'Using chunks without structure preservation',
          timestamp: new Date(),
          impact: 'medium'
        });
      }

      // Calculate quality metrics
      const stageQualities = {
        contentProcessing: processedContent.metadata.contentQuality,
        metadataExtraction: extractedMetadata.confidence,
        documentChunking: chunkingResult.qualityMetrics.averageQuality,
        structurePreservation: preservedStructure.preservationQuality
      };

      const overallQuality = Object.values(stageQualities).reduce((sum, q) => sum + q, 0) / 4;

      // Quality check
      if (this.config.enableQualityChecks && overallQuality < this.config.qualityThreshold) {
        warnings.push({
          stage: 'quality-check',
          warning: `Overall quality ${(overallQuality * 100).toFixed(1)}% below threshold ${(this.config.qualityThreshold * 100).toFixed(1)}%`,
          timestamp: new Date(),
          impact: 'high'
        });
      }

      const totalProcessingTime = Date.now() - startTime;

      const result: ProcessingResult = {
        originalUrl: scrapedContent.url,
        inputContentLength: scrapedContent.content.length,
        processedContent,
        extractedMetadata,
        chunkingResult,
        preservedStructure,
        overallQuality,
        stageQualities,
        totalProcessingTime,
        stageTimings: {
          contentProcessing: processedContent.processingDuration,
          metadataExtraction: extractedMetadata.extractionDuration,
          documentChunking: chunkingResult.chunkingDuration,
          structurePreservation: preservedStructure.processingDuration
        },
        errors,
        warnings,
        finalChunks: preservedStructure.chunks,
        processedAt: new Date(),
        pipelineVersion: '1.0.0'
      };

      logger.info('Content processing pipeline completed', {
        url: scrapedContent.url,
        totalProcessingTime,
        overallQuality: (overallQuality * 100).toFixed(1) + '%',
        finalChunks: result.finalChunks.length,
        errors: errors.length,
        warnings: warnings.length
      });

      return result;

    } catch (error) {
      const totalProcessingTime = Date.now() - startTime;
      
      logger.error('Content processing pipeline failed', {
        url: scrapedContent.url,
        error: error instanceof Error ? error.message : 'Unknown error',
        totalProcessingTime,
        errors: errors.length
      });

      throw new Error(`Processing pipeline failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process multiple scraped contents in batch
   */
  async processBatch(scrapedContents: ScrapedContent[]): Promise<BatchProcessingResult> {
    const startTime = Date.now();
    
    logger.info('Starting batch content processing', { 
      totalItems: scrapedContents.length,
      maxConcurrency: this.config.maxConcurrency,
      parallelProcessing: this.config.enableParallelProcessing
    });

    const results: ProcessingResult[] = [];
    const errors: Array<{ url: string; error: string }> = [];

    if (this.config.enableParallelProcessing) {
      // Process in parallel with concurrency limit
      const batches = this.createBatches(scrapedContents, this.config.maxConcurrency);
      
      for (const batch of batches) {
        const batchPromises = batch.map(async (content) => {
          try {
            const result = await this.processContent(content);
            return { success: true, result, content };
          } catch (error) {
            return { 
              success: false, 
              error: error instanceof Error ? error.message : 'Unknown error', 
              content 
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        
        for (const batchResult of batchResults) {
          if (batchResult.success && batchResult.result) {
            results.push(batchResult.result);
          } else {
            errors.push({
              url: batchResult.content.url,
              error: batchResult.error || 'Unknown error'
            });
          }
        }
      }
    } else {
      // Process sequentially
      for (const content of scrapedContents) {
        try {
          const result = await this.processContent(content);
          results.push(result);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push({ url: content.url, error: errorMessage });
          
          logger.warn('Failed to process content in batch', {
            url: content.url,
            error: errorMessage
          });
        }
      }
    }

    const totalProcessingTime = Date.now() - startTime;
    const averageProcessingTime = results.length > 0 ? 
      results.reduce((sum, r) => sum + r.totalProcessingTime, 0) / results.length : 0;
    const overallQuality = results.length > 0 ? 
      results.reduce((sum, r) => sum + r.overallQuality, 0) / results.length : 0;

    const batchResult: BatchProcessingResult = {
      results,
      totalProcessed: scrapedContents.length,
      successful: results.length,
      failed: errors.length,
      totalProcessingTime,
      averageProcessingTime,
      overallQuality,
      errors
    };

    logger.info('Batch content processing completed', {
      totalProcessed: batchResult.totalProcessed,
      successful: batchResult.successful,
      failed: batchResult.failed,
      overallQuality: (batchResult.overallQuality * 100).toFixed(1) + '%',
      totalProcessingTime,
      averageProcessingTime: Math.round(averageProcessingTime)
    });

    return batchResult;
  }

  // Private helper methods

  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    stageName: string
  ): Promise<T> {
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= this.config.maxRetries + 1; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt <= this.config.maxRetries) {
          logger.warn(`${stageName} attempt ${attempt} failed, retrying`, {
            error: lastError.message,
            nextAttemptIn: this.config.retryDelay
          });
          
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        }
      }
    }
    
    throw lastError;
  }

  private createMinimalMetadata(): ExtractedMetadata {
    return {
      hierarchy: { levels: [], maxDepth: 0, totalNodes: 0 },
      sections: [],
      titles: [],
      documentStructure: {
        documentType: 'unknown',
        hasTableOfContents: false,
        hasIndex: false,
        hasAppendices: false,
        hasFootnotes: false,
        totalSections: 0,
        averageSectionLength: 0,
        structureComplexity: 0
      },
      extractedAt: new Date(),
      extractionDuration: 0,
      confidence: 0.1
    };
  }

  private createBasicChunks(processedContent: ProcessedContent): ChunkingResult {
    // Create basic fixed-size chunks as fallback
    const chunkSize = 500;
    const chunks: DocumentChunk[] = [];
    const content = processedContent.cleanedContent;
    
    let position = 0;
    let chunkIndex = 0;

    while (position < content.length) {
      const endPosition = Math.min(position + chunkSize, content.length);
      const chunkContent = content.substring(position, endPosition);
      
      const chunk: DocumentChunk = {
        id: `basic_chunk_${chunkIndex}`,
        content: chunkContent,
        originalUrl: processedContent.originalUrl,
        chunkIndex,
        totalChunks: 0, // Will be set later
        characterCount: chunkContent.length,
        wordCount: chunkContent.split(/\s+/).length,
        startPosition: position,
        endPosition,
        metadata: {
          contentType: 'text',
          hasHeadings: false,
          hasLists: false,
          hasTables: false,
          hasLinks: false,
          topics: [],
          keyTerms: [],
          entities: [],
          parentSections: []
        },
        relatedChunks: [],
        quality: {
          completeness: 0.5,
          coherence: 0.5,
          informativeness: 0.5,
          contextPreservation: 0.5,
          overallScore: 0.5
        },
        createdAt: new Date(),
        chunkingStrategy: 'basic_fallback'
      };
      
      chunks.push(chunk);
      position = endPosition;
      chunkIndex++;
    }

    // Set total chunks count
    chunks.forEach(chunk => {
      chunk.totalChunks = chunks.length;
    });

    return {
      chunks,
      totalChunks: chunks.length,
      totalCharacters: content.length,
      totalWords: processedContent.wordCount,
      averageChunkSize: chunks.length > 0 ? Math.round(content.length / chunks.length) : 0,
      chunkingStrategy: 'basic_fallback',
      chunkingDuration: 0,
      qualityMetrics: {
        averageQuality: 0.5,
        minQuality: 0.5,
        maxQuality: 0.5,
        qualityDistribution: [0, 0, chunks.length, 0, 0]
      }
    };
  }

  private createMinimalStructure(chunks: DocumentChunk[]): PreservedStructure {
    return {
      chunks,
      relationships: [],
      contexts: new Map(),
      totalRelationships: 0,
      relationshipTypes: {
        hierarchical: 0,
        sequential: 0,
        cross_reference: 0,
        citation: 0,
        semantic: 0,
        structural: 0
      },
      averageRelationshipsPerChunk: 0,
      structureComplexity: 0,
      processingDuration: 0,
      preservationQuality: 0.1
    };
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    
    return batches;
  }
}