import { logger } from '../utils/logger';
import { ProcessedContent } from './content-processor';

/**
 * Configuration for metadata extraction
 */
export interface MetadataExtractorConfig {
  // Hierarchy extraction
  extractHierarchy: boolean;
  maxHierarchyDepth: number;
  
  // Section extraction
  extractSections: boolean;
  minSectionLength: number;
  
  // Title extraction
  extractTitles: boolean;
  titlePatterns: string[];
  
  // Document structure
  extractDocumentStructure: boolean;
  identifyDocumentType: boolean;
  
  // DFSA-specific extraction
  extractDFSAStructure: boolean;
  extractRuleNumbers: boolean;
  extractCitations: boolean;
}

/**
 * Extracted metadata interface
 */
export interface ExtractedMetadata {
  // Document hierarchy
  hierarchy: DocumentHierarchy;
  
  // Section information
  sections: DocumentSection[];
  
  // Title information
  titles: TitleInfo[];
  
  // Document structure
  documentStructure: DocumentStructure;
  
  // DFSA-specific metadata
  dfsaMetadata?: DFSAMetadata;
  
  // Extraction metadata
  extractedAt: Date;
  extractionDuration: number;
  confidence: number;
}

/**
 * Document hierarchy structure
 */
export interface DocumentHierarchy {
  levels: HierarchyLevel[];
  maxDepth: number;
  totalNodes: number;
}

/**
 * Hierarchy level
 */
export interface HierarchyLevel {
  level: number;
  title: string;
  content: string;
  position: number;
  children: HierarchyLevel[];
  parent?: HierarchyLevel;
}

/**
 * Document section
 */
export interface DocumentSection {
  id: string;
  title: string;
  content: string;
  level: number;
  position: number;
  wordCount: number;
  subsections: DocumentSection[];
  metadata: SectionMetadata;
}

/**
 * Section metadata
 */
export interface SectionMetadata {
  hasNumbers: boolean;
  hasLists: boolean;
  hasTables: boolean;
  hasLinks: boolean;
  keyTerms: string[];
  topics: string[];
}

/**
 * Title information
 */
export interface TitleInfo {
  text: string;
  level: number;
  type: 'main' | 'section' | 'subsection' | 'chapter' | 'rule';
  position: number;
  confidence: number;
}

/**
 * Document structure
 */
export interface DocumentStructure {
  documentType: string;
  hasTableOfContents: boolean;
  hasIndex: boolean;
  hasAppendices: boolean;
  hasFootnotes: boolean;
  totalSections: number;
  averageSectionLength: number;
  structureComplexity: number;
}

/**
 * DFSA-specific metadata
 */
export interface DFSAMetadata {
  ruleNumbers: string[];
  citations: Citation[];
  modules: string[];
  chapters: string[];
  sections: string[];
  regulatoryReferences: string[];
  complianceRequirements: string[];
}

/**
 * Citation information
 */
export interface Citation {
  text: string;
  type: 'rule' | 'section' | 'chapter' | 'module' | 'external';
  reference: string;
  position: number;
}

/**
 * MetadataExtractor - Extracts titles, sections, and hierarchy information from processed content
 * Specialized for regulatory documents with DFSA-specific extraction capabilities
 */
export class MetadataExtractor {
  private config: MetadataExtractorConfig;

  constructor(config: Partial<MetadataExtractorConfig> = {}) {
    this.config = {
      // Default configuration
      extractHierarchy: true,
      maxHierarchyDepth: 6,
      extractSections: true,
      minSectionLength: 50,
      extractTitles: true,
      titlePatterns: [
        'chapter',
        'section',
        'rule',
        'module',
        'part',
        'article',
        'subsection'
      ],
      extractDocumentStructure: true,
      identifyDocumentType: true,
      extractDFSAStructure: true,
      extractRuleNumbers: true,
      extractCitations: true,
      ...config
    };

    logger.info('MetadataExtractor initialized', { config: this.config });
  }

  /**
   * Extract metadata from processed content
   */
  async extractMetadata(processedContent: ProcessedContent): Promise<ExtractedMetadata> {
    const startTime = Date.now();
    
    logger.info('Extracting metadata', { 
      url: processedContent.originalUrl,
      contentLength: processedContent.cleanedContent.length 
    });

    try {
      const metadata: ExtractedMetadata = {
        hierarchy: { levels: [], maxDepth: 0, totalNodes: 0 },
        sections: [],
        titles: [],
        documentStructure: {
          documentType: 'unknown',
          hasTableOfContents: false,
          hasIndex: false,
          hasAppendices: false,
          hasFootnotes: false,
          totalSections: 0,
          averageSectionLength: 0,
          structureComplexity: 0
        },
        extractedAt: new Date(),
        extractionDuration: 0,
        confidence: 0
      };

      // Extract hierarchy
      if (this.config.extractHierarchy) {
        metadata.hierarchy = await this.extractHierarchy(processedContent);
      }

      // Extract sections
      if (this.config.extractSections) {
        metadata.sections = await this.extractSections(processedContent);
      }

      // Extract titles
      if (this.config.extractTitles) {
        metadata.titles = await this.extractTitles(processedContent);
      }

      // Extract document structure
      if (this.config.extractDocumentStructure) {
        metadata.documentStructure = await this.extractDocumentStructure(processedContent);
      }

      // Extract DFSA-specific metadata
      if (this.config.extractDFSAStructure) {
        metadata.dfsaMetadata = await this.extractDFSAMetadata(processedContent);
      }

      // Calculate overall confidence
      metadata.confidence = this.calculateExtractionConfidence(metadata);

      const extractionDuration = Date.now() - startTime;
      metadata.extractionDuration = extractionDuration;

      logger.info('Metadata extraction completed', {
        url: processedContent.originalUrl,
        extractionDuration,
        confidence: metadata.confidence,
        hierarchyLevels: metadata.hierarchy.levels.length,
        sections: metadata.sections.length,
        titles: metadata.titles.length,
        dfsaRules: metadata.dfsaMetadata?.ruleNumbers.length || 0
      });

      return metadata;

    } catch (error) {
      const extractionDuration = Date.now() - startTime;
      
      logger.error('Metadata extraction failed', {
        url: processedContent.originalUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
        extractionDuration
      });

      throw new Error(`Metadata extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Extract metadata from multiple processed content items
   */
  async extractMultipleMetadata(processedContents: ProcessedContent[]): Promise<ExtractedMetadata[]> {
    logger.info('Extracting metadata from multiple content items', { count: processedContents.length });

    const results: ExtractedMetadata[] = [];
    const errors: Array<{ url: string; error: string }> = [];

    for (const content of processedContents) {
      try {
        const metadata = await this.extractMetadata(content);
        results.push(metadata);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push({ url: content.originalUrl, error: errorMessage });
        
        logger.warn('Failed to extract metadata from content item', {
          url: content.originalUrl,
          error: errorMessage
        });
      }
    }

    logger.info('Multiple metadata extraction completed', {
      total: processedContents.length,
      successful: results.length,
      failed: errors.length
    });

    if (errors.length > 0) {
      logger.warn('Some content items failed metadata extraction', { errors });
    }

    return results;
  }

  // Private helper methods

  private async extractHierarchy(content: ProcessedContent): Promise<DocumentHierarchy> {
    const hierarchy: DocumentHierarchy = {
      levels: [],
      maxDepth: 0,
      totalNodes: 0
    };

    // Use existing headings from structure
    const headings = content.structure.headings;
    
    if (headings.length === 0) {
      return hierarchy;
    }

    // Build hierarchy from headings
    const levelMap = new Map<number, HierarchyLevel[]>();
    
    for (const heading of headings) {
      const level: HierarchyLevel = {
        level: heading.level,
        title: heading.text,
        content: this.extractContentForHeading(content.cleanedContent, heading),
        position: heading.position,
        children: []
      };

      if (!levelMap.has(heading.level)) {
        levelMap.set(heading.level, []);
      }
      levelMap.get(heading.level)!.push(level);
    }

    // Build parent-child relationships
    const allLevels = Array.from(levelMap.entries()).sort(([a], [b]) => a - b);
    hierarchy.levels = allLevels[0]?.[1] || [];
    hierarchy.maxDepth = Math.max(...Array.from(levelMap.keys()));
    hierarchy.totalNodes = headings.length;

    // Set parent-child relationships
    for (let i = 0; i < allLevels.length - 1; i++) {
      const currentLevelEntry = allLevels[i];
      const nextLevelEntry = allLevels[i + 1];
      if (currentLevelEntry && nextLevelEntry) {
        const currentNodes = currentLevelEntry[1];
        const nextNodes = nextLevelEntry[1];

        for (const currentNode of currentNodes) {
          for (const nextNode of nextNodes) {
            if (nextNode.position > currentNode.position) {
              currentNode.children.push(nextNode);
              nextNode.parent = currentNode;
            }
          }
        }
      }
    }

    return hierarchy;
  }

  private extractContentForHeading(content: string, heading: any): string {
    // Extract content that belongs to this heading
    // This is a simplified implementation
    const lines = content.split('\n');
    const headingLine = lines.find(line => line.includes(heading.text));
    
    if (!headingLine) {
      return '';
    }

    const headingIndex = lines.indexOf(headingLine);
    const nextHeadingIndex = lines.findIndex((line, index) => 
      index > headingIndex && line.match(/^#{1,6}\s/) // Next heading
    );

    const endIndex = nextHeadingIndex === -1 ? lines.length : nextHeadingIndex;
    return lines.slice(headingIndex + 1, endIndex).join('\n').trim();
  }

  private async extractSections(content: ProcessedContent): Promise<DocumentSection[]> {
    const sections: DocumentSection[] = [];
    
    // Use existing sections from structure or create from headings
    if (content.structure.sections.length > 0) {
      for (const section of content.structure.sections) {
        if (section.content.length >= this.config.minSectionLength) {
          const documentSection: DocumentSection = {
            id: this.generateSectionId(section.title),
            title: section.title,
            content: section.content,
            level: section.level,
            position: section.position,
            wordCount: this.countWords(section.content),
            subsections: [],
            metadata: await this.extractSectionMetadata(section.content)
          };
          
          sections.push(documentSection);
        }
      }
    } else {
      // Create sections from headings if no sections exist
      for (const heading of content.structure.headings) {
        const sectionContent = this.extractContentForHeading(content.cleanedContent, heading);
        
        if (sectionContent.length >= this.config.minSectionLength) {
          const documentSection: DocumentSection = {
            id: this.generateSectionId(heading.text),
            title: heading.text,
            content: sectionContent,
            level: heading.level,
            position: heading.position,
            wordCount: this.countWords(sectionContent),
            subsections: [],
            metadata: await this.extractSectionMetadata(sectionContent)
          };
          
          sections.push(documentSection);
        }
      }
    }

    return sections;
  }

  private async extractSectionMetadata(content: string): Promise<SectionMetadata> {
    return {
      hasNumbers: /\d+/.test(content),
      hasLists: /^\s*[-*•]\s/m.test(content) || /^\s*\d+\.\s/m.test(content),
      hasTables: content.includes('|') || content.includes('\t'),
      hasLinks: /https?:\/\//.test(content),
      keyTerms: this.extractKeyTerms(content),
      topics: this.extractTopics(content)
    };
  }

  private extractKeyTerms(content: string): string[] {
    // Extract important terms (simplified implementation)
    const words = content.toLowerCase().split(/\s+/);
    const termFreq: { [key: string]: number } = {};

    for (const word of words) {
      if (word.length > 4 && !this.isStopWord(word)) {
        termFreq[word] = (termFreq[word] || 0) + 1;
      }
    }

    return Object.entries(termFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  }

  private extractTopics(content: string): string[] {
    const topics: string[] = [];
    const topicPatterns = {
      'compliance': /compliance|regulatory|requirement/gi,
      'financial': /financial|money|payment|fund/gi,
      'legal': /legal|law|regulation|rule/gi,
      'risk': /risk|assessment|management|control/gi,
      'governance': /governance|oversight|supervision/gi
    };

    for (const [topic, pattern] of Object.entries(topicPatterns)) {
      if (pattern.test(content)) {
        topics.push(topic);
      }
    }

    return topics;
  }

  private isStopWord(word: string): boolean {
    const stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'between', 'among', 'within'];
    return stopWords.includes(word);
  }

  private async extractTitles(content: ProcessedContent): Promise<TitleInfo[]> {
    const titles: TitleInfo[] = [];
    
    // Extract from existing headings
    for (const heading of content.structure.headings) {
      const titleType = this.determineTitleType(heading.text);
      const confidence = this.calculateTitleConfidence(heading.text, titleType);
      
      titles.push({
        text: heading.text,
        level: heading.level,
        type: titleType,
        position: heading.position,
        confidence
      });
    }

    // Extract additional titles from content patterns
    const additionalTitles = this.extractTitlesFromPatterns(content.cleanedContent);
    titles.push(...additionalTitles);

    return titles.sort((a, b) => a.position - b.position);
  }

  private determineTitleType(text: string): 'main' | 'section' | 'subsection' | 'chapter' | 'rule' {
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('chapter')) return 'chapter';
    if (lowerText.includes('rule')) return 'rule';
    if (lowerText.includes('section')) return 'section';
    if (lowerText.includes('subsection')) return 'subsection';
    
    return 'main';
  }

  private calculateTitleConfidence(text: string, type: string): number {
    let confidence = 0.5;
    
    // Length factor
    if (text.length > 10 && text.length < 100) confidence += 0.2;
    
    // Pattern matching
    if (this.config.titlePatterns.some(pattern => text.toLowerCase().includes(pattern))) {
      confidence += 0.3;
    }
    
    return Math.min(confidence, 1.0);
  }

  private extractTitlesFromPatterns(content: string): TitleInfo[] {
    const titles: TitleInfo[] = [];
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const currentLine = lines[i];
      if (!currentLine) {
        continue;
      }
      const line = currentLine.trim();
      
      // Check for title patterns
      for (const pattern of this.config.titlePatterns) {
        if (line.toLowerCase().includes(pattern) && line.length < 200) {
          titles.push({
            text: line,
            level: 1,
            type: this.determineTitleType(line),
            position: i,
            confidence: this.calculateTitleConfidence(line, pattern)
          });
        }
      }
    }
    
    return titles;
  }

  private async extractDocumentStructure(content: ProcessedContent): Promise<DocumentStructure> {
    const structure: DocumentStructure = {
      documentType: this.identifyDocumentType(content),
      hasTableOfContents: this.hasTableOfContents(content.cleanedContent),
      hasIndex: this.hasIndex(content.cleanedContent),
      hasAppendices: this.hasAppendices(content.cleanedContent),
      hasFootnotes: this.hasFootnotes(content.cleanedContent),
      totalSections: content.structure.sections.length,
      averageSectionLength: this.calculateAverageSectionLength(content.structure.sections),
      structureComplexity: this.calculateStructureComplexity(content)
    };

    return structure;
  }

  private identifyDocumentType(content: ProcessedContent): string {
    const text = content.cleanedContent.toLowerCase();
    
    if (text.includes('rulebook') || text.includes('regulation')) return 'regulatory';
    if (text.includes('manual') || text.includes('handbook')) return 'manual';
    if (text.includes('policy') || text.includes('procedure')) return 'policy';
    if (text.includes('guide') || text.includes('guideline')) return 'guide';
    
    return 'document';
  }

  private hasTableOfContents(content: string): boolean {
    const lowerContent = content.toLowerCase();
    return lowerContent.includes('table of contents') || 
           lowerContent.includes('contents') && lowerContent.includes('page');
  }

  private hasIndex(content: string): boolean {
    const lowerContent = content.toLowerCase();
    return lowerContent.includes('index') && lowerContent.includes('page');
  }

  private hasAppendices(content: string): boolean {
    return /appendix\s+[a-z]/gi.test(content);
  }

  private hasFootnotes(content: string): boolean {
    return /\[\d+\]/.test(content) || /^\d+\.\s/m.test(content);
  }

  private calculateAverageSectionLength(sections: any[]): number {
    if (sections.length === 0) return 0;
    
    const totalLength = sections.reduce((sum, section) => sum + section.content.length, 0);
    return Math.round(totalLength / sections.length);
  }

  private calculateStructureComplexity(content: ProcessedContent): number {
    let complexity = 0;
    
    // Heading levels contribute to complexity
    const maxHeadingLevel = Math.max(...content.structure.headings.map(h => h.level), 0);
    complexity += maxHeadingLevel * 0.1;
    
    // Number of sections
    complexity += content.structure.sections.length * 0.05;
    
    // Lists and tables add complexity
    complexity += content.structure.lists.length * 0.02;
    complexity += content.structure.tables.length * 0.03;
    
    return Math.min(complexity, 1.0);
  }

  private async extractDFSAMetadata(content: ProcessedContent): Promise<DFSAMetadata> {
    const dfsaMetadata: DFSAMetadata = {
      ruleNumbers: this.extractRuleNumbers(content.cleanedContent),
      citations: this.extractCitations(content.cleanedContent),
      modules: this.extractModules(content.cleanedContent),
      chapters: this.extractChapters(content.cleanedContent),
      sections: this.extractDFSASections(content.cleanedContent),
      regulatoryReferences: this.extractRegulatoryReferences(content.cleanedContent),
      complianceRequirements: this.extractComplianceRequirements(content.cleanedContent)
    };

    return dfsaMetadata;
  }

  private extractRuleNumbers(content: string): string[] {
    const ruleNumbers: string[] = [];
    
    // Pattern for DFSA rule numbers (e.g., Rule 1.2.3, Rule A.1.1)
    const rulePattern = /Rule\s+([A-Z]?\d+(?:\.\d+)*)/gi;
    let match;
    
    while ((match = rulePattern.exec(content)) !== null) {
      if (match[1]) {
        ruleNumbers.push(match[1]);
      }
    }
    
    return [...new Set(ruleNumbers)]; // Remove duplicates
  }

  private extractCitations(content: string): Citation[] {
    const citations: Citation[] = [];
    let position = 0;
    
    // Extract rule citations
    const rulePattern = /Rule\s+([A-Z]?\d+(?:\.\d+)*)/gi;
    let match;
    
    while ((match = rulePattern.exec(content)) !== null) {
      if (match[0] && match[1]) {
        citations.push({
          text: match[0],
          type: 'rule',
          reference: match[1],
          position: position++
        });
      }
    }
    
    // Extract section citations
    const sectionPattern = /Section\s+(\d+(?:\.\d+)*)/gi;
    while ((match = sectionPattern.exec(content)) !== null) {
      if (match[0] && match[1]) {
        citations.push({
          text: match[0],
          type: 'section',
          reference: match[1],
          position: position++
        });
      }
    }
    
    return citations;
  }

  private extractModules(content: string): string[] {
    const modules: string[] = [];
    const modulePattern = /Module\s+([A-Z]+)/gi;
    let match;
    
    while ((match = modulePattern.exec(content)) !== null) {
      if (match[1]) {
        modules.push(match[1]);
      }
    }
    
    return [...new Set(modules)];
  }

  private extractChapters(content: string): string[] {
    const chapters: string[] = [];
    const chapterPattern = /Chapter\s+(\d+|[A-Z]+)/gi;
    let match;
    
    while ((match = chapterPattern.exec(content)) !== null) {
      if (match[1]) {
        chapters.push(match[1]);
      }
    }
    
    return [...new Set(chapters)];
  }

  private extractDFSASections(content: string): string[] {
    const sections: string[] = [];
    const sectionPattern = /Section\s+(\d+(?:\.\d+)*)/gi;
    let match;
    
    while ((match = sectionPattern.exec(content)) !== null) {
      if (match[1]) {
        sections.push(match[1]);
      }
    }
    
    return [...new Set(sections)];
  }

  private extractRegulatoryReferences(content: string): string[] {
    const references: string[] = [];
    const patterns = [
      /DFSA\s+[A-Z]+\s*\d+/gi,
      /Financial Services Authority/gi,
      /Dubai International Financial Centre/gi,
      /DIFC/gi
    ];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        references.push(match[0]);
      }
    }
    
    return [...new Set(references)];
  }

  private extractComplianceRequirements(content: string): string[] {
    const requirements: string[] = [];
    const requirementPatterns = [
      /must\s+[^.]+/gi,
      /shall\s+[^.]+/gi,
      /required\s+to\s+[^.]+/gi,
      /obligation\s+to\s+[^.]+/gi
    ];
    
    for (const pattern of requirementPatterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const requirement = match[0].trim();
        if (requirement.length > 10 && requirement.length < 200) {
          requirements.push(requirement);
        }
      }
    }
    
    return requirements.slice(0, 20); // Limit to 20 requirements
  }

  private calculateExtractionConfidence(metadata: ExtractedMetadata): number {
    let confidence = 0.5;
    
    // Hierarchy extraction confidence
    if (metadata.hierarchy.levels.length > 0) confidence += 0.1;
    if (metadata.hierarchy.maxDepth > 2) confidence += 0.1;
    
    // Section extraction confidence
    if (metadata.sections.length > 0) confidence += 0.1;
    if (metadata.sections.length > 5) confidence += 0.1;
    
    // Title extraction confidence
    if (metadata.titles.length > 0) confidence += 0.1;
    
    // DFSA-specific confidence
    if (metadata.dfsaMetadata) {
      if (metadata.dfsaMetadata.ruleNumbers.length > 0) confidence += 0.1;
      if (metadata.dfsaMetadata.citations.length > 0) confidence += 0.1;
    }
    
    return Math.min(confidence, 1.0);
  }

  private generateSectionId(title: string): string {
    return title.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
  }

  private countWords(content: string): number {
    return content.split(/\s+/).filter(word => word.length > 0).length;
  }
}