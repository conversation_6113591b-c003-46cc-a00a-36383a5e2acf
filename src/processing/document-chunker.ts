import { logger } from '../utils/logger';
import { ProcessedContent } from './content-processor';
import { ExtractedMetadata } from './metadata-extractor';

/**
 * Configuration for document chunking
 */
export interface DocumentChunkerConfig {
  // Chunk size settings
  maxChunkSize: number;
  minChunkSize: number;
  targetChunkSize: number;
  
  // Overlap settings
  chunkOverlap: number;
  preserveContext: boolean;
  
  // Chunking strategy
  strategy: 'fixed' | 'semantic' | 'hierarchical' | 'hybrid';
  
  // Semantic chunking settings
  semanticSimilarityThreshold: number;
  useSemanticBoundaries: boolean;
  
  // Hierarchical chunking settings
  respectSectionBoundaries: boolean;
  respectParagraphBoundaries: boolean;
  respectSentenceBoundaries: boolean;
  
  // Content preservation
  preserveHeadings: boolean;
  preserveListStructure: boolean;
  preserveTableStructure: boolean;
  
  // Quality settings
  minWordsPerChunk: number;
  maxWordsPerChunk: number;
  balanceChunkSizes: boolean;
}

/**
 * Document chunk interface
 */
export interface DocumentChunk {
  id: string;
  content: string;
  originalUrl: string;
  chunkIndex: number;
  totalChunks: number;
  
  // Size information
  characterCount: number;
  wordCount: number;
  
  // Position information
  startPosition: number;
  endPosition: number;
  
  // Context information
  precedingContext?: string;
  followingContext?: string;
  
  // Metadata
  metadata: ChunkMetadata;
  
  // Relationships
  parentSection?: string;
  relatedChunks: string[];
  
  // Quality metrics
  quality: ChunkQuality;
  
  // Creation info
  createdAt: Date;
  chunkingStrategy: string;
}

/**
 * Chunk metadata
 */
export interface ChunkMetadata {
  // Content type
  contentType: 'text' | 'heading' | 'list' | 'table' | 'mixed';
  
  // Structural information
  hasHeadings: boolean;
  hasLists: boolean;
  hasTables: boolean;
  hasLinks: boolean;
  
  // Semantic information
  topics: string[];
  keyTerms: string[];
  entities: string[];
  
  // DFSA-specific metadata
  dfsaElements?: {
    ruleNumbers: string[];
    citations: string[];
    complianceRequirements: string[];
  };
  
  // Hierarchy information
  hierarchyLevel?: number;
  sectionTitle?: string;
  parentSections: string[];
}

/**
 * Chunk quality metrics
 */
export interface ChunkQuality {
  completeness: number;      // How complete the chunk is (0-1)
  coherence: number;         // How coherent the content is (0-1)
  informativeness: number;   // How informative the chunk is (0-1)
  contextPreservation: number; // How well context is preserved (0-1)
  overallScore: number;      // Overall quality score (0-1)
}

/**
 * Chunking result
 */
export interface ChunkingResult {
  chunks: DocumentChunk[];
  totalChunks: number;
  totalCharacters: number;
  totalWords: number;
  averageChunkSize: number;
  chunkingStrategy: string;
  chunkingDuration: number;
  qualityMetrics: {
    averageQuality: number;
    minQuality: number;
    maxQuality: number;
    qualityDistribution: number[];
  };
}

/**
 * Semantic boundary detection result
 */
interface SemanticBoundary {
  position: number;
  confidence: number;
  type: 'paragraph' | 'section' | 'topic' | 'sentence';
}

/**
 * DocumentChunker - Splits content into optimal chunks for RAG processing
 * Supports multiple chunking strategies with context preservation and quality optimization
 */
export class DocumentChunker {
  private config: DocumentChunkerConfig;

  constructor(config: Partial<DocumentChunkerConfig> = {}) {
    this.config = {
      // Default configuration optimized for RAG
      maxChunkSize: 1000,
      minChunkSize: 100,
      targetChunkSize: 500,
      chunkOverlap: 50,
      preserveContext: true,
      strategy: 'hybrid',
      semanticSimilarityThreshold: 0.7,
      useSemanticBoundaries: true,
      respectSectionBoundaries: true,
      respectParagraphBoundaries: true,
      respectSentenceBoundaries: true,
      preserveHeadings: true,
      preserveListStructure: true,
      preserveTableStructure: true,
      minWordsPerChunk: 20,
      maxWordsPerChunk: 200,
      balanceChunkSizes: true,
      ...config
    };

    logger.info('DocumentChunker initialized', { config: this.config });
  }

  /**
   * Chunk processed content into optimal chunks for RAG
   */
  async chunkDocument(
    processedContent: ProcessedContent, 
    metadata?: ExtractedMetadata
  ): Promise<ChunkingResult> {
    const startTime = Date.now();
    
    logger.info('Chunking document', { 
      url: processedContent.originalUrl,
      contentLength: processedContent.cleanedContent.length,
      strategy: this.config.strategy
    });

    try {
      let chunks: DocumentChunk[] = [];

      // Apply chunking strategy
      switch (this.config.strategy) {
        case 'fixed':
          chunks = await this.fixedSizeChunking(processedContent, metadata);
          break;
        case 'semantic':
          chunks = await this.semanticChunking(processedContent, metadata);
          break;
        case 'hierarchical':
          chunks = await this.hierarchicalChunking(processedContent, metadata);
          break;
        case 'hybrid':
          chunks = await this.hybridChunking(processedContent, metadata);
          break;
        default:
          throw new Error(`Unknown chunking strategy: ${this.config.strategy}`);
      }

      // Post-process chunks
      chunks = await this.postProcessChunks(chunks, processedContent);

      // Calculate quality metrics
      const qualityMetrics = this.calculateQualityMetrics(chunks);

      const chunkingDuration = Date.now() - startTime;

      const result: ChunkingResult = {
        chunks,
        totalChunks: chunks.length,
        totalCharacters: chunks.reduce((sum, chunk) => sum + chunk.characterCount, 0),
        totalWords: chunks.reduce((sum, chunk) => sum + chunk.wordCount, 0),
        averageChunkSize: chunks.length > 0 ? Math.round(chunks.reduce((sum, chunk) => sum + chunk.characterCount, 0) / chunks.length) : 0,
        chunkingStrategy: this.config.strategy,
        chunkingDuration,
        qualityMetrics
      };

      logger.info('Document chunking completed', {
        url: processedContent.originalUrl,
        totalChunks: result.totalChunks,
        averageChunkSize: result.averageChunkSize,
        averageQuality: result.qualityMetrics.averageQuality,
        chunkingDuration
      });

      return result;

    } catch (error) {
      const chunkingDuration = Date.now() - startTime;
      
      logger.error('Document chunking failed', {
        url: processedContent.originalUrl,
        error: error instanceof Error ? error.message : 'Unknown error',
        chunkingDuration
      });

      throw new Error(`Document chunking failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Chunk multiple documents
   */
  async chunkMultipleDocuments(
    processedContents: ProcessedContent[],
    metadataList?: ExtractedMetadata[]
  ): Promise<ChunkingResult[]> {
    logger.info('Chunking multiple documents', { count: processedContents.length });

    const results: ChunkingResult[] = [];
    const errors: Array<{ url: string; error: string }> = [];

    for (let i = 0; i < processedContents.length; i++) {
      const content = processedContents[i];
      const metadata = metadataList?.[i];

      if (!content) {
        logger.warn('Skipping undefined content at index', { index: i });
        continue;
      }

      try {
        const result = await this.chunkDocument(content, metadata);
        results.push(result);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push({ url: content.originalUrl, error: errorMessage });

        logger.warn('Failed to chunk document', {
          url: content.originalUrl,
          error: errorMessage
        });
      }
    }

    logger.info('Multiple document chunking completed', {
      total: processedContents.length,
      successful: results.length,
      failed: errors.length
    });

    if (errors.length > 0) {
      logger.warn('Some documents failed chunking', { errors });
    }

    return results;
  }

  // Private chunking strategy implementations

  private async fixedSizeChunking(
    content: ProcessedContent, 
    metadata?: ExtractedMetadata
  ): Promise<DocumentChunk[]> {
    const chunks: DocumentChunk[] = [];
    const text = content.cleanedContent;
    const chunkSize = this.config.targetChunkSize;
    const overlap = this.config.chunkOverlap;

    let position = 0;
    let chunkIndex = 0;

    while (position < text.length) {
      const endPosition = Math.min(position + chunkSize, text.length);
      let chunkContent = text.substring(position, endPosition);

      // Respect sentence boundaries if enabled
      if (this.config.respectSentenceBoundaries && endPosition < text.length) {
        const lastSentenceEnd = chunkContent.lastIndexOf('.');
        if (lastSentenceEnd > chunkSize * 0.7) { // Only if we don't lose too much content
          chunkContent = chunkContent.substring(0, lastSentenceEnd + 1);
        }
      }

      const chunk = await this.createChunk(
        chunkContent,
        content,
        chunkIndex,
        position,
        position + chunkContent.length,
        metadata
      );

      chunks.push(chunk);

      position += chunkContent.length - overlap;
      chunkIndex++;
    }

    return chunks;
  }

  private async semanticChunking(
    content: ProcessedContent, 
    metadata?: ExtractedMetadata
  ): Promise<DocumentChunk[]> {
    const chunks: DocumentChunk[] = [];
    const text = content.cleanedContent;

    // Detect semantic boundaries
    const boundaries = await this.detectSemanticBoundaries(text);
    
    let chunkIndex = 0;
    let currentPosition = 0;

    for (const boundary of boundaries) {
      if (boundary.position > currentPosition) {
        const chunkContent = text.substring(currentPosition, boundary.position);
        
        if (chunkContent.length >= this.config.minChunkSize) {
          const chunk = await this.createChunk(
            chunkContent,
            content,
            chunkIndex,
            currentPosition,
            boundary.position,
            metadata
          );

          chunks.push(chunk);
          chunkIndex++;
        }

        currentPosition = boundary.position;
      }
    }

    // Handle remaining content
    if (currentPosition < text.length) {
      const chunkContent = text.substring(currentPosition);
      if (chunkContent.length >= this.config.minChunkSize) {
        const chunk = await this.createChunk(
          chunkContent,
          content,
          chunkIndex,
          currentPosition,
          text.length,
          metadata
        );

        chunks.push(chunk);
      }
    }

    return chunks;
  }

  private async hierarchicalChunking(
    content: ProcessedContent, 
    metadata?: ExtractedMetadata
  ): Promise<DocumentChunk[]> {
    const chunks: DocumentChunk[] = [];

    // Use sections from metadata if available
    if (metadata && metadata.sections.length > 0) {
      let chunkIndex = 0;

      for (const section of metadata.sections) {
        // Split large sections into smaller chunks
        if (section.content.length > this.config.maxChunkSize) {
          const sectionChunks = await this.splitLargeSection(section.content, content, chunkIndex, metadata);
          chunks.push(...sectionChunks);
          chunkIndex += sectionChunks.length;
        } else if (section.content.length >= this.config.minChunkSize) {
          const chunk = await this.createChunk(
            section.content,
            content,
            chunkIndex,
            0, // Position would need to be calculated from original content
            section.content.length,
            metadata,
            section.title
          );

          chunks.push(chunk);
          chunkIndex++;
        }
      }
    } else {
      // Fall back to paragraph-based chunking
      return await this.paragraphBasedChunking(content, metadata);
    }

    return chunks;
  }

  private async hybridChunking(
    content: ProcessedContent, 
    metadata?: ExtractedMetadata
  ): Promise<DocumentChunk[]> {
    // Combine hierarchical and semantic approaches
    let chunks: DocumentChunk[] = [];

    // Start with hierarchical chunking
    if (metadata && metadata.sections.length > 0) {
      chunks = await this.hierarchicalChunking(content, metadata);
    } else {
      // Fall back to semantic chunking
      chunks = await this.semanticChunking(content, metadata);
    }

    // Refine with semantic boundaries
    const refinedChunks: DocumentChunk[] = [];
    
    for (const chunk of chunks) {
      if (chunk.characterCount > this.config.maxChunkSize) {
        // Split large chunks using semantic boundaries
        const subChunks = await this.splitChunkSemantically(chunk, content, metadata);
        refinedChunks.push(...subChunks);
      } else {
        refinedChunks.push(chunk);
      }
    }

    return refinedChunks;
  }

  private async paragraphBasedChunking(
    content: ProcessedContent, 
    metadata?: ExtractedMetadata
  ): Promise<DocumentChunk[]> {
    const chunks: DocumentChunk[] = [];
    const paragraphs = content.cleanedContent.split(/\n\s*\n/);
    
    let currentChunk = '';
    let chunkIndex = 0;
    let startPosition = 0;

    for (const paragraph of paragraphs) {
      const trimmedParagraph = paragraph.trim();
      
      if (trimmedParagraph.length === 0) continue;

      // Check if adding this paragraph would exceed max chunk size
      if (currentChunk.length + trimmedParagraph.length > this.config.maxChunkSize && currentChunk.length > 0) {
        // Create chunk from current content
        const chunk = await this.createChunk(
          currentChunk,
          content,
          chunkIndex,
          startPosition,
          startPosition + currentChunk.length,
          metadata
        );

        chunks.push(chunk);
        chunkIndex++;

        // Start new chunk
        currentChunk = trimmedParagraph;
        startPosition += currentChunk.length;
      } else {
        // Add paragraph to current chunk
        if (currentChunk.length > 0) {
          currentChunk += '\n\n' + trimmedParagraph;
        } else {
          currentChunk = trimmedParagraph;
        }
      }
    }

    // Handle remaining content
    if (currentChunk.length >= this.config.minChunkSize) {
      const chunk = await this.createChunk(
        currentChunk,
        content,
        chunkIndex,
        startPosition,
        startPosition + currentChunk.length,
        metadata
      );

      chunks.push(chunk);
    }

    return chunks;
  }

  // Helper methods

  private async createChunk(
    chunkContent: string,
    originalContent: ProcessedContent,
    chunkIndex: number,
    startPosition: number,
    endPosition: number,
    metadata?: ExtractedMetadata,
    sectionTitle?: string
  ): Promise<DocumentChunk> {
    const chunkId = this.generateChunkId(originalContent.originalUrl, chunkIndex);
    
    const chunk: DocumentChunk = {
      id: chunkId,
      content: chunkContent.trim(),
      originalUrl: originalContent.originalUrl,
      chunkIndex,
      totalChunks: 0, // Will be set later
      characterCount: chunkContent.length,
      wordCount: this.countWords(chunkContent),
      startPosition,
      endPosition,
      metadata: await this.generateChunkMetadata(chunkContent, originalContent, metadata, sectionTitle),
      parentSection: sectionTitle || '',
      relatedChunks: [],
      quality: this.calculateChunkQuality(chunkContent),
      createdAt: new Date(),
      chunkingStrategy: this.config.strategy
    };

    // Add context if enabled
    if (this.config.preserveContext) {
      chunk.precedingContext = this.extractPrecedingContext(originalContent.cleanedContent, startPosition);
      chunk.followingContext = this.extractFollowingContext(originalContent.cleanedContent, endPosition);
    }

    return chunk;
  }

  private async generateChunkMetadata(
    chunkContent: string,
    originalContent: ProcessedContent,
    metadata?: ExtractedMetadata,
    sectionTitle?: string
  ): Promise<ChunkMetadata> {
    const chunkMetadata: ChunkMetadata = {
      contentType: this.determineContentType(chunkContent),
      hasHeadings: /^#{1,6}\s/.test(chunkContent) || /^[A-Z][^.]*:/.test(chunkContent),
      hasLists: /^\s*[-*•]\s/m.test(chunkContent) || /^\s*\d+\.\s/m.test(chunkContent),
      hasTables: chunkContent.includes('|') || chunkContent.includes('\t'),
      hasLinks: /https?:\/\//.test(chunkContent),
      topics: this.extractChunkTopics(chunkContent),
      keyTerms: this.extractChunkKeyTerms(chunkContent),
      entities: this.extractChunkEntities(chunkContent),
      parentSections: []
    };

    // Add DFSA-specific metadata
    chunkMetadata.dfsaElements = {
      ruleNumbers: this.extractRuleNumbers(chunkContent),
      citations: this.extractCitations(chunkContent),
      complianceRequirements: this.extractComplianceRequirements(chunkContent)
    };

    // Add hierarchy information
    if (metadata && sectionTitle) {
      chunkMetadata.sectionTitle = sectionTitle;
      chunkMetadata.hierarchyLevel = this.determineHierarchyLevel(sectionTitle, metadata);
      chunkMetadata.parentSections = this.getParentSections(sectionTitle, metadata);
    }

    return chunkMetadata;
  }

  private determineContentType(content: string): 'text' | 'heading' | 'list' | 'table' | 'mixed' {
    const hasHeadings = /^#{1,6}\s/.test(content) || /^[A-Z][^.]*:/.test(content);
    const hasLists = /^\s*[-*•]\s/m.test(content) || /^\s*\d+\.\s/m.test(content);
    const hasTables = content.includes('|') || content.includes('\t');

    const typeCount = [hasHeadings, hasLists, hasTables].filter(Boolean).length;

    if (typeCount > 1) return 'mixed';
    if (hasHeadings) return 'heading';
    if (hasLists) return 'list';
    if (hasTables) return 'table';
    return 'text';
  }

  private calculateChunkQuality(content: string): ChunkQuality {
    const completeness = this.calculateCompleteness(content);
    const coherence = this.calculateCoherence(content);
    const informativeness = this.calculateInformativeness(content);
    const contextPreservation = this.calculateContextPreservation(content);

    const overallScore = (completeness + coherence + informativeness + contextPreservation) / 4;

    return {
      completeness,
      coherence,
      informativeness,
      contextPreservation,
      overallScore
    };
  }

  private calculateCompleteness(content: string): number {
    // Check if chunk ends with complete sentences
    const endsWithPunctuation = /[.!?]$/.test(content.trim());
    const hasCompleteThoughts = content.split(/[.!?]/).length > 1;
    
    let score = 0.5;
    if (endsWithPunctuation) score += 0.3;
    if (hasCompleteThoughts) score += 0.2;
    
    return Math.min(score, 1.0);
  }

  private calculateCoherence(content: string): number {
    // Simple coherence check based on sentence flow
    const sentences = content.split(/[.!?]/).filter(s => s.trim().length > 0);
    
    if (sentences.length < 2) return 0.5;
    
    // Check for transition words and coherent flow
    const transitionWords = ['however', 'therefore', 'furthermore', 'moreover', 'additionally', 'consequently'];
    const hasTransitions = transitionWords.some(word => content.toLowerCase().includes(word));
    
    return hasTransitions ? 0.8 : 0.6;
  }

  private calculateInformativeness(content: string): number {
    const wordCount = this.countWords(content);
    const uniqueWords = new Set(content.toLowerCase().split(/\s+/)).size;
    const lexicalDiversity = uniqueWords / wordCount;
    
    // Higher lexical diversity indicates more informativeness
    return Math.min(lexicalDiversity * 2, 1.0);
  }

  private calculateContextPreservation(content: string): number {
    // Check if chunk preserves context (has proper beginning/end)
    const startsWell = /^[A-Z]/.test(content.trim());
    const endsWell = /[.!?]$/.test(content.trim());
    
    let score = 0.3;
    if (startsWell) score += 0.35;
    if (endsWell) score += 0.35;
    
    return score;
  }

  private async detectSemanticBoundaries(text: string): Promise<SemanticBoundary[]> {
    const boundaries: SemanticBoundary[] = [];
    
    // Detect paragraph boundaries
    const paragraphRegex = /\n\s*\n/g;
    let match;
    while ((match = paragraphRegex.exec(text)) !== null) {
      boundaries.push({
        position: match.index,
        confidence: 0.7,
        type: 'paragraph'
      });
    }

    // Detect section boundaries (headings)
    const headingRegex = /^#{1,6}\s.+$/gm;
    while ((match = headingRegex.exec(text)) !== null) {
      boundaries.push({
        position: match.index,
        confidence: 0.9,
        type: 'section'
      });
    }

    // Sort by position
    return boundaries.sort((a, b) => a.position - b.position);
  }

  private async splitLargeSection(
    sectionContent: string,
    originalContent: ProcessedContent,
    startChunkIndex: number,
    metadata?: ExtractedMetadata
  ): Promise<DocumentChunk[]> {
    // Use fixed-size chunking for large sections
    const chunks: DocumentChunk[] = [];
    const chunkSize = this.config.targetChunkSize;
    const overlap = this.config.chunkOverlap;

    let position = 0;
    let chunkIndex = startChunkIndex;

    while (position < sectionContent.length) {
      const endPosition = Math.min(position + chunkSize, sectionContent.length);
      const chunkContent = sectionContent.substring(position, endPosition);

      const chunk = await this.createChunk(
        chunkContent,
        originalContent,
        chunkIndex,
        position,
        endPosition,
        metadata
      );

      chunks.push(chunk);

      position += chunkContent.length - overlap;
      chunkIndex++;
    }

    return chunks;
  }

  private async splitChunkSemantically(
    chunk: DocumentChunk,
    originalContent: ProcessedContent,
    metadata?: ExtractedMetadata
  ): Promise<DocumentChunk[]> {
    // Split chunk using semantic boundaries
    const boundaries = await this.detectSemanticBoundaries(chunk.content);
    
    if (boundaries.length === 0) {
      return [chunk]; // Cannot split further
    }

    const subChunks: DocumentChunk[] = [];
    let currentPosition = 0;
    let subChunkIndex = 0;

    for (const boundary of boundaries) {
      if (boundary.position > currentPosition) {
        const subChunkContent = chunk.content.substring(currentPosition, boundary.position);
        
        if (subChunkContent.length >= this.config.minChunkSize) {
          const subChunk = await this.createChunk(
            subChunkContent,
            originalContent,
            chunk.chunkIndex + subChunkIndex,
            chunk.startPosition + currentPosition,
            chunk.startPosition + boundary.position,
            metadata
          );

          subChunks.push(subChunk);
          subChunkIndex++;
        }

        currentPosition = boundary.position;
      }
    }

    // Handle remaining content
    if (currentPosition < chunk.content.length) {
      const subChunkContent = chunk.content.substring(currentPosition);
      if (subChunkContent.length >= this.config.minChunkSize) {
        const subChunk = await this.createChunk(
          subChunkContent,
          originalContent,
          chunk.chunkIndex + subChunkIndex,
          chunk.startPosition + currentPosition,
          chunk.endPosition,
          metadata
        );

        subChunks.push(subChunk);
      }
    }

    return subChunks.length > 0 ? subChunks : [chunk];
  }

  private async postProcessChunks(
    chunks: DocumentChunk[],
    originalContent: ProcessedContent
  ): Promise<DocumentChunk[]> {
    // Set total chunks count
    chunks.forEach(chunk => {
      chunk.totalChunks = chunks.length;
    });

    // Balance chunk sizes if enabled
    if (this.config.balanceChunkSizes) {
      chunks = await this.balanceChunkSizes(chunks, originalContent);
    }

    // Establish relationships between chunks
    chunks = this.establishChunkRelationships(chunks);

    return chunks;
  }

  private async balanceChunkSizes(
    chunks: DocumentChunk[],
    originalContent: ProcessedContent
  ): Promise<DocumentChunk[]> {
    // Simple balancing: merge very small chunks with adjacent ones
    const balancedChunks: DocumentChunk[] = [];
    
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];

      if (!chunk) {
        continue;
      }

      if (chunk.characterCount < this.config.minChunkSize && i < chunks.length - 1) {
        // Merge with next chunk
        const nextChunk = chunks[i + 1];

        if (!nextChunk) {
          balancedChunks.push(chunk);
          continue;
        }

        const mergedContent = chunk.content + '\n\n' + nextChunk.content;

        const mergedChunk = await this.createChunk(
          mergedContent,
          originalContent,
          chunk.chunkIndex,
          chunk.startPosition,
          nextChunk.endPosition
        );

        balancedChunks.push(mergedChunk);
        i++; // Skip next chunk as it's been merged
      } else {
        balancedChunks.push(chunk);
      }
    }

    return balancedChunks;
  }

  private establishChunkRelationships(chunks: DocumentChunk[]): DocumentChunk[] {
    // Establish relationships between adjacent chunks
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];

      if (!chunk) {
        continue;
      }

      // Add adjacent chunks as related
      if (i > 0) {
        const prevChunk = chunks[i - 1];
        if (prevChunk) {
          chunk.relatedChunks.push(prevChunk.id);
        }
      }
      if (i < chunks.length - 1) {
        const nextChunk = chunks[i + 1];
        if (nextChunk) {
          chunk.relatedChunks.push(nextChunk.id);
        }
      }
    }

    return chunks;
  }

  private calculateQualityMetrics(chunks: DocumentChunk[]) {
    if (chunks.length === 0) {
      return {
        averageQuality: 0,
        minQuality: 0,
        maxQuality: 0,
        qualityDistribution: []
      };
    }

    const qualities = chunks.map(chunk => chunk.quality.overallScore);
    const averageQuality = qualities.reduce((sum, q) => sum + q, 0) / qualities.length;
    const minQuality = Math.min(...qualities);
    const maxQuality = Math.max(...qualities);

    // Create quality distribution (0-0.2, 0.2-0.4, 0.4-0.6, 0.6-0.8, 0.8-1.0)
    const distribution = [0, 0, 0, 0, 0];
    for (const quality of qualities) {
      const bucket = Math.min(Math.floor(quality * 5), 4);
      if (distribution[bucket] !== undefined) {
        distribution[bucket]++;
      }
    }

    return {
      averageQuality,
      minQuality,
      maxQuality,
      qualityDistribution: distribution
    };
  }

  // Utility methods

  private extractPrecedingContext(content: string, position: number): string {
    const contextLength = 100;
    const start = Math.max(0, position - contextLength);
    return content.substring(start, position).trim();
  }

  private extractFollowingContext(content: string, position: number): string {
    const contextLength = 100;
    const end = Math.min(content.length, position + contextLength);
    return content.substring(position, end).trim();
  }

  private extractChunkTopics(content: string): string[] {
    // Simplified topic extraction
    const topics: string[] = [];
    const topicKeywords = {
      'compliance': /compliance|regulatory|requirement/gi,
      'financial': /financial|money|payment|fund/gi,
      'legal': /legal|law|regulation|rule/gi,
      'risk': /risk|assessment|management|control/gi
    };

    for (const [topic, pattern] of Object.entries(topicKeywords)) {
      if (pattern.test(content)) {
        topics.push(topic);
      }
    }

    return topics;
  }

  private extractChunkKeyTerms(content: string): string[] {
    const words = content.toLowerCase().split(/\s+/);
    const termFreq: { [key: string]: number } = {};

    for (const word of words) {
      if (word.length > 4 && !this.isStopWord(word)) {
        termFreq[word] = (termFreq[word] || 0) + 1;
      }
    }

    return Object.entries(termFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  }

  private extractChunkEntities(content: string): string[] {
    // Simple entity extraction
    const entities: string[] = [];
    const entityPattern = /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g;
    const matches = content.match(entityPattern);
    
    if (matches) {
      entities.push(...matches.slice(0, 5));
    }

    return entities;
  }

  private extractRuleNumbers(content: string): string[] {
    const ruleNumbers: string[] = [];
    const rulePattern = /Rule\s+([A-Z]?\d+(?:\.\d+)*)/gi;
    let match;
    
    while ((match = rulePattern.exec(content)) !== null) {
      if (match[1]) {
        ruleNumbers.push(match[1]);
      }
    }
    
    return [...new Set(ruleNumbers)];
  }

  private extractCitations(content: string): string[] {
    const citations: string[] = [];
    const patterns = [
      /Rule\s+([A-Z]?\d+(?:\.\d+)*)/gi,
      /Section\s+(\d+(?:\.\d+)*)/gi
    ];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        citations.push(match[0]);
      }
    }
    
    return [...new Set(citations)];
  }

  private extractComplianceRequirements(content: string): string[] {
    const requirements: string[] = [];
    const patterns = [
      /must\s+[^.]+/gi,
      /shall\s+[^.]+/gi,
      /required\s+to\s+[^.]+/gi
    ];
    
    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const requirement = match[0].trim();
        if (requirement.length > 10 && requirement.length < 100) {
          requirements.push(requirement);
        }
      }
    }
    
    return requirements.slice(0, 5);
  }

  private determineHierarchyLevel(sectionTitle: string, metadata: ExtractedMetadata): number {
    // Find the section in metadata and return its level
    const section = metadata.sections.find(s => s.title === sectionTitle);
    return section?.level || 1;
  }

  private getParentSections(sectionTitle: string, metadata: ExtractedMetadata): string[] {
    // Find parent sections based on hierarchy
    const parentSections: string[] = [];
    
    const section = metadata.sections.find(s => s.title === sectionTitle);
    if (section && section.level > 1) {
      // Find sections with lower level numbers (higher in hierarchy)
      const higherSections = metadata.sections.filter(s => s.level < section.level);
      parentSections.push(...higherSections.map(s => s.title));
    }
    
    return parentSections;
  }

  private isStopWord(word: string): boolean {
    const stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    return stopWords.includes(word);
  }

  private generateChunkId(url: string, chunkIndex: number): string {
    const urlHash = this.simpleHash(url);
    return `chunk_${urlHash}_${chunkIndex}`;
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16);
  }

  private countWords(content: string): number {
    return content.split(/\s+/).filter(word => word.length > 0).length;
  }
}