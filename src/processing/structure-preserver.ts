import { logger } from '../utils/logger';
import { DocumentChunk } from './document-chunker';
import { ExtractedMetadata } from './metadata-extractor';

/**
 * Configuration for structure preservation
 */
export interface StructurePreserverConfig {
  // Relationship preservation
  preserveHierarchicalRelationships: boolean;
  preserveSequentialRelationships: boolean;
  preserveCrossReferences: boolean;
  
  // Context preservation
  maintainDocumentContext: boolean;
  preserveSectionContext: boolean;
  preserveChapterContext: boolean;
  
  // Reference preservation
  trackCitations: boolean;
  trackRuleReferences: boolean;
  trackInternalLinks: boolean;
  
  // Relationship strength
  strongRelationshipThreshold: number;
  weakRelationshipThreshold: number;
  
  // Context window
  contextWindowSize: number;
  maxRelationships: number;
}

/**
 * Document relationship types
 */
export enum RelationshipType {
  HIERARCHICAL = 'hierarchical',
  SEQUENTIAL = 'sequential',
  CROSS_REFERENCE = 'cross_reference',
  CITATION = 'citation',
  SEMANTIC = 'semantic',
  STRUCTURAL = 'structural'
}

/**
 * Relationship strength levels
 */
export enum RelationshipStrength {
  STRONG = 'strong',
  MEDIUM = 'medium',
  WEAK = 'weak'
}

/**
 * Document relationship interface
 */
export interface DocumentRelationship {
  id: string;
  sourceChunkId: string;
  targetChunkId: string;
  type: RelationshipType;
  strength: RelationshipStrength;
  confidence: number;
  description: string;
  metadata: RelationshipMetadata;
  createdAt: Date;
}

/**
 * Relationship metadata
 */
export interface RelationshipMetadata {
  // Reference information
  referenceText?: string;
  referenceType?: string;
  
  // Position information
  sourcePosition: number;
  targetPosition: number;
  
  // Context information
  contextBefore?: string;
  contextAfter?: string;
  
  // DFSA-specific metadata
  dfsaMetadata?: {
    ruleReference?: string;
    sectionReference?: string;
    complianceRelation?: string;
  };
}

/**
 * Document context interface
 */
export interface DocumentContext {
  chunkId: string;
  
  // Hierarchical context
  parentSections: string[];
  childSections: string[];
  siblingChunks: string[];
  
  // Sequential context
  previousChunks: string[];
  nextChunks: string[];
  
  // Document structure context
  documentTitle?: string;
  chapterTitle?: string;
  sectionTitle?: string;
  subsectionTitle?: string;
  
  // Position context
  documentPosition: number;
  sectionPosition: number;
  relativePosition: number; // 0-1 within document
  
  // Semantic context
  topics: string[];
  keyTerms: string[];
  entities: string[];
}

/**
 * Preserved structure result
 */
export interface PreservedStructure {
  chunks: DocumentChunk[];
  relationships: DocumentRelationship[];
  contexts: Map<string, DocumentContext>;
  
  // Structure metrics
  totalRelationships: number;
  relationshipTypes: { [key in RelationshipType]: number };
  averageRelationshipsPerChunk: number;
  structureComplexity: number;
  
  // Processing info
  processingDuration: number;
  preservationQuality: number;
}

/**
 * StructurePreserver - Maintains document relationships and context during chunking
 * Ensures that chunk relationships preserve the original document structure
 */
export class StructurePreserver {
  private config: StructurePreserverConfig;

  constructor(config: Partial<StructurePreserverConfig> = {}) {
    this.config = {
      // Default configuration
      preserveHierarchicalRelationships: true,
      preserveSequentialRelationships: true,
      preserveCrossReferences: true,
      maintainDocumentContext: true,
      preserveSectionContext: true,
      preserveChapterContext: true,
      trackCitations: true,
      trackRuleReferences: true,
      trackInternalLinks: true,
      strongRelationshipThreshold: 0.8,
      weakRelationshipThreshold: 0.3,
      contextWindowSize: 3,
      maxRelationships: 50,
      ...config
    };

    logger.info('StructurePreserver initialized', { config: this.config });
  }

  /**
   * Preserve document structure and relationships for chunks
   */
  async preserveStructure(
    chunks: DocumentChunk[],
    metadata: ExtractedMetadata,
    originalContent: string
  ): Promise<PreservedStructure> {
    const startTime = Date.now();
    
    logger.info('Preserving document structure', { 
      chunkCount: chunks.length,
      metadataSections: metadata.sections.length
    });

    try {
      // Create document contexts for each chunk
      const contexts = await this.createDocumentContexts(chunks, metadata, originalContent);

      // Establish relationships between chunks
      const relationships = await this.establishRelationships(chunks, metadata, contexts);

      // Update chunks with relationship information
      const updatedChunks = this.updateChunksWithRelationships(chunks, relationships, contexts);

      // Calculate structure metrics
      const structureMetrics = this.calculateStructureMetrics(relationships, chunks.length);

      const processingDuration = Date.now() - startTime;

      const preservedStructure: PreservedStructure = {
        chunks: updatedChunks,
        relationships,
        contexts,
        totalRelationships: relationships.length,
        relationshipTypes: structureMetrics.relationshipTypes,
        averageRelationshipsPerChunk: structureMetrics.averageRelationshipsPerChunk,
        structureComplexity: structureMetrics.structureComplexity,
        processingDuration,
        preservationQuality: this.calculatePreservationQuality(relationships, contexts, chunks.length)
      };

      logger.info('Document structure preservation completed', {
        chunkCount: chunks.length,
        totalRelationships: relationships.length,
        averageRelationshipsPerChunk: structureMetrics.averageRelationshipsPerChunk,
        preservationQuality: preservedStructure.preservationQuality,
        processingDuration
      });

      return preservedStructure;

    } catch (error) {
      const processingDuration = Date.now() - startTime;
      
      logger.error('Document structure preservation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        processingDuration
      });

      throw new Error(`Structure preservation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Private helper methods

  private async createDocumentContexts(
    chunks: DocumentChunk[],
    metadata: ExtractedMetadata,
    originalContent: string
  ): Promise<Map<string, DocumentContext>> {
    const contexts = new Map<string, DocumentContext>();

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];

      if (!chunk) {
        continue;
      }

      const context: DocumentContext = {
        chunkId: chunk.id,
        parentSections: this.findParentSections(chunk, metadata),
        childSections: this.findChildSections(chunk, metadata),
        siblingChunks: this.findSiblingChunks(chunk, chunks),
        previousChunks: this.findPreviousChunks(chunk, chunks, this.config.contextWindowSize),
        nextChunks: this.findNextChunks(chunk, chunks, this.config.contextWindowSize),
        documentTitle: this.extractDocumentTitle(metadata) || '',
        chapterTitle: this.findChapterTitle(chunk, metadata) || '',
        sectionTitle: this.findSectionTitle(chunk, metadata) || '',
        subsectionTitle: this.findSubsectionTitle(chunk, metadata) || '',
        documentPosition: i,
        sectionPosition: this.findSectionPosition(chunk, metadata),
        relativePosition: i / chunks.length,
        topics: chunk.metadata.topics,
        keyTerms: chunk.metadata.keyTerms,
        entities: chunk.metadata.entities
      };

      contexts.set(chunk.id, context);
    }

    return contexts;
  }

  private async establishRelationships(
    chunks: DocumentChunk[],
    metadata: ExtractedMetadata,
    contexts: Map<string, DocumentContext>
  ): Promise<DocumentRelationship[]> {
    const relationships: DocumentRelationship[] = [];

    // Establish hierarchical relationships
    if (this.config.preserveHierarchicalRelationships) {
      const hierarchicalRels = await this.createHierarchicalRelationships(chunks, metadata, contexts);
      relationships.push(...hierarchicalRels);
    }

    // Establish sequential relationships
    if (this.config.preserveSequentialRelationships) {
      const sequentialRels = await this.createSequentialRelationships(chunks, contexts);
      relationships.push(...sequentialRels);
    }

    // Establish cross-reference relationships
    if (this.config.preserveCrossReferences) {
      const crossRefRels = await this.createCrossReferenceRelationships(chunks, metadata);
      relationships.push(...crossRefRels);
    }

    // Establish citation relationships
    if (this.config.trackCitations) {
      const citationRels = await this.createCitationRelationships(chunks, metadata);
      relationships.push(...citationRels);
    }

    // Establish semantic relationships
    const semanticRels = await this.createSemanticRelationships(chunks, contexts);
    relationships.push(...semanticRels);

    // Limit relationships per chunk
    return this.limitRelationships(relationships, chunks.length);
  }

  private async createHierarchicalRelationships(
    chunks: DocumentChunk[],
    metadata: ExtractedMetadata,
    contexts: Map<string, DocumentContext>
  ): Promise<DocumentRelationship[]> {
    const relationships: DocumentRelationship[] = [];

    for (const chunk of chunks) {
      const context = contexts.get(chunk.id);
      if (!context) continue;

      // Create parent-child relationships
      for (const parentSection of context.parentSections) {
        const parentChunk = this.findChunkBySection(parentSection, chunks);
        if (parentChunk && parentChunk.id !== chunk.id) {
          const relationship = this.createRelationship(
            parentChunk.id,
            chunk.id,
            RelationshipType.HIERARCHICAL,
            RelationshipStrength.STRONG,
            0.9,
            `Parent section relationship: ${parentSection}`,
            {
              sourcePosition: parentChunk.startPosition,
              targetPosition: chunk.startPosition,
              referenceText: parentSection,
              referenceType: 'parent_section'
            }
          );
          relationships.push(relationship);
        }
      }

      // Create sibling relationships
      for (const siblingId of context.siblingChunks) {
        if (siblingId !== chunk.id) {
          const relationship = this.createRelationship(
            chunk.id,
            siblingId,
            RelationshipType.HIERARCHICAL,
            RelationshipStrength.MEDIUM,
            0.7,
            'Sibling chunk relationship',
            {
              sourcePosition: chunk.startPosition,
              targetPosition: chunks.find(c => c.id === siblingId)?.startPosition || 0,
              referenceType: 'sibling'
            }
          );
          relationships.push(relationship);
        }
      }
    }

    return relationships;
  }

  private async createSequentialRelationships(
    chunks: DocumentChunk[],
    contexts: Map<string, DocumentContext>
  ): Promise<DocumentRelationship[]> {
    const relationships: DocumentRelationship[] = [];

    for (const chunk of chunks) {
      const context = contexts.get(chunk.id);
      if (!context) continue;

      // Create relationships with previous chunks
      for (const prevChunkId of context.previousChunks) {
        const relationship = this.createRelationship(
          prevChunkId,
          chunk.id,
          RelationshipType.SEQUENTIAL,
          RelationshipStrength.STRONG,
          0.8,
          'Sequential predecessor relationship',
          {
            sourcePosition: chunks.find(c => c.id === prevChunkId)?.startPosition || 0,
            targetPosition: chunk.startPosition,
            referenceType: 'sequential_predecessor'
          }
        );
        relationships.push(relationship);
      }

      // Create relationships with next chunks
      for (const nextChunkId of context.nextChunks) {
        const relationship = this.createRelationship(
          chunk.id,
          nextChunkId,
          RelationshipType.SEQUENTIAL,
          RelationshipStrength.STRONG,
          0.8,
          'Sequential successor relationship',
          {
            sourcePosition: chunk.startPosition,
            targetPosition: chunks.find(c => c.id === nextChunkId)?.startPosition || 0,
            referenceType: 'sequential_successor'
          }
        );
        relationships.push(relationship);
      }
    }

    return relationships;
  }

  private async createCrossReferenceRelationships(
    chunks: DocumentChunk[],
    metadata: ExtractedMetadata
  ): Promise<DocumentRelationship[]> {
    const relationships: DocumentRelationship[] = [];

    for (const chunk of chunks) {
      // Find cross-references in chunk content
      const crossRefs = this.findCrossReferences(chunk.content);
      
      for (const crossRef of crossRefs) {
        // Find target chunk that contains the referenced content
        const targetChunk = this.findChunkByReference(crossRef, chunks);
        
        if (targetChunk && targetChunk.id !== chunk.id) {
          const relationship = this.createRelationship(
            chunk.id,
            targetChunk.id,
            RelationshipType.CROSS_REFERENCE,
            RelationshipStrength.MEDIUM,
            0.6,
            `Cross-reference: ${crossRef.text}`,
            {
              sourcePosition: chunk.startPosition,
              targetPosition: targetChunk.startPosition,
              referenceText: crossRef.text,
              referenceType: crossRef.type
            }
          );
          relationships.push(relationship);
        }
      }
    }

    return relationships;
  }

  private async createCitationRelationships(
    chunks: DocumentChunk[],
    metadata: ExtractedMetadata
  ): Promise<DocumentRelationship[]> {
    const relationships: DocumentRelationship[] = [];

    for (const chunk of chunks) {
      // Use DFSA metadata if available
      if (chunk.metadata.dfsaElements) {
        const { ruleNumbers, citations } = chunk.metadata.dfsaElements;
        
        // Create relationships for rule references
        for (const ruleNumber of ruleNumbers) {
          const targetChunk = this.findChunkByRuleNumber(ruleNumber, chunks);
          
          if (targetChunk && targetChunk.id !== chunk.id) {
            const relationship = this.createRelationship(
              chunk.id,
              targetChunk.id,
              RelationshipType.CITATION,
              RelationshipStrength.STRONG,
              0.9,
              `Rule citation: ${ruleNumber}`,
              {
                sourcePosition: chunk.startPosition,
                targetPosition: targetChunk.startPosition,
                referenceText: `Rule ${ruleNumber}`,
                referenceType: 'rule_citation',
                dfsaMetadata: {
                  ruleReference: ruleNumber
                }
              }
            );
            relationships.push(relationship);
          }
        }

        // Create relationships for other citations
        for (const citation of citations) {
          const targetChunk = this.findChunkByCitation(citation, chunks);
          
          if (targetChunk && targetChunk.id !== chunk.id) {
            const relationship = this.createRelationship(
              chunk.id,
              targetChunk.id,
              RelationshipType.CITATION,
              RelationshipStrength.MEDIUM,
              0.7,
              `Citation: ${citation}`,
              {
                sourcePosition: chunk.startPosition,
                targetPosition: targetChunk.startPosition,
                referenceText: citation,
                referenceType: 'citation'
              }
            );
            relationships.push(relationship);
          }
        }
      }
    }

    return relationships;
  }

  private async createSemanticRelationships(
    chunks: DocumentChunk[],
    contexts: Map<string, DocumentContext>
  ): Promise<DocumentRelationship[]> {
    const relationships: DocumentRelationship[] = [];

    for (let i = 0; i < chunks.length; i++) {
      const chunk1 = chunks[i];

      if (!chunk1) {
        continue;
      }

      for (let j = i + 1; j < chunks.length; j++) {
        const chunk2 = chunks[j];

        if (!chunk2) {
          continue;
        }

        // Calculate semantic similarity
        const similarity = this.calculateSemanticSimilarity(chunk1, chunk2);

        if (similarity >= this.config.weakRelationshipThreshold) {
          const strength = similarity >= this.config.strongRelationshipThreshold
            ? RelationshipStrength.STRONG
            : similarity >= 0.5
              ? RelationshipStrength.MEDIUM
              : RelationshipStrength.WEAK;

          const relationship = this.createRelationship(
            chunk1.id,
            chunk2.id,
            RelationshipType.SEMANTIC,
            strength,
            similarity,
            `Semantic similarity: ${(similarity * 100).toFixed(1)}%`,
            {
              sourcePosition: chunk1.startPosition,
              targetPosition: chunk2.startPosition,
              referenceType: 'semantic_similarity'
            }
          );
          relationships.push(relationship);
        }
      }
    }

    return relationships;
  }

  private calculateSemanticSimilarity(chunk1: DocumentChunk, chunk2: DocumentChunk): number {
    // Simple semantic similarity based on shared topics and key terms
    const topics1 = new Set(chunk1.metadata.topics);
    const topics2 = new Set(chunk2.metadata.topics);
    const keyTerms1 = new Set(chunk1.metadata.keyTerms);
    const keyTerms2 = new Set(chunk2.metadata.keyTerms);

    // Calculate topic overlap
    const topicOverlap = this.calculateSetOverlap(topics1, topics2);
    
    // Calculate key term overlap
    const keyTermOverlap = this.calculateSetOverlap(keyTerms1, keyTerms2);

    // Weighted average
    return (topicOverlap * 0.6) + (keyTermOverlap * 0.4);
  }

  private calculateSetOverlap(set1: Set<string>, set2: Set<string>): number {
    if (set1.size === 0 && set2.size === 0) return 0;
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }

  private createRelationship(
    sourceId: string,
    targetId: string,
    type: RelationshipType,
    strength: RelationshipStrength,
    confidence: number,
    description: string,
    metadata: Partial<RelationshipMetadata>
  ): DocumentRelationship {
    return {
      id: this.generateRelationshipId(sourceId, targetId, type),
      sourceChunkId: sourceId,
      targetChunkId: targetId,
      type,
      strength,
      confidence,
      description,
      metadata: {
        sourcePosition: 0,
        targetPosition: 0,
        ...metadata
      },
      createdAt: new Date()
    };
  }

  private limitRelationships(
    relationships: DocumentRelationship[],
    chunkCount: number
  ): DocumentRelationship[] {
    // Sort by confidence and limit per chunk
    const relationshipsByChunk = new Map<string, DocumentRelationship[]>();
    
    for (const rel of relationships) {
      if (!relationshipsByChunk.has(rel.sourceChunkId)) {
        relationshipsByChunk.set(rel.sourceChunkId, []);
      }
      relationshipsByChunk.get(rel.sourceChunkId)!.push(rel);
    }

    const limitedRelationships: DocumentRelationship[] = [];
    
    for (const [chunkId, rels] of relationshipsByChunk.entries()) {
      // Sort by confidence and take top relationships
      const sortedRels = rels.sort((a, b) => b.confidence - a.confidence);
      const limitedRels = sortedRels.slice(0, this.config.maxRelationships);
      limitedRelationships.push(...limitedRels);
    }

    return limitedRelationships;
  }

  private updateChunksWithRelationships(
    chunks: DocumentChunk[],
    relationships: DocumentRelationship[],
    contexts: Map<string, DocumentContext>
  ): DocumentChunk[] {
    const relationshipsByChunk = new Map<string, DocumentRelationship[]>();
    
    // Group relationships by source chunk
    for (const rel of relationships) {
      if (!relationshipsByChunk.has(rel.sourceChunkId)) {
        relationshipsByChunk.set(rel.sourceChunkId, []);
      }
      relationshipsByChunk.get(rel.sourceChunkId)!.push(rel);
    }

    // Update chunks with relationship information
    return chunks.map(chunk => {
      const chunkRelationships = relationshipsByChunk.get(chunk.id) || [];
      const context = contexts.get(chunk.id);
      
      return {
        ...chunk,
        relatedChunks: chunkRelationships.map(rel => rel.targetChunkId),
        metadata: {
          ...chunk.metadata,
          parentSections: context?.parentSections || []
        }
      };
    });
  }

  private calculateStructureMetrics(relationships: DocumentRelationship[], chunkCount: number) {
    const relationshipTypes: { [key in RelationshipType]: number } = {
      [RelationshipType.HIERARCHICAL]: 0,
      [RelationshipType.SEQUENTIAL]: 0,
      [RelationshipType.CROSS_REFERENCE]: 0,
      [RelationshipType.CITATION]: 0,
      [RelationshipType.SEMANTIC]: 0,
      [RelationshipType.STRUCTURAL]: 0
    };

    for (const rel of relationships) {
      relationshipTypes[rel.type]++;
    }

    const averageRelationshipsPerChunk = chunkCount > 0 ? relationships.length / chunkCount : 0;
    
    // Calculate structure complexity based on relationship diversity and density
    const typeCount = Object.values(relationshipTypes).filter(count => count > 0).length;
    const density = averageRelationshipsPerChunk / 10; // Normalize to 0-1 scale
    const structureComplexity = Math.min((typeCount / 6) * 0.5 + density * 0.5, 1.0);

    return {
      relationshipTypes,
      averageRelationshipsPerChunk,
      structureComplexity
    };
  }

  private calculatePreservationQuality(
    relationships: DocumentRelationship[],
    contexts: Map<string, DocumentContext>,
    chunkCount: number
  ): number {
    let quality = 0.5; // Base quality

    // Relationship density factor
    const relationshipDensity = relationships.length / chunkCount;
    if (relationshipDensity > 2) quality += 0.2;
    if (relationshipDensity > 5) quality += 0.1;

    // Relationship type diversity
    const typeSet = new Set(relationships.map(rel => rel.type));
    quality += (typeSet.size / 6) * 0.2;

    // Context completeness
    const contextsWithParents = Array.from(contexts.values()).filter(ctx => ctx.parentSections.length > 0).length;
    quality += (contextsWithParents / chunkCount) * 0.1;

    return Math.min(quality, 1.0);
  }

  // Utility methods for finding relationships

  private findParentSections(chunk: DocumentChunk, metadata: ExtractedMetadata): string[] {
    // Find parent sections based on hierarchy
    const parentSections: string[] = [];
    
    if (chunk.parentSection) {
      parentSections.push(chunk.parentSection);
      
      // Find parent of parent section
      const parentSection = metadata.sections.find(s => s.title === chunk.parentSection);
      if (parentSection && parentSection.level > 1) {
        // Find sections with lower level numbers (higher in hierarchy)
        const higherSections = metadata.sections.filter(s => s.level < parentSection.level);
        parentSections.push(...higherSections.map(s => s.title));
      }
    }
    
    return parentSections;
  }

  private findChildSections(chunk: DocumentChunk, metadata: ExtractedMetadata): string[] {
    // Find child sections based on hierarchy
    const childSections: string[] = [];
    
    if (chunk.parentSection) {
      const currentSection = metadata.sections.find(s => s.title === chunk.parentSection);
      if (currentSection) {
        // Find sections with higher level numbers (lower in hierarchy)
        const lowerSections = metadata.sections.filter(s => s.level > currentSection.level);
        childSections.push(...lowerSections.map(s => s.title));
      }
    }
    
    return childSections;
  }

  private findSiblingChunks(chunk: DocumentChunk, chunks: DocumentChunk[]): string[] {
    // Find chunks in the same section
    return chunks
      .filter(c => c.parentSection === chunk.parentSection && c.id !== chunk.id)
      .map(c => c.id);
  }

  private findPreviousChunks(chunk: DocumentChunk, chunks: DocumentChunk[], windowSize: number): string[] {
    const chunkIndex = chunks.findIndex(c => c.id === chunk.id);
    if (chunkIndex === -1) return [];
    
    const startIndex = Math.max(0, chunkIndex - windowSize);
    return chunks.slice(startIndex, chunkIndex).map(c => c.id);
  }

  private findNextChunks(chunk: DocumentChunk, chunks: DocumentChunk[], windowSize: number): string[] {
    const chunkIndex = chunks.findIndex(c => c.id === chunk.id);
    if (chunkIndex === -1) return [];
    
    const endIndex = Math.min(chunks.length, chunkIndex + windowSize + 1);
    return chunks.slice(chunkIndex + 1, endIndex).map(c => c.id);
  }

  private extractDocumentTitle(metadata: ExtractedMetadata): string | undefined {
    const mainTitle = metadata.titles.find(t => t.type === 'main');
    return mainTitle?.text;
  }

  private findChapterTitle(chunk: DocumentChunk, metadata: ExtractedMetadata): string | undefined {
    const chapterTitle = metadata.titles.find(t => t.type === 'chapter');
    return chapterTitle?.text;
  }

  private findSectionTitle(chunk: DocumentChunk, metadata: ExtractedMetadata): string | undefined {
    return chunk.parentSection;
  }

  private findSubsectionTitle(chunk: DocumentChunk, metadata: ExtractedMetadata): string | undefined {
    const subsectionTitle = metadata.titles.find(t => t.type === 'subsection');
    return subsectionTitle?.text;
  }

  private findSectionPosition(chunk: DocumentChunk, metadata: ExtractedMetadata): number {
    if (!chunk.parentSection) return 0;
    
    const section = metadata.sections.find(s => s.title === chunk.parentSection);
    return section?.position || 0;
  }

  private findChunkBySection(sectionTitle: string, chunks: DocumentChunk[]): DocumentChunk | undefined {
    return chunks.find(c => c.parentSection === sectionTitle);
  }

  private findCrossReferences(content: string): Array<{ text: string; type: string }> {
    const crossRefs: Array<{ text: string; type: string }> = [];
    
    // Find "see Section X" references
    const sectionRefs = content.match(/see\s+section\s+[\d.]+/gi);
    if (sectionRefs) {
      crossRefs.push(...sectionRefs.map(ref => ({ text: ref, type: 'section_reference' })));
    }
    
    // Find "as described in" references
    const descriptionRefs = content.match(/as\s+described\s+in\s+[^.]+/gi);
    if (descriptionRefs) {
      crossRefs.push(...descriptionRefs.map(ref => ({ text: ref, type: 'description_reference' })));
    }
    
    return crossRefs;
  }

  private findChunkByReference(crossRef: { text: string; type: string }, chunks: DocumentChunk[]): DocumentChunk | undefined {
    // Simple implementation - would need more sophisticated matching in production
    return chunks.find(c => c.content.toLowerCase().includes(crossRef.text.toLowerCase()));
  }

  private findChunkByRuleNumber(ruleNumber: string, chunks: DocumentChunk[]): DocumentChunk | undefined {
    return chunks.find(c => 
      c.metadata.dfsaElements?.ruleNumbers.includes(ruleNumber) ||
      c.content.includes(`Rule ${ruleNumber}`)
    );
  }

  private findChunkByCitation(citation: string, chunks: DocumentChunk[]): DocumentChunk | undefined {
    return chunks.find(c => c.content.includes(citation));
  }

  private generateRelationshipId(sourceId: string, targetId: string, type: RelationshipType): string {
    const combined = `${sourceId}_${targetId}_${type}`;
    return this.simpleHash(combined);
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(16);
  }
}