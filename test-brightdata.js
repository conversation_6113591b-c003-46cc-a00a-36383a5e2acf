require('dotenv').config();
const puppeteer = require('puppeteer-core');

async function testBrightdataConnection() {
  console.log('🧪 Testing Brightdata connection with credentials from .env...');
  
  const username = process.env.BRIGHTDATA_USERNAME;
  const password = process.env.BRIGHTDATA_PASSWORD;
  
  if (!username || !password) {
    console.error('❌ Missing Brightdata credentials in .env file');
    console.log('Please set BRIGHTDATA_USERNAME and BRIGHTDATA_PASSWORD in .env');
    return;
  }
  
  const auth = `${username}:${password}`;
  const browserWSEndpoint = `wss://${auth}@brd.superproxy.io:9222`;
  
  console.log('🔗 Connecting to:', browserWSEndpoint.replace(password, '***'));
  
  try {
    const browser = await puppeteer.connect({
      browserWSEndpoint,
      timeout: 60000
    });
    
    console.log('✅ Successfully connected to Brightdata Browser API!');
    
    const page = await browser.newPage();
    
    // Test with geo endpoint
    console.log('🌍 Testing geo endpoint...');
    await page.goto('https://geo.brdtest.com/mygeo.json', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });
    
    const content = await page.content();
    const title = await page.title();
    
    console.log('📄 Page title:', title);
    console.log('📄 Content preview:', content.substring(0, 300));
    
    // Try to extract geo data
    try {
      const bodyMatch = content.match(/<body[^>]*>(.*?)<\/body>/s);
      const bodyContent = bodyMatch?.[1]?.trim() || content;
      const geoData = JSON.parse(bodyContent);
      console.log('🌍 Geo data:', geoData);
    } catch (parseError) {
      console.log('⚠️  Could not parse geo data, but connection successful');
    }
    
    await page.close();
    await browser.close();
    
    // Test DFSA website - NEW SESSION REQUIRED
    console.log('\\n🏛️  Testing DFSA website (new session)...');
    console.log('🔗 Creating new browser connection for DFSA...');
    
    const dfsaBrowser = await puppeteer.connect({
      browserWSEndpoint,
      timeout: 60000
    });
    
    const dfsaPage = await dfsaBrowser.newPage();
    
    await dfsaPage.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    
    await dfsaPage.goto('https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa', {
      waitUntil: 'networkidle2',
      timeout: 60000
    });
    
    const dfsaTitle = await dfsaPage.title();
    const dfsaContent = await dfsaPage.content();
    
    console.log('📄 DFSA Title:', dfsaTitle);
    console.log('📄 DFSA Content length:', dfsaContent.length, 'characters');
    console.log('📄 Has DFSA content:', /dfsa|dubai financial services|rulebook/i.test(dfsaContent) ? '✅ Yes' : '❌ No');
    console.log('📄 Content preview:', dfsaContent.substring(0, 500).replace(/\\s+/g, ' '));
    
    await dfsaPage.close();
    await dfsaBrowser.close();
    
    console.log('\\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('Full error:', error);
  }
}

testBrightdataConnection().catch(console.error);