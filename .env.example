# Application
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/dfsa_rag
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# Redis
REDIS_URL=redis://localhost:6379

# AI Providers
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************
COHERE_API_KEY=AjRCna4m9TgGKrOg7e10URLRAUjWyX18ApLbfs2t

# Vector Database
PINECONE_API_KEY=pcsk_6Efjzc_NevUqNP1bt818P8MgRdtDs94YmEJwZKfZvJV6ADy8b52awueWjYw5Cc7Q9PwAtF
PINECONE_ENVIRONMENT=https://rag-system-6dzs5y1.svc.aped-4627-b74a.pinecone.io
PINECONE_INDEX=rag-system

# Web Scraping
BRIGHTDATA_API_KEY=your_brightdata_api_key
BRIGHTDATA_BROWSER_URL=your_brightdata_browser_url
SCRAPING_RATE_LIMIT=5
SCRAPING_CONCURRENT_JOBS=3

# Security
JWT_SECRET=your_jwt_secret
CORS_ORIGINS=http://localhost:3001,http://localhost:3000

# Deployment
DOCKER_REGISTRY=localhost
TAG=latest
DATA_PATH=/data