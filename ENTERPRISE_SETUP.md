# 🏗️ Enterprise Scraping System Setup

## High-Performance Docker Deployment for Million-Scale Operations

This guide covers the complete setup of the enterprise scraping system with **30 Brightdata zones**, **600 concurrent requests**, and **enterprise-grade monitoring**.

---

## 🎯 **System Requirements**

### **Recommended Hardware:**
- **RAM**: 32GB+ (28GB allocated to containers)
- **CPU**: 16+ cores (17 cores allocated to containers)
- **Storage**: 100GB+ SSD
- **Network**: Stable broadband connection

### **Minimum Hardware:**
- **RAM**: 16GB (will work but slower)
- **CPU**: 8+ cores
- **Storage**: 50GB+ SSD

### **Software Requirements:**
- Docker 20.0+
- Docker Compose 2.0+
- Git
- Bash shell

---

## 🚀 **Quick Start**

### **1. Start Enterprise System**
```bash
# Start the complete enterprise infrastructure
npm run enterprise:start

# Or run the script directly
./scripts/start-enterprise.sh
```

### **2. Access Services**
- **🏗️ Enterprise Dashboard**: http://localhost:3001
- **🗄️ PostgreSQL Admin**: http://localhost:8080
- **📊 Redis Insight**: http://localhost:8001
- **📈 Prometheus**: http://localhost:9090
- **📊 Grafana**: http://localhost:3002

### **3. Run Test Scraping**
```bash
# Quick test (100 URLs)
npm run test:enterprise

# Development scale (1K URLs)
npm run start:development

# Production scale (10K URLs)
npm run start:production

# Million scale (100K URLs)
npm run start:million

# Ultra scale (1M URLs)
npm run start:ultra
```

---

## 🏗️ **Architecture Overview**

### **Container Resource Allocation:**

| Service | Memory | CPU | Purpose |
|---------|--------|-----|---------|
| **Scraper App** | 12GB | 8 cores | Main scraping engine |
| **PostgreSQL** | 8GB | 4 cores | Data storage |
| **Redis** | 4GB | 2 cores | Queue & caching |
| **Prometheus** | 2GB | 1 core | Metrics collection |
| **Grafana** | 1GB | 1 core | Visualization |
| **Others** | 1GB | 1 core | Admin tools |
| **Total** | **28GB** | **17 cores** | |

### **Performance Targets:**
- **Throughput**: 300-500 pages/second
- **Concurrency**: 600 simultaneous requests
- **Zones**: 30 Brightdata zones
- **Success Rate**: 98%+
- **Response Time**: <5 seconds average

---

## 📊 **Monitoring & Dashboards**

### **Enterprise Dashboard (Port 3001)**
- Real-time job tracking with unique IDs
- Zone health monitoring
- Circuit breaker status
- Rate limiter visualization
- Cache performance metrics
- Error categorization

### **Grafana (Port 3002)**
- System resource monitoring
- Performance trending
- Alert management
- Custom dashboards

### **Prometheus (Port 9090)**
- Metrics collection
- Alert rules
- Query interface

---

## 🛠️ **Management Commands**

### **System Control:**
```bash
# Start enterprise system
npm run enterprise:start

# Stop all services
npm run enterprise:stop

# View scraper logs
npm run enterprise:logs

# Restart scraper only
npm run enterprise:restart

# Check container status
npm run enterprise:status
```

### **Scraping Operations:**
```bash
# Test all 30 zones
npm run zones:test

# Web dashboard scraping
npm run dashboard:web

# Enterprise demo with different scales
npm run test:enterprise        # 100 URLs
npm run start:development      # 1K URLs
npm run start:production       # 10K URLs
npm run start:million          # 100K URLs
npm run start:ultra            # 1M URLs
```

### **Docker Commands:**
```bash
# View all containers
docker-compose -f docker-compose.enterprise.yml ps

# Scale scraper instances
docker-compose -f docker-compose.enterprise.yml up -d --scale scraper-app=2

# View resource usage
docker stats

# Clean up everything
docker-compose -f docker-compose.enterprise.yml down -v
docker system prune -a
```

---

## 🔧 **Configuration**

### **Environment Variables:**
```bash
# In docker-compose.enterprise.yml
NODE_ENV=production
DATABASE_URL=*************************************************************/brightdata_scraper
REDIS_URL=redis://redis:6379
BRIGHTDATA_API_KEY=e5e6337c844ca6b30d53dc6094079af8950047632c8428686b6fc40352b7f4ee
ENTERPRISE_SCALE=ultra
MAX_CONCURRENCY=600
TOTAL_ZONES=30
```

### **Scale Configurations:**
- **Development**: 3 zones, 10 concurrency
- **Production**: 15 zones, 50 concurrency
- **Million**: 30 zones, 300 concurrency
- **Ultra**: 30 zones, 600 concurrency

---

## 📈 **Performance Optimization**

### **PostgreSQL Tuning:**
- **shared_buffers**: 2GB
- **effective_cache_size**: 6GB
- **work_mem**: 64MB
- **max_connections**: 500

### **Redis Tuning:**
- **maxmemory**: 3GB
- **maxmemory-policy**: allkeys-lru
- **maxclients**: 10,000

### **Node.js Tuning:**
- **max-old-space-size**: 8192MB
- **max-semi-space-size**: 512MB
- **UV_THREADPOOL_SIZE**: 32

---

## 🚨 **Troubleshooting**

### **Common Issues:**

#### **Out of Memory:**
```bash
# Check memory usage
docker stats

# Reduce concurrency
# Edit docker-compose.enterprise.yml
MAX_CONCURRENCY=300
```

#### **Slow Performance:**
```bash
# Check system resources
htop
iostat -x 1

# Monitor container logs
npm run enterprise:logs
```

#### **Connection Issues:**
```bash
# Test zone connectivity
npm run zones:test

# Check Redis connection
docker-compose -f docker-compose.enterprise.yml exec redis redis-cli ping

# Check PostgreSQL connection
docker-compose -f docker-compose.enterprise.yml exec postgres pg_isready
```

#### **Port Conflicts:**
```bash
# Check port usage
netstat -tulpn | grep :3001

# Kill conflicting processes
sudo lsof -ti:3001 | xargs kill -9
```

---

## 📊 **Cost Analysis**

### **Brightdata Costs:**
- **Rate**: $1.50 per 1,000 requests
- **1M URLs**: $1,500
- **100K URLs**: $150
- **10K URLs**: $15

### **Infrastructure Costs (Local):**
- **Electricity**: ~$5/day for high-end workstation
- **Internet**: Existing broadband sufficient

---

## 🎯 **Best Practices**

### **Before Large Runs:**
1. Test with small batches first
2. Monitor system resources
3. Check zone health
4. Verify database connectivity

### **During Operations:**
1. Monitor enterprise dashboard
2. Watch for circuit breaker activations
3. Check memory usage
4. Monitor error rates

### **After Completion:**
1. Review performance metrics
2. Analyze error patterns
3. Export results
4. Clean up temporary data

---

## 🔒 **Security Notes**

### **Default Credentials:**
- **PostgreSQL**: scraper_user / scraper_password_2024
- **pgAdmin**: <EMAIL> / admin_password_2024
- **Grafana**: admin / admin_password_2024

### **Production Recommendations:**
- Change all default passwords
- Use environment variables for secrets
- Enable SSL/TLS for external access
- Implement proper firewall rules

---

## 📞 **Support**

### **Logs Location:**
- **Application**: `./logs/`
- **Docker**: `docker-compose logs`
- **System**: `/var/log/`

### **Monitoring URLs:**
- **Health Check**: http://localhost:3001/api/health
- **Metrics**: http://localhost:9090/metrics
- **Status**: http://localhost:3001/api/metrics

---

**🎉 The enterprise system is ready for million-scale scraping operations with battle-tested reliability and performance!**
