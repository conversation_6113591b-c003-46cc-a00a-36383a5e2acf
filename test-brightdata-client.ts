import { BrightdataClient } from './src/scraping/brightdata-client';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testBrightdataClient() {
  console.log('🧪 Testing Brightdata TypeScript Client...');
  
  if (!process.env.BRIGHTDATA_USERNAME || !process.env.BRIGHTDATA_PASSWORD) {
    console.error('❌ Missing Brightdata credentials in .env file');
    return;
  }

  const client = new BrightdataClient({
    username: process.env.BRIGHTDATA_USERNAME,
    password: process.env.BRIGHTDATA_PASSWORD,
    zone: process.env.BRIGHTDATA_ZONE || 'scraping_browser2',
    country: 'US',
    timeout: 60000,
    rateLimitDelay: 2000
  });

  try {
    console.log('🔍 Testing connection...');
    const connectionTest = await client.testConnection();
    console.log(`Connection test: ${connectionTest ? '✅ Success' : '❌ Failed'}`);

    if (connectionTest) {
      console.log('📊 Getting session info...');
      const sessionInfo = await client.getSessionInfo();
      console.log('Session Info:', sessionInfo);

      console.log('🌐 Testing single URL scraping...');
      const result = await client.scrapeUrl('https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa', {
        waitForTimeout: 10000,
        blockResources: ['image', 'stylesheet', 'font'],
        solveCaptcha: true,
        captchaTimeout: 30000
      });

      console.log('📄 Scraping Result:');
      console.log(`  Status: ${result.error ? '❌ Failed' : '✅ Success'}`);
      console.log(`  Status Code: ${result.statusCode}`);
      console.log(`  Title: ${result.title}`);
      console.log(`  Content Length: ${result.content.length} chars`);
      console.log(`  Load Time: ${result.loadTime}ms`);
      if (result.error) {
        console.log(`  Error: ${result.error}`);
      } else {
        console.log(`  Has DFSA Content: ${/dfsa|dubai financial services|rulebook/i.test(result.content) ? '✅ Yes' : '❌ No'}`);
      }

      console.log('\n📦 Testing batch scraping (multiple sessions)...');
      const testUrls = [
        'https://geo.brdtest.com/mygeo.json',
        'https://dfsaen.thomsonreuters.com/rulebook/general-module'
      ];

      const batchResults = await client.scrapeUrls(testUrls, {
        waitForTimeout: 5000,
        blockResources: ['image', 'stylesheet']
      }, 1); // Low concurrency for testing

      console.log('📊 Batch Results:');
      console.log(`  Total URLs: ${testUrls.length}`);
      console.log(`  Successful: ${batchResults.filter(r => !r.error).length}`);
      console.log(`  Failed: ${batchResults.filter(r => r.error).length}`);

      batchResults.forEach((result, index) => {
        console.log(`  ${index + 1}. ${result.url}`);
        console.log(`     Status: ${result.error ? '❌ Failed' : '✅ Success'}`);
        console.log(`     Title: ${result.title || 'N/A'}`);
        console.log(`     Content: ${result.content.length} chars`);
        if (result.error) {
          console.log(`     Error: ${result.error}`);
        }
      });
    }

    console.log('\n🏥 Health Status:');
    const health = client.getHealthStatus();
    console.log(health);

  } catch (error: any) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await client.close();
    console.log('\n🎉 Test completed!');
  }
}

testBrightdataClient().catch(console.error);