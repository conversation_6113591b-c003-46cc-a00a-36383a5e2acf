/**
 * End-to-End Workflow Test
 * 
 * Tests the complete workflow from URL discovery through RAG querying
 * Validates all quality gates and manual confirmation workflows
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

class EndToEndWorkflowTest {
  constructor() {
    this.baseUrl = 'http://localhost:3000';
    this.testResults = [];
    this.workflowData = {};
    this.timeout = 60000; // 60 seconds for long-running operations
  }

  async runEndToEndTest() {
    console.log('🔄 Starting End-to-End Workflow Test');
    console.log('=' .repeat(60));

    try {
      // Phase 1: URL Discovery and Validation
      await this.testURLDiscovery();

      // Phase 2: Content Scraping with Quality Gates
      await this.testContentScraping();

      // Phase 3: Content Processing and Chunking
      await this.testContentProcessing();

      // Phase 4: Embedding Generation and Vector Storage
      await this.testEmbeddingAndVectorStorage();

      // Phase 5: RAG Query Processing
      await this.testRAGQueryProcessing();

      // Phase 6: Quality Assurance Validation
      await this.testQualityAssurance();

      // Phase 7: Manual Confirmation Workflow
      await this.testManualConfirmationWorkflow();

      // Print comprehensive results
      this.printWorkflowResults();

    } catch (error) {
      console.error('❌ End-to-end workflow test failed:', error.message);
      process.exit(1);
    }
  }

  async testURLDiscovery() {
    console.log('\n🔍 Phase 1: URL Discovery and Validation');

    try {
      const startTime = performance.now();

      // Start a test scraping job with URL discovery
      const scrapingConfig = {
        baseUrl: 'https://www.dfsa.ae/rulebook/general-module',
        mode: 'test',
        maxDepth: 2,
        delay: 1000,
        maxPages: 10,
        enableQualityGates: true
      };

      const response = await axios.post(
        `${this.baseUrl}/api/scraping/start`,
        scrapingConfig,
        { timeout: this.timeout }
      );

      const endTime = performance.now();

      this.assert(response.status === 200, 'URL discovery should start successfully');
      this.assert(response.data.jobId, 'Should return job ID');
      this.assert(response.data.status === 'started', 'Job should be in started status');

      this.workflowData.jobId = response.data.jobId;
      this.workflowData.urlDiscoveryTime = endTime - startTime;

      this.recordTest('URL Discovery', true, 
        `Job ${this.workflowData.jobId} started in ${this.workflowData.urlDiscoveryTime.toFixed(2)}ms`);

      console.log(`   ✅ URL discovery started: Job ${this.workflowData.jobId}`);
      console.log(`   ⏱️  Discovery time: ${this.workflowData.urlDiscoveryTime.toFixed(2)}ms`);

    } catch (error) {
      this.recordTest('URL Discovery', false, error.message);
      throw new Error(`URL discovery failed: ${error.message}`);
    }
  }

  async testContentScraping() {
    console.log('\n🕷️  Phase 2: Content Scraping with Quality Gates');

    if (!this.workflowData.jobId) {
      throw new Error('No job ID available for content scraping test');
    }

    try {
      let scrapingComplete = false;
      let attempts = 0;
      const maxAttempts = 30; // 30 attempts with 2-second intervals = 1 minute

      while (!scrapingComplete && attempts < maxAttempts) {
        const response = await axios.get(`${this.baseUrl}/api/scraping/job/${this.workflowData.jobId}`);
        
        const status = response.data.status;
        const progress = response.data.progress || {};

        console.log(`   📊 Scraping status: ${status} (${progress.completed || 0}/${progress.total || 0} URLs)`);

        if (status === 'completed' || status === 'failed') {
          scrapingComplete = true;
          
          if (status === 'completed') {
            this.assert(response.data.results, 'Completed job should have results');
            this.assert(response.data.results.urlsProcessed > 0, 'Should have processed URLs');
            this.assert(response.data.results.contentExtracted > 0, 'Should have extracted content');

            this.workflowData.scrapingResults = response.data.results;
            this.workflowData.urlsProcessed = response.data.results.urlsProcessed;
            this.workflowData.contentExtracted = response.data.results.contentExtracted;

            // Check quality gates
            if (response.data.qualityGates) {
              this.workflowData.qualityGates = response.data.qualityGates;
              console.log(`   🚪 Quality gates: ${JSON.stringify(response.data.qualityGates)}`);
            }

          } else {
            throw new Error(`Scraping job failed: ${response.data.error || 'Unknown error'}`);
          }
        }

        attempts++;
        if (!scrapingComplete && attempts < maxAttempts) {
          await this.sleep(2000); // Wait 2 seconds between checks
        }
      }

      if (!scrapingComplete) {
        throw new Error('Scraping job timeout - job did not complete within expected time');
      }

      this.recordTest('Content Scraping', true, 
        `Processed ${this.workflowData.urlsProcessed} URLs, extracted ${this.workflowData.contentExtracted} content items`);

      console.log(`   ✅ Content scraping completed`);
      console.log(`   📊 URLs processed: ${this.workflowData.urlsProcessed}`);
      console.log(`   📊 Content extracted: ${this.workflowData.contentExtracted}`);

    } catch (error) {
      this.recordTest('Content Scraping', false, error.message);
      throw new Error(`Content scraping failed: ${error.message}`);
    }
  }

  async testContentProcessing() {
    console.log('\n⚙️  Phase 3: Content Processing and Chunking');

    try {
      // Get processed content from the scraping job
      const response = await axios.get(`${this.baseUrl}/api/scraping/job/${this.workflowData.jobId}/content`);
      
      this.assert(response.status === 200, 'Content should be accessible');
      this.assert(response.data.content, 'Should return processed content');
      this.assert(Array.isArray(response.data.content), 'Content should be an array');

      const content = response.data.content;
      this.workflowData.processedContent = content;
      this.workflowData.contentChunks = content.length;

      // Validate content structure
      if (content.length > 0) {
        const sampleContent = content[0];
        this.assert(sampleContent.title, 'Content should have title');
        this.assert(sampleContent.text, 'Content should have text');
        this.assert(sampleContent.metadata, 'Content should have metadata');
        this.assert(sampleContent.chunks, 'Content should be chunked');
      }

      this.recordTest('Content Processing', true, 
        `Processed ${this.workflowData.contentChunks} content items with chunking`);

      console.log(`   ✅ Content processing completed`);
      console.log(`   📊 Content chunks: ${this.workflowData.contentChunks}`);

    } catch (error) {
      this.recordTest('Content Processing', false, error.message);
      throw new Error(`Content processing failed: ${error.message}`);
    }
  }

  async testEmbeddingAndVectorStorage() {
    console.log('\n🧮 Phase 4: Embedding Generation and Vector Storage');

    try {
      // Check if embeddings were generated during processing
      const response = await axios.get(`${this.baseUrl}/api/scraping/job/${this.workflowData.jobId}/embeddings`);
      
      this.assert(response.status === 200, 'Embeddings should be accessible');
      this.assert(response.data.embeddings, 'Should return embeddings data');

      const embeddingsData = response.data.embeddings;
      this.workflowData.embeddingsGenerated = embeddingsData.count || 0;
      this.workflowData.vectorsStored = embeddingsData.vectorsStored || 0;

      this.assert(this.workflowData.embeddingsGenerated > 0, 'Should have generated embeddings');
      this.assert(this.workflowData.vectorsStored > 0, 'Should have stored vectors');

      this.recordTest('Embedding and Vector Storage', true, 
        `Generated ${this.workflowData.embeddingsGenerated} embeddings, stored ${this.workflowData.vectorsStored} vectors`);

      console.log(`   ✅ Embedding and vector storage completed`);
      console.log(`   📊 Embeddings generated: ${this.workflowData.embeddingsGenerated}`);
      console.log(`   📊 Vectors stored: ${this.workflowData.vectorsStored}`);

    } catch (error) {
      this.recordTest('Embedding and Vector Storage', false, error.message);
      throw new Error(`Embedding and vector storage failed: ${error.message}`);
    }
  }

  async testRAGQueryProcessing() {
    console.log('\n🤖 Phase 5: RAG Query Processing');

    try {
      const testQueries = [
        'What are the main regulations in the DFSA rulebook?',
        'What are the compliance requirements for financial institutions?',
        'What are the reporting obligations?'
      ];

      const queryResults = [];

      for (const query of testQueries) {
        const startTime = performance.now();

        const response = await axios.post(`${this.baseUrl}/api/query`, {
          query,
          config: {
            retrieval: {
              maxResults: 5,
              minSimilarity: 0.7
            },
            generation: {
              maxTokens: 300,
              temperature: 0.3
            }
          }
        }, { timeout: this.timeout });

        const endTime = performance.now();
        const responseTime = endTime - startTime;

        this.assert(response.status === 200, 'Query should process successfully');
        this.assert(response.data.status === 'success', 'Query status should be success');
        this.assert(response.data.response, 'Should return response object');
        this.assert(response.data.response.answer, 'Response should contain answer');
        this.assert(response.data.response.sources, 'Response should contain sources');

        queryResults.push({
          query,
          responseTime,
          sourcesCount: response.data.response.sources.length,
          confidence: response.data.response.confidence || 0
        });

        console.log(`   ✅ Query processed: "${query.substring(0, 50)}..."`);
        console.log(`   ⏱️  Response time: ${responseTime.toFixed(2)}ms`);
        console.log(`   📊 Sources: ${response.data.response.sources.length}`);
      }

      this.workflowData.queryResults = queryResults;
      const avgResponseTime = queryResults.reduce((sum, r) => sum + r.responseTime, 0) / queryResults.length;
      const avgSourcesCount = queryResults.reduce((sum, r) => sum + r.sourcesCount, 0) / queryResults.length;

      this.recordTest('RAG Query Processing', true, 
        `Processed ${queryResults.length} queries, avg response time: ${avgResponseTime.toFixed(2)}ms, avg sources: ${avgSourcesCount.toFixed(1)}`);

      console.log(`   ✅ RAG query processing completed`);
      console.log(`   📊 Average response time: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`   📊 Average sources per query: ${avgSourcesCount.toFixed(1)}`);

    } catch (error) {
      this.recordTest('RAG Query Processing', false, error.message);
      throw new Error(`RAG query processing failed: ${error.message}`);
    }
  }

  async testQualityAssurance() {
    console.log('\n🔍 Phase 6: Quality Assurance Validation');

    try {
      // Get quality metrics for the scraping job
      const response = await axios.get(`${this.baseUrl}/api/scraping/job/${this.workflowData.jobId}/quality`);
      
      this.assert(response.status === 200, 'Quality metrics should be accessible');
      this.assert(response.data.quality, 'Should return quality data');

      const qualityData = response.data.quality;
      this.workflowData.qualityMetrics = qualityData;

      // Validate quality metrics
      this.assert(qualityData.contentQuality >= 0.7, 'Content quality should be above threshold');
      this.assert(qualityData.extractionSuccess >= 0.8, 'Extraction success rate should be high');

      this.recordTest('Quality Assurance', true, 
        `Content quality: ${qualityData.contentQuality}, extraction success: ${qualityData.extractionSuccess}`);

      console.log(`   ✅ Quality assurance validation completed`);
      console.log(`   📊 Content quality: ${qualityData.contentQuality}`);
      console.log(`   📊 Extraction success: ${qualityData.extractionSuccess}`);

    } catch (error) {
      this.recordTest('Quality Assurance', false, error.message);
      throw new Error(`Quality assurance failed: ${error.message}`);
    }
  }

  async testManualConfirmationWorkflow() {
    console.log('\n✋ Phase 7: Manual Confirmation Workflow');

    try {
      // Get test scrape results for manual confirmation
      const response = await axios.get(`${this.baseUrl}/api/scraping/job/${this.workflowData.jobId}/test-results`);
      
      this.assert(response.status === 200, 'Test results should be accessible');
      this.assert(response.data.testResults, 'Should return test results');

      const testResults = response.data.testResults;
      this.workflowData.testResults = testResults;

      // Simulate manual approval
      const approvalResponse = await axios.post(
        `${this.baseUrl}/api/scraping/test/${this.workflowData.jobId}/approve`,
        {
          approved: true,
          feedback: 'End-to-end test approval',
          qualityScore: 0.9
        }
      );

      this.assert(approvalResponse.status === 200, 'Manual approval should succeed');
      this.assert(approvalResponse.data.status === 'approved', 'Should return approved status');

      this.recordTest('Manual Confirmation Workflow', true, 
        'Test results reviewed and approved successfully');

      console.log(`   ✅ Manual confirmation workflow completed`);
      console.log(`   📊 Test results approved with quality score: 0.9`);

    } catch (error) {
      this.recordTest('Manual Confirmation Workflow', false, error.message);
      throw new Error(`Manual confirmation workflow failed: ${error.message}`);
    }
  }

  // Helper methods
  assert(condition, message) {
    if (!condition) {
      throw new Error(`Assertion failed: ${message}`);
    }
  }

  recordTest(testName, passed, details) {
    this.testResults.push({
      name: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printWorkflowResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 END-TO-END WORKFLOW TEST RESULTS');
    console.log('='.repeat(60));

    const passed = this.testResults.filter(t => t.passed).length;
    const total = this.testResults.length;

    this.testResults.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.details}`);
    });

    console.log('\n📈 Workflow Data Summary:');
    console.log(`   🆔 Job ID: ${this.workflowData.jobId || 'N/A'}`);
    console.log(`   🔍 URLs Processed: ${this.workflowData.urlsProcessed || 0}`);
    console.log(`   📄 Content Extracted: ${this.workflowData.contentExtracted || 0}`);
    console.log(`   📦 Content Chunks: ${this.workflowData.contentChunks || 0}`);
    console.log(`   🧮 Embeddings Generated: ${this.workflowData.embeddingsGenerated || 0}`);
    console.log(`   💾 Vectors Stored: ${this.workflowData.vectorsStored || 0}`);
    
    if (this.workflowData.queryResults) {
      const avgResponseTime = this.workflowData.queryResults.reduce((sum, r) => sum + r.responseTime, 0) / this.workflowData.queryResults.length;
      console.log(`   🤖 Queries Processed: ${this.workflowData.queryResults.length}`);
      console.log(`   ⏱️  Average Query Response Time: ${avgResponseTime.toFixed(2)}ms`);
    }

    console.log('\n' + '-'.repeat(60));
    console.log(`📊 Summary: ${passed}/${total} workflow phases passed`);
    
    if (passed === total) {
      console.log('🎉 Complete end-to-end workflow successful! System is fully integrated and operational.');
    } else {
      console.log('⚠️  Some workflow phases failed. Please review and fix issues.');
    }
    
    console.log('='.repeat(60));
  }
}

// Run the end-to-end workflow test
async function runEndToEndWorkflowTest() {
  const workflowTest = new EndToEndWorkflowTest();
  await workflowTest.runEndToEndTest();
}

// Export for use in other test files
module.exports = {
  EndToEndWorkflowTest,
  runEndToEndWorkflowTest
};

// Run test if this file is executed directly
if (require.main === module) {
  runEndToEndWorkflowTest().catch(error => {
    console.error('End-to-end workflow test failed:', error);
    process.exit(1);
  });
}