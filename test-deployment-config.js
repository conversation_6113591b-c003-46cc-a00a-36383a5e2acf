/**
 * Test script for Task 18: Deployment Configuration and Documentation
 * 
 * This script tests the deployment configuration components to ensure they work properly.
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

function runTest(testName, testFunction) {
  try {
    console.log(`\n🧪 Testing: ${testName}`);
    testFunction();
    console.log(`✅ PASSED: ${testName}`);
    testResults.passed++;
    testResults.tests.push({ name: testName, status: 'PASSED' });
  } catch (error) {
    console.log(`❌ FAILED: ${testName}`);
    console.log(`   Error: ${error.message}`);
    testResults.failed++;
    testResults.tests.push({ name: testName, status: 'FAILED', error: error.message });
  }
}

// Test 1: Dockerfile exists and has multi-stage builds
runTest('Dockerfile Multi-Stage Build Structure', () => {
  const dockerfilePath = 'Dockerfile';
  if (!fs.existsSync(dockerfilePath)) {
    throw new Error('Dockerfile not found');
  }
  
  const dockerfileContent = fs.readFileSync(dockerfilePath, 'utf8');
  
  // Check for multi-stage build stages
  const requiredStages = ['base', 'dependencies', 'build', 'development', 'production'];
  requiredStages.forEach(stage => {
    if (!dockerfileContent.includes(`FROM`) || !dockerfileContent.includes(`AS ${stage}`)) {
      throw new Error(`Missing stage: ${stage}`);
    }
  });
  
  // Check for essential Dockerfile elements
  const requiredElements = ['WORKDIR', 'COPY', 'RUN', 'EXPOSE', 'CMD'];
  requiredElements.forEach(element => {
    if (!dockerfileContent.includes(element)) {
      throw new Error(`Missing Dockerfile element: ${element}`);
    }
  });
});

// Test 2: Docker Compose files exist and are valid
runTest('Docker Compose Configuration', () => {
  const composeFiles = ['docker-compose.yml', 'docker-compose.prod.yml'];
  
  composeFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      throw new Error(`${file} not found`);
    }
    
    const composeContent = fs.readFileSync(file, 'utf8');
    const composeConfig = yaml.load(composeContent);
    
    // Check for required services
    if (!composeConfig.services) {
      throw new Error(`${file}: No services defined`);
    }
    
    // Check for app service
    if (!composeConfig.services.app && !composeConfig.services.redis) {
      throw new Error(`${file}: Missing required services`);
    }
  });
});

// Test 3: Environment files exist and have required variables
runTest('Environment Configuration Files', () => {
  const envFiles = ['.env.example', '.env.development', '.env.production'];
  
  envFiles.forEach(file => {
    if (!fs.existsSync(file)) {
      throw new Error(`${file} not found`);
    }
    
    const envContent = fs.readFileSync(file, 'utf8');
    
    // Check for essential environment variables
    const requiredVars = ['NODE_ENV', 'PORT', 'DATABASE_URL', 'REDIS_URL'];
    requiredVars.forEach(varName => {
      if (!envContent.includes(varName)) {
        throw new Error(`${file}: Missing environment variable ${varName}`);
      }
    });
  });
});

// Test 4: AWS CloudFormation template is valid YAML
runTest('AWS CloudFormation Template', () => {
  const cfnPath = 'aws/cloudformation.yaml';
  if (!fs.existsSync(cfnPath)) {
    throw new Error('CloudFormation template not found');
  }
  
  const cfnContent = fs.readFileSync(cfnPath, 'utf8');
  
  // Check for basic CloudFormation structure without parsing YAML
  // (CloudFormation intrinsic functions like !Sub cause issues with standard YAML parsers)
  const requiredSections = ['AWSTemplateFormatVersion', 'Description:', 'Parameters:', 'Resources:', 'Outputs:'];
  requiredSections.forEach(section => {
    if (!cfnContent.includes(section)) {
      throw new Error(`CloudFormation template missing section: ${section}`);
    }
  });
  
  // Check for essential resources by searching content
  const requiredResources = ['AWS::EC2::VPC', 'AWS::RDS::DBInstance', 'AWS::ECS::Cluster', 'AWS::ElasticLoadBalancingV2::LoadBalancer'];
  requiredResources.forEach(resource => {
    if (!cfnContent.includes(resource)) {
      throw new Error(`CloudFormation template missing resource type: ${resource}`);
    }
  });
  
  // Check for CloudFormation intrinsic functions
  const intrinsicFunctions = ['!Ref', '!Sub', '!GetAtt'];
  const hasIntrinsicFunctions = intrinsicFunctions.some(func => cfnContent.includes(func));
  if (!hasIntrinsicFunctions) {
    throw new Error('CloudFormation template missing intrinsic functions');
  }
});

// Test 5: Railway configuration is valid JSON
runTest('Railway Deployment Configuration', () => {
  const railwayPath = 'railway.json';
  if (!fs.existsSync(railwayPath)) {
    throw new Error('railway.json not found');
  }
  
  const railwayContent = fs.readFileSync(railwayPath, 'utf8');
  const railwayConfig = JSON.parse(railwayContent);
  
  // Check for required Railway configuration
  if (!railwayConfig.build || !railwayConfig.deploy) {
    throw new Error('Railway config missing build or deploy section');
  }
  
  if (!railwayConfig.build.builder || !railwayConfig.deploy.startCommand) {
    throw new Error('Railway config missing essential build/deploy settings');
  }
});

// Test 6: Render configuration is valid YAML
runTest('Render Deployment Configuration', () => {
  const renderPath = 'render.yaml';
  if (!fs.existsSync(renderPath)) {
    throw new Error('render.yaml not found');
  }
  
  const renderContent = fs.readFileSync(renderPath, 'utf8');
  const renderConfig = yaml.load(renderContent);
  
  // Check for required Render configuration
  if (!renderConfig.services || !Array.isArray(renderConfig.services)) {
    throw new Error('Render config missing services array');
  }
  
  // Check for web service
  const webService = renderConfig.services.find(service => service.type === 'web');
  if (!webService) {
    throw new Error('Render config missing web service');
  }
});

// Test 7: GitHub Actions workflow is valid YAML
runTest('GitHub Actions CI/CD Workflow', () => {
  const workflowPath = '.github/workflows/ci-cd.yml';
  if (!fs.existsSync(workflowPath)) {
    throw new Error('GitHub Actions workflow not found');
  }
  
  const workflowContent = fs.readFileSync(workflowPath, 'utf8');
  const workflowConfig = yaml.load(workflowContent);
  
  // Check for required workflow sections
  if (!workflowConfig.name || !workflowConfig.on || !workflowConfig.jobs) {
    throw new Error('GitHub Actions workflow missing required sections');
  }
  
  // Check for essential jobs
  const requiredJobs = ['test', 'build'];
  requiredJobs.forEach(job => {
    if (!workflowConfig.jobs[job]) {
      throw new Error(`GitHub Actions workflow missing job: ${job}`);
    }
  });
});

// Test 8: Backup scripts exist and are executable
runTest('Backup and Recovery Scripts', () => {
  const backupScripts = [
    'scripts/backup-database.sh',
    'scripts/export-vectors.js',
    'scripts/restore-vectors.js'
  ];
  
  backupScripts.forEach(script => {
    if (!fs.existsSync(script)) {
      throw new Error(`Backup script not found: ${script}`);
    }
    
    const scriptContent = fs.readFileSync(script, 'utf8');
    
    // Check for essential script elements
    if (script.endsWith('.sh')) {
      if (!scriptContent.includes('#!/bin/bash')) {
        throw new Error(`${script}: Missing shebang`);
      }
      if (!scriptContent.includes('set -e')) {
        throw new Error(`${script}: Missing error handling`);
      }
    } else if (script.endsWith('.js')) {
      if (!scriptContent.includes('require(') && !scriptContent.includes('import ')) {
        throw new Error(`${script}: Missing module imports`);
      }
    }
  });
});

// Test 9: Documentation files exist and have content
runTest('Documentation Completeness', () => {
  const docFiles = [
    'docs/setup.md',
    'docs/api.md'
  ];
  
  docFiles.forEach(docFile => {
    if (!fs.existsSync(docFile)) {
      throw new Error(`Documentation file not found: ${docFile}`);
    }
    
    const docContent = fs.readFileSync(docFile, 'utf8');
    
    // Check for minimum content length
    if (docContent.length < 1000) {
      throw new Error(`${docFile}: Documentation appears incomplete (too short)`);
    }
    
    // Check for essential documentation sections
    if (docFile.includes('setup.md')) {
      const requiredSections = ['Prerequisites', 'Local Development', 'Deployment', 'Environment'];
      requiredSections.forEach(section => {
        if (!docContent.includes(section)) {
          throw new Error(`${docFile}: Missing section ${section}`);
        }
      });
    }
    
    if (docFile.includes('api.md')) {
      const requiredSections = ['Authentication', 'Error Handling', 'API'];
      requiredSections.forEach(section => {
        if (!docContent.includes(section)) {
          throw new Error(`${docFile}: Missing section ${section}`);
        }
      });
    }
  });
});

// Test 10: Template files exist and are generalized
runTest('RAG System Template Generalization', () => {
  const templateFiles = [
    'rag-system-template/DEPLOYMENT_GUIDE.md',
    'rag-system-template/Dockerfile',
    'rag-system-template/docker-compose.prod.yml',
    'rag-system-template/railway.json',
    'rag-system-template/render.yaml',
    'rag-system-template/aws/cloudformation.yaml',
    'rag-system-template/aws/deploy.sh',
    'rag-system-template/scripts/backup-database.sh',
    'rag-system-template/docs/api.md'
  ];
  
  templateFiles.forEach(templateFile => {
    if (!fs.existsSync(templateFile)) {
      throw new Error(`Template file not found: ${templateFile}`);
    }
    
    const templateContent = fs.readFileSync(templateFile, 'utf8');
    
    // Check that DFSA-specific content has been generalized
    const dfsaSpecificTerms = ['dfsa-rag', 'DFSA', 'Dubai Financial Services'];
    const hasSpecificTerms = dfsaSpecificTerms.some(term => 
      templateContent.toLowerCase().includes(term.toLowerCase())
    );
    
    if (hasSpecificTerms && !templateFile.includes('DEPLOYMENT_GUIDE.md')) {
      // DEPLOYMENT_GUIDE.md may contain examples, so we're more lenient
      console.warn(`Warning: ${templateFile} may contain DFSA-specific content`);
    }
  });
});

// Run all tests and display results
console.log('\n' + '='.repeat(60));
console.log('🧪 DEPLOYMENT CONFIGURATION TEST RESULTS');
console.log('='.repeat(60));

console.log(`\n📊 Test Summary:`);
console.log(`   ✅ Passed: ${testResults.passed}`);
console.log(`   ❌ Failed: ${testResults.failed}`);
console.log(`   📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

if (testResults.failed > 0) {
  console.log(`\n❌ Failed Tests:`);
  testResults.tests
    .filter(test => test.status === 'FAILED')
    .forEach(test => {
      console.log(`   • ${test.name}: ${test.error}`);
    });
}

console.log(`\n✅ Passed Tests:`);
testResults.tests
  .filter(test => test.status === 'PASSED')
  .forEach(test => {
    console.log(`   • ${test.name}`);
  });

// Overall result
if (testResults.failed === 0) {
  console.log(`\n🎉 ALL TESTS PASSED! Deployment configuration is ready.`);
  process.exit(0);
} else {
  console.log(`\n⚠️  Some tests failed. Please fix the issues before proceeding.`);
  process.exit(1);
}