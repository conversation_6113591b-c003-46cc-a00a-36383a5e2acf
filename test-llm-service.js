#!/usr/bin/env node

/**
 * LLM Service Test
 * 
 * Validates the implementation of the multi-LLM response generation system
 */

const fs = require('fs');
const path = require('path');

// Test configuration
const testConfig = {
  verbose: true,
  testFiles: [
    'src/llm/llm-provider.ts',
    'src/llm/llm-service.ts',
    'src/llm/providers/openai-provider.ts',
    'src/llm/providers/anthropic-provider.ts',
    'src/llm/providers/local-model-provider.ts',
    'src/llm/response-generator.ts',
    'src/llm/llm-provider-factory.ts',
    'src/llm/index.ts'
  ],
  examples: [
    'examples/llm-service-example.ts'
  ]
};

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  details: []
};

function logTest(name, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    if (testConfig.verbose) {
      console.log(`✅ ${name}`);
    }
  } else {
    testResults.failed++;
    console.log(`❌ ${name}`);
    if (details) {
      console.log(`   ${details}`);
    }
  }
  testResults.details.push({ name, passed, details });
}

function testFileExists(filePath) {
  const exists = fs.existsSync(filePath);
  logTest(`File exists: ${filePath}`, exists);
  return exists;
}

function testFileContent(filePath, requiredPatterns) {
  if (!fs.existsSync(filePath)) {
    logTest(`Content test: ${filePath}`, false, 'File does not exist');
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  let allPatternsFound = true;
  const missingPatterns = [];

  for (const pattern of requiredPatterns) {
    const regex = new RegExp(pattern, 'i');
    if (!regex.test(content)) {
      allPatternsFound = false;
      missingPatterns.push(pattern);
    }
  }

  logTest(
    `Content patterns: ${path.basename(filePath)}`,
    allPatternsFound,
    missingPatterns.length > 0 ? `Missing: ${missingPatterns.join(', ')}` : ''
  );

  return allPatternsFound;
}

function testLLMProviderInterface() {
  console.log('\n🧪 Testing LLM Provider Interface...');
  
  const interfacePatterns = [
    'interface LLMProviderConfig',
    'abstract class LLMProvider',
    'abstract readonly name',
    'abstract readonly models',
    'abstract isAvailable\\(\\)',
    'abstract generateResponse\\(',
    'abstract countTokens\\(',
    'abstract estimateCost\\(',
    'abstract getProviderInfo\\(',
    'protected abstract validateConfig\\('
  ];

  return testFileContent('src/llm/llm-provider.ts', interfacePatterns);
}

function testLLMService() {
  console.log('\n🧪 Testing LLM Service...');
  
  const servicePatterns = [
    'interface LLMServiceConfig',
    'interface LLMServiceMetrics',
    'interface LLMServiceStatus',
    'interface PromptTemplate',
    'interface ResponseQualityMetrics',
    'class LLMService extends EventEmitter',
    'async initialize\\(\\)',
    'async generateResponse\\(',
    'async getStatus\\(\\)',
    'getMetrics\\(\\)',
    'resetMetrics\\(\\)',
    'registerPromptTemplate\\(',
    'getPromptTemplate\\(',
    'renderPromptTemplate\\(',
    'async evaluateResponseQuality\\(',
    'clearCache\\(\\)',
    'private createProvider\\(',
    'private async executeWithRetry\\(',
    'private async processLLMResponse\\(',
    'private extractSourceCitations\\(',
    'private extractKeyPhrases\\(',
    'private extractRelevantExcerpt\\(',
    'private async assemblePrompt\\(',
    'private generateCacheKey\\(',
    'private updateMetrics\\(',
    'private registerDefaultPromptTemplates\\(\\)'
  ];

  return testFileContent('src/llm/llm-service.ts', servicePatterns);
}

function testOpenAIProvider() {
  console.log('\n🧪 Testing OpenAI Provider...');
  
  const providerPatterns = [
    'interface OpenAIProviderConfig',
    'class OpenAIProvider extends LLMProvider',
    'public readonly name = \'openai\'',
    'public readonly models',
    'private client: OpenAI',
    'private modelInfo',
    'private costPerToken',
    'async isAvailable\\(\\)',
    'async generateResponse\\(',
    'countTokens\\(',
    'estimateCost\\(',
    'getProviderInfo\\(\\)',
    'protected validateConfig\\(',
    'private isValidModel\\('
  ];

  return testFileContent('src/llm/providers/openai-provider.ts', providerPatterns);
}

function testAnthropicProvider() {
  console.log('\n🧪 Testing Anthropic Provider...');
  
  const providerPatterns = [
    'interface AnthropicProviderConfig',
    'class AnthropicProvider extends LLMProvider',
    'public readonly name = \'anthropic\'',
    'public readonly models',
    'private client: Anthropic',
    'private modelInfo',
    'private costPerToken',
    'async isAvailable\\(\\)',
    'async generateResponse\\(',
    'countTokens\\(',
    'estimateCost\\(',
    'getProviderInfo\\(\\)',
    'protected validateConfig\\(',
    'private isValidModel\\('
  ];

  return testFileContent('src/llm/providers/anthropic-provider.ts', providerPatterns);
}

function testLocalModelProvider() {
  console.log('\n🧪 Testing Local Model Provider...');
  
  const providerPatterns = [
    'interface LocalModelProviderConfig',
    'class LocalModelProvider extends LLMProvider',
    'public readonly name = \'local\'',
    'public readonly models',
    'private baseUrl',
    'private modelInfo',
    'async isAvailable\\(\\)',
    'async generateResponse\\(',
    'countTokens\\(',
    'estimateCost\\(',
    'getProviderInfo\\(\\)',
    'protected validateConfig\\(',
    'private isValidModel\\('
  ];

  return testFileContent('src/llm/providers/local-model-provider.ts', providerPatterns);
}

function testResponseGenerator() {
  console.log('\n🧪 Testing Response Generator...');
  
  const generatorPatterns = [
    'interface ResponseGeneratorConfig',
    'class ResponseGenerator implements IResponseGenerator',
    'private llmService',
    'private config',
    'async generateResponse\\(',
    'private parseContextString\\(',
    'private getDefaultConfig\\(',
    'private getAlternativePrompt\\('
  ];

  return testFileContent('src/llm/response-generator.ts', generatorPatterns);
}

function testLLMProviderFactory() {
  console.log('\n🧪 Testing LLM Provider Factory...');
  
  const factoryPatterns = [
    'interface ProviderRecommendation',
    'interface ProviderRequirements',
    'class LLMProviderFactory',
    'static create\\(',
    'static getSupportedProviders\\(',
    'static getProviderRequirements\\(',
    'static getRecommendedModels\\(',
    'static getProviderRecommendations\\(',
    'static getProviderComparison\\(',
    'static getMigrationGuide\\(',
    'static createDefaultConfig\\(',
    'private static validateConfig\\('
  ];

  return testFileContent('src/llm/llm-provider-factory.ts', factoryPatterns);
}

function testIndexExports() {
  console.log('\n🧪 Testing Index Exports...');
  
  const indexPatterns = [
    'export { LLMProvider, LLMProviderConfig',
    'export { LLMService, LLMServiceConfig',
    'export { ResponseGenerator, ResponseGeneratorConfig',
    'export { LLMProviderFactory',
    'export { OpenAIProvider, OpenAIProviderConfig',
    'export { AnthropicProvider, AnthropicProviderConfig',
    'export { LocalModelProvider, LocalModelProviderConfig',
    'export const DefaultLLMConfigs',
    'export const DefaultLLMServiceConfigs',
    'export const DefaultPromptTemplates'
  ];

  return testFileContent('src/llm/index.ts', indexPatterns);
}

function testExampleImplementation() {
  console.log('\n🧪 Testing Example Implementation...');
  
  const examplePatterns = [
    'import { LLMService',
    'import { RAGContext',
    'async function llmServiceExample\\(',
    'async function providerComparisonExample\\(',
    'async function promptTemplateExample\\(',
    'await llmService.initialize\\(',
    'await llmService.generateResponse\\(',
    'LLMProviderFactory.getProviderRecommendations\\(',
    'LLMProviderFactory.getProviderComparison\\(',
    'LLMProviderFactory.getMigrationGuide\\(',
    'llmService.renderPromptTemplate\\('
  ];

  return testFileContent('examples/llm-service-example.ts', examplePatterns);
}

function testErrorHandling() {
  console.log('\n🧪 Testing Error Handling...');
  
  const errorPatterns = [
    'try {',
    'catch \\(error\\)',
    'throw new Error\\(',
    'retryable',
    'executeWithRetry',
    'maxRetries'
  ];

  const files = [
    'src/llm/llm-service.ts',
    'src/llm/providers/openai-provider.ts',
    'src/llm/providers/anthropic-provider.ts',
    'src/llm/providers/local-model-provider.ts'
  ];

  let allPassed = true;
  for (const file of files) {
    const passed = testFileContent(file, errorPatterns);
    if (!passed) {
      allPassed = false;
    }
  }

  return allPassed;
}

function testFallbackMechanisms() {
  console.log('\n🧪 Testing Fallback Mechanisms...');
  
  const fallbackPatterns = [
    'fallbackProviders',
    'enableFallback',
    'this.metrics.fallbackCount\\+\\+',
    'this.emit\\(\'fallback\'',
    'for \\(const fallbackProvider of this.fallbackProviders\\)'
  ];

  return testFileContent('src/llm/llm-service.ts', fallbackPatterns);
}

function testResponseQualityValidation() {
  console.log('\n🧪 Testing Response Quality Validation...');
  
  const qualityPatterns = [
    'interface ResponseQualityMetrics',
    'async evaluateResponseQuality\\(',
    'calculateContentOverlap',
    'calculateQueryRelevance',
    'calculateCoherence',
    'calculateCompleteness',
    'minConfidence',
    'validateQuality',
    'retryWithDifferentPrompts'
  ];

  return testFileContent('src/llm/llm-service.ts', qualityPatterns);
}

function testModelSwitching() {
  console.log('\n🧪 Testing Model Switching Capabilities...');
  
  const switchingPatterns = [
    'primaryProvider',
    'fallbackProviders',
    'createProvider\\(',
    'switch \\(config.name\\)',
    'switch \\(config.provider\\)',
    'getProviderRecommendations',
    'getMigrationGuide'
  ];

  const files = [
    'src/llm/llm-service.ts',
    'src/llm/llm-provider-factory.ts'
  ];

  let allPassed = true;
  for (const file of files) {
    const passed = testFileContent(file, switchingPatterns);
    if (!passed) {
      allPassed = false;
    }
  }

  return allPassed;
}

function testPromptTemplating() {
  console.log('\n🧪 Testing Prompt Templating...');
  
  const templatePatterns = [
    'interface PromptTemplate',
    'private promptTemplates: Map<string, PromptTemplate>',
    'registerPromptTemplate\\(',
    'getPromptTemplate\\(',
    'renderPromptTemplate\\(',
    'registerDefaultPromptTemplates\\(',
    'DefaultPromptTemplates'
  ];

  const files = [
    'src/llm/llm-service.ts',
    'src/llm/index.ts'
  ];

  let allPassed = true;
  for (const file of files) {
    const passed = testFileContent(file, templatePatterns);
    if (!passed) {
      allPassed = false;
    }
  }

  return allPassed;
}

function testCaching() {
  console.log('\n🧪 Testing Caching Mechanisms...');
  
  const cachePatterns = [
    'enableCache',
    'cacheTTL',
    'maxCacheSize',
    'private responseCache',
    'generateCacheKey\\(',
    'clearCache\\(',
    'this.responseCache.set\\(',
    'this.responseCache.get\\(',
    'this.responseCache.delete\\(',
    'this.responseCache.size'
  ];

  return testFileContent('src/llm/llm-service.ts', cachePatterns);
}

function testMetricsTracking() {
  console.log('\n🧪 Testing Metrics Tracking...');
  
  const metricsPatterns = [
    'interface LLMServiceMetrics',
    'private metrics',
    'getMetrics\\(',
    'resetMetrics\\(',
    'updateMetrics\\(',
    'totalRequests',
    'successfulRequests',
    'failedRequests',
    'fallbackCount',
    'averageLatency',
    'averageConfidence',
    'providerUsage',
    'modelUsage',
    'errorsByType',
    'tokenUsage',
    'costEstimate'
  ];

  return testFileContent('src/llm/llm-service.ts', metricsPatterns);
}

function testSourceCitation() {
  console.log('\n🧪 Testing Source Citation...');
  
  const citationPatterns = [
    'interface SourceCitation',
    'extractSourceCitations\\(',
    'extractKeyPhrases\\(',
    'extractRelevantExcerpt\\(',
    'sources: SourceCitation\\[\\]',
    'relevance'
  ];

  const files = [
    'src/llm/llm-service.ts',
    'src/rag/types.ts'
  ];

  let allPassed = true;
  for (const file of files) {
    const passed = testFileContent(file, citationPatterns);
    if (!passed) {
      allPassed = false;
    }
  }

  return allPassed;
}

// Main test execution
async function runTests() {
  console.log('🚀 Multi-LLM Response Generation System Test Suite\n');

  // Test file existence
  console.log('📁 Testing File Structure...');
  for (const file of [...testConfig.testFiles, ...testConfig.examples]) {
    testFileExists(file);
  }

  // Test implementations
  testLLMProviderInterface();
  testLLMService();
  testOpenAIProvider();
  testAnthropicProvider();
  testLocalModelProvider();
  testResponseGenerator();
  testLLMProviderFactory();
  testIndexExports();
  testExampleImplementation();

  // Test specific functionality
  testErrorHandling();
  testFallbackMechanisms();
  testResponseQualityValidation();
  testModelSwitching();
  testPromptTemplating();
  testCaching();
  testMetricsTracking();
  testSourceCitation();

  // Generate summary
  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);

  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.details
      .filter(test => !test.passed)
      .forEach(test => {
        console.log(`  - ${test.name}`);
        if (test.details) {
          console.log(`    ${test.details}`);
        }
      });
  }

  // Overall assessment
  const successRate = (testResults.passed / testResults.total) * 100;
  console.log('\n🎯 Overall Assessment');
  console.log('=====================');
  
  if (successRate >= 95) {
    console.log('🌟 EXCELLENT: Multi-LLM response generation system is comprehensive and well-implemented');
  } else if (successRate >= 85) {
    console.log('✅ GOOD: Multi-LLM response generation system is solid with minor gaps');
  } else if (successRate >= 70) {
    console.log('⚠️ FAIR: Multi-LLM response generation system needs some improvements');
  } else {
    console.log('❌ POOR: Multi-LLM response generation system has significant issues');
  }

  // Feature completeness check
  console.log('\n🔍 Feature Completeness Check');
  console.log('=============================');
  
  const features = [
    { name: 'Multi-Provider Support', test: () => testResults.details.find(t => t.name.includes('OpenAI Provider'))?.passed && testResults.details.find(t => t.name.includes('Anthropic Provider'))?.passed },
    { name: 'Response Generation', test: () => testResults.details.find(t => t.name.includes('Response Generator'))?.passed },
    { name: 'Provider Factory', test: () => testResults.details.find(t => t.name.includes('LLM Provider Factory'))?.passed },
    { name: 'Error Handling', test: () => testResults.details.find(t => t.name.includes('Error Handling'))?.passed },
    { name: 'Fallback Mechanisms', test: () => testResults.details.find(t => t.name.includes('Fallback Mechanisms'))?.passed },
    { name: 'Response Quality Validation', test: () => testResults.details.find(t => t.name.includes('Response Quality Validation'))?.passed },
    { name: 'Model Switching', test: () => testResults.details.find(t => t.name.includes('Model Switching'))?.passed },
    { name: 'Prompt Templating', test: () => testResults.details.find(t => t.name.includes('Prompt Templating'))?.passed },
    { name: 'Caching', test: () => testResults.details.find(t => t.name.includes('Caching'))?.passed },
    { name: 'Metrics Tracking', test: () => testResults.details.find(t => t.name.includes('Metrics Tracking'))?.passed },
    { name: 'Source Citation', test: () => testResults.details.find(t => t.name.includes('Source Citation'))?.passed }
  ];

  features.forEach(feature => {
    const status = feature.test() ? '✅' : '❌';
    console.log(`${status} ${feature.name}`);
  });

  const completedFeatures = features.filter(f => f.test()).length;
  console.log(`\nFeature Completion: ${completedFeatures}/${features.length} (${((completedFeatures / features.length) * 100).toFixed(1)}%)`);

  return {
    success: successRate >= 85,
    successRate,
    totalTests: testResults.total,
    passedTests: testResults.passed,
    failedTests: testResults.failed,
    features: completedFeatures,
    totalFeatures: features.length
  };
}

// Run tests
runTests().then(result => {
  if (result.success) {
    console.log('\n🎉 Multi-LLM Response Generation System Test: PASSED');
    process.exit(0);
  } else {
    console.log('\n💥 Multi-LLM Response Generation System Test: FAILED');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});