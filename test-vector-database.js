/**
 * Vector Database Integration Test
 * 
 * Comprehensive test to validate the vector database implementation
 * including factory patterns, configurations, and utility functions.
 */

const fs = require('fs');
const path = require('path');

// Test configuration
const testConfig = {
  verbose: true,
  testFiles: [
    'src/vector-database/vector-store.ts',
    'src/vector-database/providers/pinecone-store.ts',
    'src/vector-database/providers/weaviate-store.ts',
    'src/vector-database/vector-store-factory.ts',
    'src/vector-database/vector-store-service.ts',
    'src/vector-database/index.ts'
  ],
  examples: [
    'examples/vector-database/vector-store-example.ts'
  ],
  documentation: [
    'src/vector-database/README.md'
  ]
};

// Test results tracking
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  details: []
};

function logTest(name, passed, details = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    if (testConfig.verbose) {
      console.log(`✅ ${name}`);
    }
  } else {
    testResults.failed++;
    console.log(`❌ ${name}`);
    if (details) {
      console.log(`   ${details}`);
    }
  }
  testResults.details.push({ name, passed, details });
}

function testFileExists(filePath) {
  const exists = fs.existsSync(filePath);
  logTest(`File exists: ${filePath}`, exists);
  return exists;
}

function testFileContent(filePath, requiredPatterns) {
  if (!fs.existsSync(filePath)) {
    logTest(`Content test: ${filePath}`, false, 'File does not exist');
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  let allPatternsFound = true;
  const missingPatterns = [];

  for (const pattern of requiredPatterns) {
    const regex = new RegExp(pattern, 'i');
    if (!regex.test(content)) {
      allPatternsFound = false;
      missingPatterns.push(pattern);
    }
  }

  logTest(
    `Content patterns: ${path.basename(filePath)}`,
    allPatternsFound,
    missingPatterns.length > 0 ? `Missing: ${missingPatterns.join(', ')}` : ''
  );

  return allPatternsFound;
}

function testVectorStoreInterface() {
  console.log('\n🧪 Testing Vector Store Interface...');
  
  const interfacePatterns = [
    'interface VectorRecord',
    'interface VectorSearchQuery',
    'interface VectorSearchResponse',
    'interface VectorStoreConfig',
    'interface VectorStoreHealth',
    'abstract class VectorStore',
    'abstract initialize\\(\\)',
    'abstract search\\(',
    'abstract upsert\\(',
    'abstract fetch\\(',
    'abstract delete\\(',
    'abstract getHealth\\(\\)',
    'abstract getStats\\(\\)',
    'class VectorStoreError',
    'class VectorStoreConnectionError',
    'class VectorStoreIndexError',
    'class VectorStoreQueryError'
  ];

  return testFileContent('src/vector-database/vector-store.ts', interfacePatterns);
}

function testPineconeImplementation() {
  console.log('\n🧪 Testing Pinecone Implementation...');
  
  const pineconePatterns = [
    'class PineconeVectorStore extends VectorStore',
    'interface PineconeConfig',
    'from \'@pinecone-database/pinecone\'',
    'async initialize\\(\\)',
    'async search\\(',
    'async upsert\\(',
    'async fetch\\(',
    'async delete\\(',
    'async getHealth\\(\\)',
    'async createIndex\\(\\)',
    'async deleteIndex\\(\\)',
    'serverless',
    'pod',
    'configureIndex',
    'describeIndex'
  ];

  return testFileContent('src/vector-database/providers/pinecone-store.ts', pineconePatterns);
}

function testWeaviateImplementation() {
  console.log('\n🧪 Testing Weaviate Implementation...');
  
  const weaviatePatterns = [
    'class WeaviateVectorStore extends VectorStore',
    'interface WeaviateConfig',
    'from \'weaviate-client\'',
    'async initialize\\(\\)',
    'async search\\(',
    'async upsert\\(',
    'async fetch\\(',
    'async delete\\(',
    'async getHealth\\(\\)',
    'async createIndex\\(\\)',
    'async deleteIndex\\(\\)',
    'WeaviateClient',
    'nearVector',
    'nearObject',
    'buildWhereFilter',
    'createCollection'
  ];

  return testFileContent('src/vector-database/providers/weaviate-store.ts', weaviatePatterns);
}

function testVectorStoreFactory() {
  console.log('\n🧪 Testing Vector Store Factory...');
  
  const factoryPatterns = [
    'class VectorStoreFactory',
    'static create\\(',
    'validateConfig',
    'validatePineconeConfig',
    'validateWeaviateConfig',
    'getRecommendedConfig',
    'getPineconeRecommendations',
    'getWeaviateRecommendations',
    'getProviderComparison',
    'getMigrationGuide',
    'case \'pinecone\'',
    'case \'weaviate\'',
    'semantic-search',
    'high-performance',
    'multimodal'
  ];

  return testFileContent('src/vector-database/vector-store-factory.ts', factoryPatterns);
}

function testVectorStoreService() {
  console.log('\n🧪 Testing Vector Store Service...');
  
  const servicePatterns = [
    'class VectorStoreService extends EventEmitter',
    'interface VectorStoreServiceConfig',
    'interface VectorStoreMetrics',
    'async initialize\\(\\)',
    'async upsert\\(',
    'async search\\(',
    'async fetch\\(',
    'async delete\\(',
    'async getHealth\\(\\)',
    'getMetrics\\(\\)',
    'getStatus\\(\\)',
    'executeWithRetry',
    'startHealthMonitoring',
    'recordMetrics',
    'fallbackStore',
    'healthCheck',
    'performance',
    'retry'
  ];

  return testFileContent('src/vector-database/vector-store-service.ts', servicePatterns);
}

function testIndexExports() {
  console.log('\n🧪 Testing Index Exports...');
  
  const indexPatterns = [
    'export.*VectorStore',
    'export.*VectorRecord',
    'export.*VectorSearchQuery',
    'export.*VectorSearchResponse',
    'export.*PineconeVectorStore',
    'export.*WeaviateVectorStore',
    'export.*VectorStoreFactory',
    'export.*VectorStoreService',
    'export.*VectorUtils',
    'export.*DefaultVectorConfigs',
    'export.*DefaultServiceConfigs',
    'cosineSimilarity',
    'euclideanDistance',
    'normalizeVector',
    'validateVector',
    'generateRandomVector',
    'batchVectors',
    'pineconeSemanticSearch',
    'weaviateSemanticSearch',
    'production',
    'development',
    'testing'
  ];

  return testFileContent('src/vector-database/index.ts', indexPatterns);
}

function testExamples() {
  console.log('\n🧪 Testing Examples...');
  
  const examplePatterns = [
    'basicPineconeExample',
    'basicWeaviateExample',
    'advancedServiceExample',
    'batchOperationsExample',
    'vectorUtilitiesExample',
    'VectorStoreFactory.create',
    'VectorStoreService',
    'generateRandomVector',
    'cosineSimilarity',
    'euclideanDistance',
    'normalizeVector',
    'validateVector',
    'batchVectors',
    'DefaultVectorConfigs',
    'DefaultServiceConfigs',
    'await store.initialize',
    'await store.upsert',
    'await store.search',
    'await service.initialize',
    'service.on\\(',
    'console.log'
  ];

  return testFileContent('examples/vector-database/vector-store-example.ts', examplePatterns);
}

function testDocumentation() {
  console.log('\n🧪 Testing Documentation...');
  
  const docPatterns = [
    '# Vector Database Integration',
    '## Features',
    '## Quick Start',
    '## Providers',
    '### Pinecone',
    '### Weaviate',
    '## API Reference',
    '## Configuration',
    '## Utilities',
    '## Error Handling',
    '## Performance Optimization',
    '## Migration Guide',
    '## Troubleshooting',
    '## Examples',
    'Multi-Provider Support',
    'Health Monitoring',
    'Automatic Fallbacks',
    'Performance Metrics',
    'Retry Logic',
    'Batch Operations',
    'Type Safety',
    'VectorStoreFactory',
    'VectorStoreService',
    'VectorUtils',
    'DefaultVectorConfigs',
    'DefaultServiceConfigs'
  ];

  return testFileContent('src/vector-database/README.md', docPatterns);
}

function testUtilityFunctions() {
  console.log('\n🧪 Testing Utility Functions...');
  
  // Test VectorUtils patterns in index.ts
  const utilityPatterns = [
    'VectorUtils.*=.*{',
    'cosineSimilarity.*function',
    'euclideanDistance.*function',
    'normalizeVector.*function',
    'similarityToDistance.*function',
    'distanceToSimilarity.*function',
    'validateVector.*function',
    'generateRandomVector.*function',
    'batchVectors.*function',
    'Math.sqrt',
    'Array.isArray',
    'typeof.*===.*number',
    'Math.random',
    'slice\\(',
    'reduce\\(',
    'map\\(',
    'every\\(',
    'Array.from'
  ];

  return testFileContent('src/vector-database/index.ts', utilityPatterns);
}

function testConfigurationStructure() {
  console.log('\n🧪 Testing Configuration Structure...');
  
  // Test default configurations
  const configPatterns = [
    'DefaultVectorConfigs.*=.*{',
    'pineconeSemanticSearch.*:.*{',
    'pineconeHighPerformance.*:.*{',
    'weaviateSemanticSearch.*:.*{',
    'weaviateMultimodal.*:.*{',
    'DefaultServiceConfigs.*=.*{',
    'production.*:.*{',
    'development.*:.*{',
    'testing.*:.*{',
    'provider.*:.*pinecone',
    'provider.*:.*weaviate',
    'dimension.*:.*1536',
    'metric.*:.*cosine',
    'healthCheck.*:.*{',
    'performance.*:.*{',
    'retry.*:.*{',
    'cloud.*:.*aws',
    'region.*:.*us-west-2',
    'scheme.*:.*https',
    'className.*:.*Document'
  ];

  return testFileContent('src/vector-database/index.ts', configPatterns);
}

function testTypeDefinitions() {
  console.log('\n🧪 Testing Type Definitions...');
  
  let allTypesValid = true;
  
  // Test interface completeness in vector-store.ts
  const typePatterns = [
    'interface VectorRecord.*{[^}]*id.*:.*string',
    'interface VectorRecord.*{[^}]*vector.*:.*number\\[\\]',
    'interface VectorRecord.*{[^}]*metadata.*\\?.*:.*Record',
    'interface VectorSearchQuery.*{[^}]*topK.*:.*number',
    'interface VectorSearchQuery.*{[^}]*vector.*\\?.*:.*number\\[\\]',
    'interface VectorSearchResponse.*{[^}]*matches.*:.*VectorSearchResult\\[\\]',
    'interface VectorStoreConfig.*{[^}]*provider.*:.*pinecone.*\\|.*weaviate',
    'interface VectorStoreConfig.*{[^}]*dimension.*:.*number',
    'interface VectorStoreHealth.*{[^}]*isHealthy.*:.*boolean',
    'interface VectorStoreStats.*{[^}]*totalRecords.*:.*number'
  ];

  for (const pattern of typePatterns) {
    const result = testFileContent('src/vector-database/vector-store.ts', [pattern]);
    if (!result) {
      allTypesValid = false;
    }
  }

  return allTypesValid;
}

function testErrorHandling() {
  console.log('\n🧪 Testing Error Handling...');
  
  const errorPatterns = [
    'class.*Error extends.*Error',
    'VectorStoreError',
    'VectorStoreConnectionError',
    'VectorStoreIndexError',
    'VectorStoreQueryError',
    'throw new.*Error',
    'catch.*error',
    'try.*{',
    'finally.*{',
    'error.message',
    'instanceof.*Error'
  ];

  let errorHandlingValid = true;
  
  // Test error handling in all main files
  const filesToTest = [
    'src/vector-database/vector-store.ts',
    'src/vector-database/providers/pinecone-store.ts',
    'src/vector-database/providers/weaviate-store.ts',
    'src/vector-database/vector-store-service.ts'
  ];

  for (const file of filesToTest) {
    const result = testFileContent(file, errorPatterns.slice(0, 5)); // Test subset for each file
    if (!result) {
      errorHandlingValid = false;
    }
  }

  return errorHandlingValid;
}

// Main test execution
async function runTests() {
  console.log('🚀 Vector Database Integration Test Suite\n');

  // Test file existence
  console.log('📁 Testing File Structure...');
  for (const file of [...testConfig.testFiles, ...testConfig.examples, ...testConfig.documentation]) {
    testFileExists(file);
  }

  // Test implementations
  testVectorStoreInterface();
  testPineconeImplementation();
  testWeaviateImplementation();
  testVectorStoreFactory();
  testVectorStoreService();
  testIndexExports();

  // Test examples and documentation
  testExamples();
  testDocumentation();

  // Test specific functionality
  testUtilityFunctions();
  testConfigurationStructure();
  testTypeDefinitions();
  testErrorHandling();

  // Generate summary
  console.log('\n📊 Test Summary');
  console.log('================');
  console.log(`Total Tests: ${testResults.total}`);
  console.log(`Passed: ${testResults.passed} ✅`);
  console.log(`Failed: ${testResults.failed} ❌`);
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);

  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.details
      .filter(test => !test.passed)
      .forEach(test => {
        console.log(`  - ${test.name}`);
        if (test.details) {
          console.log(`    ${test.details}`);
        }
      });
  }

  // Overall assessment
  const successRate = (testResults.passed / testResults.total) * 100;
  console.log('\n🎯 Overall Assessment');
  console.log('=====================');
  
  if (successRate >= 95) {
    console.log('🌟 EXCELLENT: Vector database integration is comprehensive and well-implemented');
  } else if (successRate >= 85) {
    console.log('✅ GOOD: Vector database integration is solid with minor gaps');
  } else if (successRate >= 70) {
    console.log('⚠️ FAIR: Vector database integration needs some improvements');
  } else {
    console.log('❌ POOR: Vector database integration has significant issues');
  }

  // Feature completeness check
  console.log('\n🔍 Feature Completeness Check');
  console.log('=============================');
  
  const features = [
    { name: 'Multi-Provider Support', test: () => testResults.details.find(t => t.name.includes('Pinecone Implementation'))?.passed && testResults.details.find(t => t.name.includes('Weaviate Implementation'))?.passed },
    { name: 'Factory Pattern', test: () => testResults.details.find(t => t.name.includes('Vector Store Factory'))?.passed },
    { name: 'Service Layer', test: () => testResults.details.find(t => t.name.includes('Vector Store Service'))?.passed },
    { name: 'Utility Functions', test: () => testResults.details.find(t => t.name.includes('Utility Functions'))?.passed },
    { name: 'Error Handling', test: () => testResults.details.find(t => t.name.includes('Error Handling'))?.passed },
    { name: 'Type Safety', test: () => testResults.details.find(t => t.name.includes('Type Definitions'))?.passed },
    { name: 'Configuration', test: () => testResults.details.find(t => t.name.includes('Configuration Structure'))?.passed },
    { name: 'Examples', test: () => testResults.details.find(t => t.name.includes('Examples'))?.passed },
    { name: 'Documentation', test: () => testResults.details.find(t => t.name.includes('Documentation'))?.passed }
  ];

  features.forEach(feature => {
    const status = feature.test() ? '✅' : '❌';
    console.log(`${status} ${feature.name}`);
  });

  const completedFeatures = features.filter(f => f.test()).length;
  console.log(`\nFeature Completion: ${completedFeatures}/${features.length} (${((completedFeatures / features.length) * 100).toFixed(1)}%)`);

  return {
    success: successRate >= 85,
    successRate,
    totalTests: testResults.total,
    passedTests: testResults.passed,
    failedTests: testResults.failed,
    features: completedFeatures,
    totalFeatures: features.length
  };
}

// Run tests
runTests().then(result => {
  if (result.success) {
    console.log('\n🎉 Vector Database Integration Test: PASSED');
    process.exit(0);
  } else {
    console.log('\n💥 Vector Database Integration Test: FAILED');
    process.exit(1);
  }
}).catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});