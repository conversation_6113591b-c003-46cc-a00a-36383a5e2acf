# Application
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/dfsa_rag
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# Redis
REDIS_URL=redis://localhost:6379

# AI Providers
OPENAI_API_KEY=********************************************************************************************************************************************************************
ANTHROPIC_API_KEY=************************************************************************************************************
COHERE_API_KEY=AjRCna4m9TgGKrOg7e10URLRAUjWyX18ApLbfs2t

# Vector Database
PINECONE_API_KEY=pcsk_6Efjzc_NevUqNP1bt818P8MgRdtDs94YmEJwZKfZvJV6ADy8b52awueWjYw5Cc7Q9PwAtF
PINECONE_ENVIRONMENT=https://rag-system-6dzs5y1.svc.aped-4627-b74a.pinecone.io
PINECONE_INDEX=rag-system

# Web Scraping - Brightdata
BRIGHTDATA_HOST=brd.superproxy.io
BRIGHTDATA_PORT=9222
BRIGHTDATA_USERNAME=brd-customer-hl_2573e6f5-zone-scraping_browser3
BRIGHTDATA_PASSWORD=3kzbxparb7pe
BRIGHTDATA_BROWSER_URL=wss://brd-customer-hl_2573e6f5-zone-scraping_browser3:<EMAIL>:9222
BRIGHTDATA_SELENIUM_URL=https://brd-customer-hl_2573e6f5-zone-scraping_browser3:<EMAIL>:9515
SCRAPING_RATE_LIMIT=2
SCRAPING_CONCURRENT_JOBS=1

# Security
JWT_SECRET=dev_secret_change_in_production
CORS_ORIGINS=http://localhost:3001,http://localhost:3000

# Deployment
DOCKER_REGISTRY=localhost
TAG=dev
DATA_PATH=./data