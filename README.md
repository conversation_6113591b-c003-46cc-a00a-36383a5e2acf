# DFSA Rulebook RAG System

A comprehensive, production-ready system for scraping the DFSA (Dubai Financial Services Authority) rulebook and providing intelligent querying through a RAG (Retrieval-Augmented Generation) pipeline.

## 🌟 Features

- **Advanced Web Scraping**: Brightdata Browser API integration with quality assurance gates
- **Multi-Provider AI**: OpenAI, Anthropic, Cohere, and local model support
- **Intelligent Processing**: Advanced text processing, chunking, and metadata extraction
- **Vector Storage**: Pinecone and Weaviate integration with automatic fallbacks
- **Real-time Dashboard**: Socket.IO-powered monitoring interface
- **Quality Evaluation**: Comprehensive evaluation tools with ground truth management
- **Production Ready**: Docker deployment, CI/CD, monitoring, and backup procedures

## 🚀 Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd dfsa-rag-system
   npm install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Run Integration Tests**
   ```bash
   npm run test:integration
   ```

5. **Access Dashboard**
   Open http://localhost:3000 in your browser

## 📚 Documentation

### 🔧 [Complete Documentation](docs/README.md)
Comprehensive documentation including setup, API reference, and user guides.

### 🏗️ [System Architecture](docs/development/architecture.md)
Detailed system architecture and component interactions.

### 🚀 [Deployment Guide](docs/setup/deployment.md)
Production deployment options for Docker, Railway, Render, and AWS.

### 🔌 [API Reference](docs/api/README.md)
Complete API documentation with examples and authentication.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Scraping  │───▶│  Content        │───▶│   Embedding     │
│   (Brightdata)  │    │  Processing     │    │   Generation    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────▼─────────┐
│   Dashboard     │◄───│  RAG Pipeline   │◄───│  Vector Storage │
│  (React/Socket) │    │  Orchestrator   │    │ (Pinecone/Weaviate)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────▼─────────┐
                       │   LLM Providers   │
                       │ (OpenAI/Anthropic)│
                       └───────────────────┘
```

## ⚙️ Configuration

### Required Environment Variables

```bash
# Brightdata Configuration
BRIGHTDATA_USERNAME=your-brightdata-username
BRIGHTDATA_PASSWORD=your-brightdata-password

# Database Configuration
DATABASE_URL=your-postgresql-url
REDIS_URL=your-redis-url

# AI Provider Configuration
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
COHERE_API_KEY=your-cohere-api-key

# Vector Database Configuration
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX_NAME=your-index-name

# Optional: Weaviate Configuration
WEAVIATE_URL=your-weaviate-url
WEAVIATE_API_KEY=your-weaviate-api-key
```

### External Services Required

1. **[Brightdata Account](https://brightdata.com/)**: Web scraping capabilities
2. **PostgreSQL Database**: Content and metadata storage
3. **Redis Instance**: Job queue management
4. **AI Provider Accounts**: OpenAI, Anthropic, and/or Cohere
5. **Vector Database**: Pinecone or Weaviate account

## 🔌 Key API Endpoints

### Scraping Operations
- `POST /api/scraping/start` - Start new scraping job
- `GET /api/scraping/job/:jobId` - Monitor job progress
- `POST /api/scraping/job/:jobId/cancel` - Cancel running job

### RAG Queries
- `POST /api/query` - Process natural language queries
- `GET /api/query/conversation/:id` - Retrieve conversation history
- `GET /api/query/metrics` - Get query performance metrics

### System Management
- `GET /api/config` - System configuration
- `GET /api/health` - System health status
- `POST /api/evaluation/runs` - Run quality evaluations

## 🧪 Testing

```bash
# Run all tests
npm test

# Run integration tests
npm run test:integration

# Run system validation
npm run validate:system

# Run end-to-end tests
npm run test:e2e

# Run load performance tests
npm run test:load
```

## 🚀 Deployment

### Docker (Recommended)
```bash
# Development
docker-compose up

# Production
docker-compose -f docker-compose.prod.yml up -d
```

### Cloud Platforms
- **Railway**: Use included `railway.json`
- **Render**: Deploy with `render.yaml`
- **AWS**: CloudFormation template in `aws/` directory

### Manual Deployment
```bash
npm run build
npm start
```

## 📊 Performance Benchmarks

- **Query Response Time**: < 3 seconds average
- **Concurrent Users**: 5+ simultaneous queries
- **Memory Usage**: < 100MB increase under load
- **Error Rate**: < 5% under normal load
- **Throughput**: > 1 request/second sustained

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes with tests
4. Run the test suite (`npm test`)
5. Submit a pull request

See [Contributing Guide](docs/development/contributing.md) for detailed guidelines.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [Complete Documentation](docs/README.md)
- **API Reference**: [API Documentation](docs/api/README.md)
- **Troubleshooting**: [Common Issues](docs/development/troubleshooting.md)
- **Architecture**: [System Architecture](docs/development/architecture.md)

---

**Status**: Production Ready ✅  
**Version**: 1.0.0  
**Last Updated**: January 2025