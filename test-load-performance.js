/**
 * Load Testing and Performance Validation
 * 
 * Tests system performance under realistic DFSA content volumes
 * Validates scalability and resource usage
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

class LoadTestSuite {
  constructor() {
    this.testResults = [];
    this.performanceMetrics = [];
    this.baseUrl = 'http://localhost:3000';
    this.concurrentUsers = 5;
    this.testDuration = 30000; // 30 seconds
  }

  async runLoadTests() {
    console.log('⚡ Starting Load Testing and Performance Validation');
    console.log('=' .repeat(60));

    try {
      // Test 1: Concurrent Query Processing
      await this.testConcurrentQueries();

      // Test 2: Bulk Content Processing
      await this.testBulkContentProcessing();

      // Test 3: Memory Usage Under Load
      await this.testMemoryUsage();

      // Test 4: Response Time Consistency
      await this.testResponseTimeConsistency();

      // Test 5: Error Rate Under Load
      await this.testErrorRateUnderLoad();

      // Print results
      this.printLoadTestResults();

    } catch (error) {
      console.error('❌ Load testing failed:', error.message);
      process.exit(1);
    }
  }

  async testConcurrentQueries() {
    console.log('\n🔄 Test 1: Concurrent Query Processing');

    const queries = [
      'What are the main regulations in the DFSA rulebook?',
      'What are the compliance requirements for financial institutions?',
      'What are the reporting obligations under DFSA rules?',
      'What are the capital adequacy requirements?',
      'What are the risk management guidelines?'
    ];

    const startTime = performance.now();
    const promises = [];

    // Create concurrent requests
    for (let i = 0; i < this.concurrentUsers; i++) {
      const query = queries[i % queries.length];
      const promise = this.sendQuery(query, `concurrent-${i}`);
      promises.push(promise);
    }

    try {
      const results = await Promise.allSettled(promises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      const avgResponseTime = totalTime / this.concurrentUsers;

      this.recordPerformanceMetric('Concurrent Queries', {
        totalRequests: this.concurrentUsers,
        successful,
        failed,
        totalTime,
        avgResponseTime,
        throughput: (this.concurrentUsers / totalTime) * 1000 // requests per second
      });

      console.log(`   ✅ Processed ${successful}/${this.concurrentUsers} concurrent queries`);
      console.log(`   📊 Average response time: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`   🚀 Throughput: ${((this.concurrentUsers / totalTime) * 1000).toFixed(2)} req/sec`);

    } catch (error) {
      console.log(`   ❌ Concurrent query test failed: ${error.message}`);
    }
  }

  async testBulkContentProcessing() {
    console.log('\n📦 Test 2: Bulk Content Processing');

    try {
      // Simulate processing multiple documents
      const documents = this.generateTestDocuments(10);
      const startTime = performance.now();

      const processingPromises = documents.map((doc, index) => 
        this.processDocument(doc, `bulk-${index}`)
      );

      const results = await Promise.allSettled(processingPromises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      this.recordPerformanceMetric('Bulk Processing', {
        totalDocuments: documents.length,
        successful,
        failed,
        totalTime,
        avgProcessingTime: totalTime / documents.length
      });

      console.log(`   ✅ Processed ${successful}/${documents.length} documents`);
      console.log(`   📊 Average processing time: ${(totalTime / documents.length).toFixed(2)}ms`);

    } catch (error) {
      console.log(`   ❌ Bulk processing test failed: ${error.message}`);
    }
  }

  async testMemoryUsage() {
    console.log('\n💾 Test 3: Memory Usage Under Load');

    try {
      const initialMemory = process.memoryUsage();
      console.log(`   📊 Initial memory usage: ${(initialMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);

      // Generate load for memory testing
      const promises = [];
      for (let i = 0; i < 20; i++) {
        promises.push(this.sendQuery(`Memory test query ${i}`, `memory-${i}`));
      }

      await Promise.allSettled(promises);

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = (finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024;

      console.log(`   📊 Final memory usage: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
      console.log(`   📈 Memory increase: ${memoryIncrease.toFixed(2)} MB`);

      this.recordPerformanceMetric('Memory Usage', {
        initialMemory: initialMemory.heapUsed,
        finalMemory: finalMemory.heapUsed,
        memoryIncrease,
        memoryIncreasePercent: (memoryIncrease / (initialMemory.heapUsed / 1024 / 1024)) * 100
      });

      if (memoryIncrease < 100) { // Less than 100MB increase is acceptable
        console.log('   ✅ Memory usage within acceptable limits');
      } else {
        console.log('   ⚠️  High memory usage detected');
      }

    } catch (error) {
      console.log(`   ❌ Memory usage test failed: ${error.message}`);
    }
  }

  async testResponseTimeConsistency() {
    console.log('\n⏱️  Test 4: Response Time Consistency');

    const responseTimes = [];
    const testQuery = 'What are the key compliance requirements?';

    try {
      // Send multiple requests to measure consistency
      for (let i = 0; i < 10; i++) {
        const startTime = performance.now();
        await this.sendQuery(testQuery, `consistency-${i}`);
        const endTime = performance.now();
        responseTimes.push(endTime - startTime);
        
        // Small delay between requests
        await this.sleep(100);
      }

      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const minResponseTime = Math.min(...responseTimes);
      const maxResponseTime = Math.max(...responseTimes);
      const stdDev = this.calculateStandardDeviation(responseTimes);

      this.recordPerformanceMetric('Response Time Consistency', {
        avgResponseTime,
        minResponseTime,
        maxResponseTime,
        standardDeviation: stdDev,
        consistencyScore: (1 - (stdDev / avgResponseTime)) * 100
      });

      console.log(`   📊 Average response time: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`   📊 Min/Max response time: ${minResponseTime.toFixed(2)}ms / ${maxResponseTime.toFixed(2)}ms`);
      console.log(`   📊 Standard deviation: ${stdDev.toFixed(2)}ms`);
      console.log(`   📊 Consistency score: ${((1 - (stdDev / avgResponseTime)) * 100).toFixed(2)}%`);

    } catch (error) {
      console.log(`   ❌ Response time consistency test failed: ${error.message}`);
    }
  }

  async testErrorRateUnderLoad() {
    console.log('\n🚨 Test 5: Error Rate Under Load');

    const totalRequests = 50;
    const promises = [];
    let successCount = 0;
    let errorCount = 0;

    try {
      // Generate high load
      for (let i = 0; i < totalRequests; i++) {
        const promise = this.sendQuery(`Load test query ${i}`, `load-${i}`)
          .then(() => successCount++)
          .catch(() => errorCount++);
        promises.push(promise);
      }

      await Promise.allSettled(promises);

      const errorRate = (errorCount / totalRequests) * 100;

      this.recordPerformanceMetric('Error Rate Under Load', {
        totalRequests,
        successCount,
        errorCount,
        errorRate,
        successRate: (successCount / totalRequests) * 100
      });

      console.log(`   📊 Total requests: ${totalRequests}`);
      console.log(`   ✅ Successful: ${successCount}`);
      console.log(`   ❌ Errors: ${errorCount}`);
      console.log(`   📊 Error rate: ${errorRate.toFixed(2)}%`);

      if (errorRate < 5) {
        console.log('   ✅ Error rate within acceptable limits');
      } else {
        console.log('   ⚠️  High error rate detected');
      }

    } catch (error) {
      console.log(`   ❌ Error rate test failed: ${error.message}`);
    }
  }

  // Helper methods
  async sendQuery(query, testId) {
    try {
      const response = await axios.post(`${this.baseUrl}/api/query`, {
        query,
        config: {
          retrieval: { maxResults: 3 },
          generation: { maxTokens: 200 }
        }
      }, {
        timeout: 10000,
        headers: { 'X-Test-ID': testId }
      });

      return response.data;
    } catch (error) {
      throw new Error(`Query failed: ${error.message}`);
    }
  }

  async processDocument(document, testId) {
    // Simulate document processing
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) { // 90% success rate
          resolve({ processed: true, testId });
        } else {
          reject(new Error('Processing failed'));
        }
      }, Math.random() * 1000 + 500); // 500-1500ms processing time
    });
  }

  generateTestDocuments(count) {
    const documents = [];
    for (let i = 0; i < count; i++) {
      documents.push({
        id: `doc-${i}`,
        title: `Test Document ${i}`,
        content: `This is test content for document ${i}. `.repeat(100),
        metadata: {
          source: 'test',
          timestamp: new Date().toISOString()
        }
      });
    }
    return documents;
  }

  calculateStandardDeviation(values) {
    const avg = values.reduce((a, b) => a + b, 0) / values.length;
    const squareDiffs = values.map(value => Math.pow(value - avg, 2));
    const avgSquareDiff = squareDiffs.reduce((a, b) => a + b, 0) / squareDiffs.length;
    return Math.sqrt(avgSquareDiff);
  }

  recordPerformanceMetric(testName, metrics) {
    this.performanceMetrics.push({
      testName,
      metrics,
      timestamp: new Date().toISOString()
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printLoadTestResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 LOAD TEST RESULTS');
    console.log('='.repeat(60));

    this.performanceMetrics.forEach(metric => {
      console.log(`\n📈 ${metric.testName}:`);
      Object.entries(metric.metrics).forEach(([key, value]) => {
        if (typeof value === 'number') {
          console.log(`   ${key}: ${value.toFixed(2)}`);
        } else {
          console.log(`   ${key}: ${value}`);
        }
      });
    });

    console.log('\n' + '-'.repeat(60));
    console.log('🎯 Performance Summary:');
    
    // Calculate overall performance score
    let totalScore = 0;
    let scoreCount = 0;

    this.performanceMetrics.forEach(metric => {
      if (metric.testName === 'Response Time Consistency' && metric.metrics.consistencyScore) {
        totalScore += metric.metrics.consistencyScore;
        scoreCount++;
      }
      if (metric.testName === 'Error Rate Under Load' && metric.metrics.successRate) {
        totalScore += metric.metrics.successRate;
        scoreCount++;
      }
    });

    const overallScore = scoreCount > 0 ? totalScore / scoreCount : 0;
    console.log(`📊 Overall Performance Score: ${overallScore.toFixed(2)}%`);

    if (overallScore > 80) {
      console.log('🎉 Excellent performance! System ready for production load.');
    } else if (overallScore > 60) {
      console.log('✅ Good performance. Minor optimizations recommended.');
    } else {
      console.log('⚠️  Performance issues detected. Optimization required.');
    }

    console.log('='.repeat(60));
  }
}

// Run the load tests
async function runLoadTests() {
  const testSuite = new LoadTestSuite();
  await testSuite.runLoadTests();
}

// Export for use in other test files
module.exports = {
  LoadTestSuite,
  runLoadTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runLoadTests().catch(error => {
    console.error('Load testing failed:', error);
    process.exit(1);
  });
}