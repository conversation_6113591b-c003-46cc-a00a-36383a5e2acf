/**
 * Simple Node.js server to test the system without TypeScript compilation
 * This bypasses TypeScript errors and gets the basic system running
 */

// Load environment variables
require('dotenv').config();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const { createServer } = require('http');
const path = require('path');

// Create Express app
const app = express();
const httpServer = createServer(app);

// Configuration from environment
const config = {
  port: process.env.PORT || 3001,
  env: process.env.NODE_ENV || 'development',
  corsOrigin: process.env.CORS_ORIGINS || 'http://localhost:3001,http://localhost:3000'
};

// Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
      fontSrc: ["'self'", 'https://fonts.gstatic.com'],
      imgSrc: ["'self'", 'data:', 'blob:'],
      connectSrc: ["'self'", 'ws:', 'wss:']
    }
  }
}));

app.use(cors({ 
  origin: config.corsOrigin.split(','), 
  credentials: true 
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: config.env,
    version: '1.0.0',
    services: {
      redis: process.env.REDIS_URL ? 'configured' : 'not configured',
      openai: process.env.OPENAI_API_KEY ? 'configured' : 'not configured',
      pinecone: process.env.PINECONE_API_KEY ? 'configured' : 'not configured',
      supabase: process.env.SUPABASE_URL ? 'configured' : 'not configured',
      brightdata: process.env.BRIGHTDATA_USERNAME ? 'configured' : 'not configured'
    }
  });
});

// Basic API info endpoint
app.get('/api', (req, res) => {
  res.json({
    message: 'DFSA Rulebook RAG System API',
    version: '1.0.0',
    status: 'running',
    endpoints: {
      health: '/health',
      api: '/api',
      config: '/api/config'
    }
  });
});

// Simple configuration endpoints
app.get('/api/config', (req, res) => {
  res.json({
    status: 'success',
    config: {
      environment: config.env,
      port: config.port,
      services: {
        redis: !!process.env.REDIS_URL,
        openai: !!process.env.OPENAI_API_KEY,
        pinecone: !!process.env.PINECONE_API_KEY,
        supabase: !!process.env.SUPABASE_URL,
        brightdata: !!process.env.BRIGHTDATA_USERNAME
      }
    }
  });
});

app.post('/api/config', (req, res) => {
  const { key, value } = req.body;
  
  if (!key || value === undefined) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Key and value are required'
    });
  }

  // In a real implementation, this would save to database
  // For now, just return success
  res.json({
    status: 'success',
    message: `Configuration ${key} set successfully`,
    key,
    value
  });
});

app.get('/api/config/:key', (req, res) => {
  const { key } = req.params;
  
  // In a real implementation, this would read from database
  // For now, return a mock value
  res.json({
    status: 'success',
    key,
    value: `mock-value-for-${key}`
  });
});

// Mock scraping endpoints
app.post('/api/scraping/start', (req, res) => {
  const jobId = `job-${Date.now()}`;
  
  res.json({
    status: 'started',
    jobId,
    message: 'Scraping job started successfully (mock)',
    testResult: {
      approved: true,
      qualityScore: 0.9
    }
  });
});

app.get('/api/scraping/job/:jobId', (req, res) => {
  const { jobId } = req.params;
  
  res.json({
    jobId,
    status: 'completed',
    progress: {
      completed: 5,
      total: 5,
      percentage: 100
    },
    results: {
      urlsProcessed: 5,
      contentExtracted: 5,
      qualityScore: 0.9
    }
  });
});

app.get('/api/scraping/stats', (req, res) => {
  res.json({
    status: 'success',
    stats: {
      activeJobs: 0,
      completedJobs: 1,
      failedJobs: 0,
      totalUrls: 5
    }
  });
});

// Mock query endpoints
app.post('/api/query', (req, res) => {
  const { query } = req.body;
  
  if (!query) {
    return res.status(400).json({
      error: 'Bad Request',
      message: 'Query is required'
    });
  }

  res.json({
    status: 'success',
    response: {
      query,
      answer: `This is a mock response to your query: "${query}". In a real implementation, this would use the RAG pipeline to provide contextual answers based on scraped DFSA content.`,
      sources: [
        {
          title: 'DFSA General Module',
          url: 'https://www.dfsa.ae/rulebook/general-module',
          relevance: 0.9
        },
        {
          title: 'DFSA Conduct Rules',
          url: 'https://www.dfsa.ae/rulebook/conduct-rules',
          relevance: 0.8
        }
      ],
      confidence: 0.85,
      provider: 'openai'
    }
  });
});

app.post('/api/query/conversation', (req, res) => {
  const conversationId = `conv-${Date.now()}`;
  
  res.json({
    status: 'success',
    conversationId,
    title: req.body.title || 'New Conversation'
  });
});

app.get('/api/query/conversation/:conversationId', (req, res) => {
  const { conversationId } = req.params;
  
  res.json({
    status: 'success',
    conversationId,
    messages: [
      {
        id: 'msg-1',
        role: 'user',
        content: 'What are the main regulations?',
        timestamp: new Date().toISOString()
      },
      {
        id: 'msg-2',
        role: 'assistant',
        content: 'The main regulations include...',
        timestamp: new Date().toISOString()
      }
    ]
  });
});

app.get('/api/query/metrics', (req, res) => {
  res.json({
    status: 'success',
    metrics: {
      totalQueries: 10,
      averageResponseTime: 1500,
      successRate: 0.95
    }
  });
});

// 404 handler for API routes
app.use('/api/*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `API endpoint ${req.method} ${req.path} not found`
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: config.env === 'development' ? error.message : 'Something went wrong'
  });
});

// Start server
const server = httpServer.listen(config.port, () => {
  console.log(`🚀 DFSA Rulebook RAG System started on port ${config.port}`);
  console.log(`📊 Environment: ${config.env}`);
  console.log(`🏥 Health check: http://localhost:${config.port}/health`);
  console.log(`📚 API info: http://localhost:${config.port}/api`);
  console.log('');
  console.log('🔧 Service Status:');
  console.log(`   Redis: ${process.env.REDIS_URL ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   OpenAI: ${process.env.OPENAI_API_KEY ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   Pinecone: ${process.env.PINECONE_API_KEY ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   Supabase: ${process.env.SUPABASE_URL ? '✅ Configured' : '❌ Not configured'}`);
  console.log(`   Brightdata: ${process.env.BRIGHTDATA_USERNAME ? '✅ Configured' : '❌ Not configured'}`);
  console.log('');
  console.log('🧪 Ready for testing!');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
    process.exit(0);
  });
});

module.exports = app;