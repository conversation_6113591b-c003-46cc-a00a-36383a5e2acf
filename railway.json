{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "buildCommand": "npm run build"}, "deploy": {"restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10, "healthcheckPath": "/api/health", "healthcheckTimeout": 300, "numReplicas": 1, "sleepApplication": false, "startCommand": "node dist/index.js", "envVarGroups": [{"name": "database", "envVars": [{"name": "DATABASE_URL", "value": "${DATABASE_URL}"}]}, {"name": "ai-providers", "envVars": [{"name": "OPENAI_API_KEY", "value": "${OPENAI_API_KEY}"}, {"name": "ANTHROPIC_API_KEY", "value": "${ANTHROPIC_API_KEY}"}, {"name": "COHERE_API_KEY", "value": "${COHERE_API_KEY}"}]}, {"name": "vector-database", "envVars": [{"name": "PINECONE_API_KEY", "value": "${PINECONE_API_KEY}"}, {"name": "PINECONE_ENVIRONMENT", "value": "${PINECONE_ENVIRONMENT}"}, {"name": "PINECONE_INDEX", "value": "${PINECONE_INDEX}"}]}, {"name": "scraping", "envVars": [{"name": "BRIGHTDATA_API_KEY", "value": "${BRIGHTDATA_API_KEY}"}, {"name": "BRIGHTDATA_USERNAME", "value": "${BRIGHTDATA_USERNAME}"}, {"name": "BRIGHTDATA_PASSWORD", "value": "${BRIGHTDATA_PASSWORD}"}, {"name": "SCRAPING_RATE_LIMIT", "value": "10"}, {"name": "SCRAPING_CONCURRENT_JOBS", "value": "5"}]}, {"name": "application", "envVars": [{"name": "NODE_ENV", "value": "production"}, {"name": "PORT", "value": "3000"}, {"name": "LOG_LEVEL", "value": "info"}, {"name": "JWT_SECRET", "value": "${JWT_SECRET}"}, {"name": "CORS_ORIGINS", "value": "${CORS_ORIGINS}"}]}]}}