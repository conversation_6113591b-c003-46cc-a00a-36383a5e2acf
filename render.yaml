services:
  # Main application service
  - type: web
    name: dfsa-rag-app
    env: node
    buildCommand: npm ci && npm run build
    startCommand: node dist/index.js
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3000
      - key: DATABASE_URL
        fromDatabase:
          name: dfsa-rag-db
          property: connectionString
      - key: REDIS_URL
        fromService:
          name: dfsa-rag-redis
          type: redis
          property: connectionString
      - key: OPENAI_API_KEY
        sync: false
      - key: ANTHROPIC_API_KEY
        sync: false
      - key: PINECONE_API_KEY
        sync: false
      - key: PINECONE_ENVIRONMENT
        sync: false
      - key: PINECONE_INDEX
        value: dfsa-prod
      - key: BRIGHTDATA_API_KEY
        sync: false
      - key: JWT_SECRET
        generateValue: true
    autoDeploy: true
    disk:
      name: data
      mountPath: /app/data
      sizeGB: 10

  # Redis service
  - type: redis
    name: dfsa-rag-redis
    ipAllowList:
      - source: 0.0.0.0/0
        description: everywhere
    plan: starter

databases:
  - name: dfsa-rag-db
    databaseName: dfsa_rag
    user: dfsa_user
    plan: starter