-- Local database setup for DFSA Rulebook RAG System
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE content_status AS ENUM ('pending', 'scraped', 'processed', 'embedded', 'failed');
CREATE TYPE job_status AS ENUM ('pending', 'running', 'paused', 'completed', 'failed', 'cancelled');
CREATE TYPE url_status AS ENUM ('discovered', 'pending', 'scraped', 'failed', 'skipped');
CREATE TYPE content_type_enum AS ENUM ('html', 'pdf', 'text', 'table', 'image');

-- Scraped content table
CREATE TABLE scraped_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL UNIQUE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    content_type content_type_enum NOT NULL DEFAULT 'html',
    metadata JSONB DEFAULT '{}',
    quality_metrics JSONB DEFAULT '{}',
    scraped_at TIMESTAMPTZ DEFAULT NOW(),
    status content_status DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Scraping jobs table
CREATE TABLE scraping_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    urls JSONB NOT NULL DEFAULT '[]',
    status job_status DEFAULT 'pending',
    progress JSONB DEFAULT '{"totalUrls": 0, "completedUrls": 0, "failedUrls": 0, "percentage": 0}',
    config JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    errors JSONB DEFAULT '[]'
);

-- URLs tracking table
CREATE TABLE urls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL UNIQUE,
    status url_status DEFAULT 'discovered',
    discovered_at TIMESTAMPTZ DEFAULT NOW(),
    scraped_at TIMESTAMPTZ,
    job_id UUID REFERENCES scraping_jobs(id) ON DELETE SET NULL,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Document chunks table (for RAG) - without vector extension for local dev
CREATE TABLE document_chunks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_id UUID NOT NULL REFERENCES scraped_content(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    text TEXT NOT NULL,
    embedding TEXT, -- Store as JSON string for local dev
    metadata JSONB DEFAULT '{}',
    tokens INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(content_id, chunk_index)
);

-- Quality validations table
CREATE TABLE quality_validations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_id UUID NOT NULL REFERENCES scraped_content(id) ON DELETE CASCADE,
    validation_type TEXT NOT NULL,
    passed BOOLEAN NOT NULL,
    score DECIMAL(3,2) NOT NULL DEFAULT 0.0,
    issues JSONB DEFAULT '[]',
    validated_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- System configuration table
CREATE TABLE system_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key TEXT NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_scraped_content_url ON scraped_content(url);
CREATE INDEX idx_scraped_content_status ON scraped_content(status);
CREATE INDEX idx_scraped_content_scraped_at ON scraped_content(scraped_at);

CREATE INDEX idx_scraping_jobs_status ON scraping_jobs(status);
CREATE INDEX idx_scraping_jobs_created_at ON scraping_jobs(created_at);

CREATE INDEX idx_urls_url ON urls(url);
CREATE INDEX idx_urls_status ON urls(status);
CREATE INDEX idx_urls_job_id ON urls(job_id);

CREATE INDEX idx_document_chunks_content_id ON document_chunks(content_id);

CREATE INDEX idx_quality_validations_content_id ON quality_validations(content_id);
CREATE INDEX idx_quality_validations_validation_type ON quality_validations(validation_type);

CREATE INDEX idx_system_config_key ON system_config(key);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_scraped_content_updated_at BEFORE UPDATE ON scraped_content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_scraping_jobs_updated_at BEFORE UPDATE ON scraping_jobs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_urls_updated_at BEFORE UPDATE ON urls FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_document_chunks_updated_at BEFORE UPDATE ON document_chunks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_system_config_updated_at BEFORE UPDATE ON system_config FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default system configuration
INSERT INTO system_config (key, value, description) VALUES
('scraping.max_concurrent', '5', 'Maximum number of concurrent scraping operations'),
('scraping.default_delay', '1000', 'Default delay between scraping requests in milliseconds'),
('scraping.quality_gate_threshold', '0.8', 'Quality gate threshold for content validation'),
('rag.chunk_size', '1000', 'Default chunk size for document processing'),
('rag.chunk_overlap', '200', 'Default overlap between document chunks');