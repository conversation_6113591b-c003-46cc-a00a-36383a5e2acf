#!/usr/bin/env ts-node

/**
 * Scraping Orchestrator Test Script
 * Demonstrates the complete scraping workflow with job management
 */

import { ScrapingOrchestrator } from './src/scraping/scraping-orchestrator';
import { JobStatus } from './src/types';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testScrapingOrchestrator() {
  console.log('🚀 Testing DFSA Scraping Orchestrator\n');

  // Verify credentials are available
  if (!process.env.BRIGHTDATA_USERNAME || !process.env.BRIGHTDATA_PASSWORD) {
    console.error('❌ Missing Brightdata credentials in environment variables');
    process.exit(1);
  }

  // Initialize orchestrator
  const orchestrator = new ScrapingOrchestrator({
    brightdata: {
      username: process.env.BRIGHTDATA_USERNAME,
      password: process.env.BRIGHTDATA_PASSWORD,
      zone: process.env.BRIGHTDATA_ZONE || 'scraping_browser2',
      country: 'US'
    },
    scraping: {
      maxConcurrency: 2,
      delayBetweenRequests: 3000,
      retryAttempts: 2,
      qualityGateThreshold: 0.6,
      testScrapeSize: 3,
      timeout: 60000
    },
    jobManager: {
      concurrency: 1,
      retryAttempts: 2,
      retryDelay: 5000
    }
  });

  try {
    console.log('📋 Configuration:');
    console.log(`  Brightdata Zone: ${process.env.BRIGHTDATA_ZONE || 'scraping_browser2'}`);
    console.log(`  Max Concurrency: 2`);
    console.log(`  Quality Gate Threshold: 60%`);
    console.log(`  Test Scrape Size: 3 URLs`);
    console.log('');

    // Test 1: Start scraping workflow
    console.log('🧪 Test 1: Starting Complete Scraping Workflow');
    console.log('This will:');
    console.log('  • Discover URLs from DFSA base URL');
    console.log('  • Perform test scraping with quality assurance');
    console.log('  • Request manual confirmation');
    console.log('  • Create and queue full scraping job');
    console.log('');

    const baseUrl = 'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa';
    const scrapeConfig = {
      mode: 'comprehensive' as const,
      maxDepth: 2,
      delay: 2000,
      retryAttempts: 2,
      qualityGateInterval: 5,
      testScrapeSize: 3
    };

    console.log(`🔍 Starting workflow for: ${baseUrl}`);
    const workflowResult = await orchestrator.startScraping(baseUrl, scrapeConfig);

    if (!workflowResult.jobId) {
      console.log('❌ Workflow stopped - test scraping not approved');
      if (workflowResult.testResult) {
        console.log(`   Quality Score: ${(workflowResult.testResult.qualityScore * 100).toFixed(1)}%`);
        console.log(`   Recommendations: ${workflowResult.testResult.recommendations.length}`);
      }
      return;
    }

    console.log('✅ Workflow started successfully!');
    console.log(`   Job ID: ${workflowResult.jobId}`);
    if (workflowResult.testResult) {
      console.log(`   Test Quality Score: ${(workflowResult.testResult.qualityScore * 100).toFixed(1)}%`);
      console.log(`   Test URLs: ${workflowResult.testResult.results.length}`);
    }
    console.log('');

    // Test 2: Monitor job progress
    console.log('🧪 Test 2: Monitoring Job Progress');
    const jobId = workflowResult.jobId;
    let jobCompleted = false;
    let monitoringCount = 0;
    const maxMonitoring = 30; // Maximum monitoring cycles

    while (!jobCompleted && monitoringCount < maxMonitoring) {
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
      
      const job = await orchestrator.getJobStatus(jobId);
      if (!job) {
        console.log('❌ Job not found');
        break;
      }

      console.log(`📊 Job Status Update (${monitoringCount + 1}/${maxMonitoring}):`);
      console.log(`   Status: ${job.status}`);
      console.log(`   Progress: ${job.progress.completedUrls}/${job.progress.totalUrls} (${job.progress.percentage}%)`);
      console.log(`   Failed URLs: ${job.progress.failedUrls}`);
      console.log(`   Errors: ${job.errors.length}`);
      
      if (job.progress.currentUrl) {
        console.log(`   Current URL: ${job.progress.currentUrl}`);
      }

      if (job.status === JobStatus.COMPLETED || job.status === JobStatus.FAILED) {
        jobCompleted = true;
        
        if (job.status === JobStatus.COMPLETED) {
          console.log('✅ Job completed successfully!');
          console.log(`   Duration: ${job.completedAt ? Math.round((job.completedAt.getTime() - job.createdAt.getTime()) / 1000) : 'N/A'}s`);
        } else {
          console.log('❌ Job failed');
          if (job.errors.length > 0) {
            console.log('   Recent errors:');
            job.errors.slice(-3).forEach(error => {
              console.log(`     • ${error.url}: ${error.error}`);
            });
          }
        }
      }
      
      console.log('');
      monitoringCount++;
    }

    if (!jobCompleted) {
      console.log('⏰ Monitoring timeout reached, job may still be running');
    }

    // Test 3: Queue statistics
    console.log('🧪 Test 3: Queue Statistics');
    const queueStats = await orchestrator.getQueueStats();
    console.log('📊 Queue Status:');
    console.log(`   Waiting: ${queueStats.waiting}`);
    console.log(`   Active: ${queueStats.active}`);
    console.log(`   Completed: ${queueStats.completed}`);
    console.log(`   Failed: ${queueStats.failed}`);
    console.log(`   Delayed: ${queueStats.delayed}`);
    console.log('');

    // Test 4: Job management operations
    console.log('🧪 Test 4: Job Management Operations');
    
    // Get all jobs
    const allJobs = await orchestrator.getJobs();
    console.log(`📋 Total Jobs: ${allJobs.length}`);
    
    allJobs.forEach((job, index) => {
      console.log(`   ${index + 1}. ${job.id} - ${job.status} (${job.progress.percentage}%)`);
    });

    // Get jobs by status
    const runningJobs = await orchestrator.getJobs(JobStatus.RUNNING);
    const completedJobs = await orchestrator.getJobs(JobStatus.COMPLETED);
    const failedJobs = await orchestrator.getJobs(JobStatus.FAILED);

    console.log(`📊 Jobs by Status:`);
    console.log(`   Running: ${runningJobs.length}`);
    console.log(`   Completed: ${completedJobs.length}`);
    console.log(`   Failed: ${failedJobs.length}`);
    console.log('');

    // Test 5: Error handling and retry (if there are failed jobs)
    if (failedJobs.length > 0) {
      console.log('🧪 Test 5: Error Handling and Retry');
      const failedJob = failedJobs[0];
      console.log(`🔄 Attempting to retry failed job: ${failedJob.id}`);
      
      const retryResult = await orchestrator.retryJob(failedJob.id);
      console.log(`   Retry Result: ${retryResult ? '✅ Success' : '❌ Failed'}`);
      
      if (retryResult) {
        console.log('   Job has been re-queued for processing');
      }
      console.log('');
    }

    console.log('🎉 Scraping Orchestrator Test Completed!');
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log('✅ Workflow Management: Complete scraping workflow tested');
    console.log('✅ Job Monitoring: Real-time progress tracking verified');
    console.log('✅ Queue Management: Job queuing and statistics working');
    console.log('✅ Error Handling: Retry mechanisms and error tracking functional');
    console.log('✅ Quality Assurance: Integrated QA workflow operational');

  } catch (error) {
    console.error('\n❌ Orchestrator test failed:', error);
  } finally {
    // Clean up resources
    await orchestrator.close();
    console.log('\n🧹 Resources cleaned up');
  }
}

// Demonstration of orchestrator configuration for different scenarios
function showConfigurationExamples() {
  console.log('\n🔧 Configuration Examples for Different Scenarios:\n');

  // High-volume scraping
  console.log('🚀 High-Volume Scraping:');
  console.log(`const highVolumeConfig = {
  scraping: {
    maxConcurrency: 5,
    delayBetweenRequests: 1000,
    retryAttempts: 3,
    qualityGateThreshold: 0.7,
    testScrapeSize: 10
  },
  jobManager: {
    concurrency: 3,
    retryAttempts: 3,
    retryDelay: 3000
  }
};`);

  // Conservative scraping
  console.log('\n🐌 Conservative Scraping:');
  console.log(`const conservativeConfig = {
  scraping: {
    maxConcurrency: 1,
    delayBetweenRequests: 5000,
    retryAttempts: 5,
    qualityGateThreshold: 0.8,
    testScrapeSize: 5
  },
  jobManager: {
    concurrency: 1,
    retryAttempts: 5,
    retryDelay: 10000
  }
};`);

  // Development/Testing
  console.log('\n🧪 Development/Testing:');
  console.log(`const devConfig = {
  scraping: {
    maxConcurrency: 1,
    delayBetweenRequests: 2000,
    retryAttempts: 1,
    qualityGateThreshold: 0.5,
    testScrapeSize: 2
  },
  jobManager: {
    concurrency: 1,
    retryAttempts: 1,
    retryDelay: 1000
  }
};`);
}

// Run the test
if (require.main === module) {
  testScrapingOrchestrator()
    .then(() => {
      showConfigurationExamples();
      console.log('\n✅ All tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test suite failed:', error);
      process.exit(1);
    });
}

export { testScrapingOrchestrator };