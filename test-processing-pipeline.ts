#!/usr/bin/env ts-node

/**
 * Test script for the Content Processing Pipeline (Task 7)
 * Tests all components: ContentProcessor, MetadataExtractor, DocumentChunker, StructurePreserver, ProcessingPipeline
 */

import {
  ContentProcessor,
  MetadataExtractor,
  DocumentChunker,
  StructurePreserver,
  ProcessingPipeline,
  ProcessedContent,
  ExtractedMetadata,
  ChunkingResult,
  PreservedStructure
} from './src/processing';

// Simple interface for testing - matches what the processing pipeline expects
interface ScrapedContent {
  url: string;
  title?: string;
  content: string;
  contentType?: string;
  statusCode?: number;
  scrapedAt: Date;
  metadata?: {
    wordCount?: number;
    hasImages?: boolean;
    hasLinks?: boolean;
    hasTables?: boolean;
    language?: string;
    [key: string]: any;
  };
}

// Mock scraped content for testing
const mockScrapedContent: ScrapedContent = {
  url: 'https://dfsa.ae/rulebook/chapter1',
  title: 'DFSA Rulebook - Chapter 1: Introduction',
  content: `
    <h1>Chapter 1: Introduction to DFSA Regulations</h1>
    
    <h2>1.1 Purpose and Scope</h2>
    <p>The Dubai Financial Services Authority (DFSA) is the independent regulator of financial services conducted in or from the Dubai International Financial Centre (DIFC). This rulebook sets out the regulatory framework for financial institutions operating within the DIFC.</p>
    
    <h3>1.1.1 Regulatory Objectives</h3>
    <p>The DFSA's regulatory objectives include:</p>
    <ul>
      <li>Maintaining confidence in the financial system</li>
      <li>Protecting consumers and investors</li>
      <li>Promoting market integrity</li>
      <li>Reducing financial crime</li>
    </ul>
    
    <h2>1.2 Application of Rules</h2>
    <p>These rules apply to all Authorised Firms, Recognised Bodies, and other entities as specified in Rule 1.2.1.</p>
    
    <h3>1.2.1 Scope of Application</h3>
    <p>Rule 1.2.1 states that all financial institutions must comply with the requirements set out in this rulebook. See Section 2.1 for detailed compliance requirements.</p>
    
    <table>
      <tr><th>Entity Type</th><th>Applicable Rules</th><th>Compliance Date</th></tr>
      <tr><td>Banks</td><td>All Rules</td><td>Immediate</td></tr>
      <tr><td>Insurance Companies</td><td>Chapters 1-5</td><td>6 months</td></tr>
      <tr><td>Investment Firms</td><td>Chapters 1-3, 6-8</td><td>3 months</td></tr>
    </table>
    
    <h2>1.3 Definitions</h2>
    <p>For the purposes of this rulebook, the following definitions apply:</p>
    <p><strong>Authorised Firm:</strong> A firm that has been granted a licence by the DFSA to conduct financial services in or from the DIFC.</p>
    <p><strong>Recognised Body:</strong> An entity recognised by the DFSA for specific purposes under these rules.</p>
    
    <p>The requirements outlined in this chapter must be followed by all entities. Failure to comply may result in regulatory action as described in Chapter 7.</p>
  `,
  contentType: 'text/html',
  statusCode: 200,
  scrapedAt: new Date(),
  metadata: {
    wordCount: 250,
    hasImages: false,
    hasLinks: true,
    hasTables: true,
    language: 'en'
  }
};

async function testProcessingPipeline() {
  console.log('🧪 Testing Content Processing Pipeline (Task 7)\n');

  try {
    // Test 1: ContentProcessor
    console.log('📝 Test 1: ContentProcessor');
    const contentProcessor = new ContentProcessor({
      removeExtraWhitespace: true,
      preserveHeadings: true,
      preserveLists: true,
      preserveTables: true,
      minContentLength: 50
    });

    const processedContent = await contentProcessor.processContent(mockScrapedContent);
    
    console.log('✅ ContentProcessor Results:');
    console.log(`   Original length: ${mockScrapedContent.content.length} chars`);
    console.log(`   Processed length: ${processedContent.cleanedContent.length} chars`);
    console.log(`   Word count: ${processedContent.wordCount}`);
    console.log(`   Content quality: ${(processedContent.metadata.contentQuality * 100).toFixed(1)}%`);
    console.log(`   Processing duration: ${processedContent.processingDuration}ms`);
    console.log(`   Structure elements:`);
    console.log(`     Headings: ${processedContent.structure.headings.length}`);
    console.log(`     Lists: ${processedContent.structure.lists.length}`);
    console.log(`     Tables: ${processedContent.structure.tables.length}`);
    console.log(`     Links: ${processedContent.structure.links.length}`);
    console.log(`     Sections: ${processedContent.structure.sections.length}`);
    console.log('');

    // Test 2: MetadataExtractor
    console.log('📊 Test 2: MetadataExtractor');
    const metadataExtractor = new MetadataExtractor({
      extractHierarchy: true,
      maxHierarchyDepth: 6,
      extractSections: true,
      extractTitles: true,
      extractDocumentStructure: true
    });

    const extractedMetadata = await metadataExtractor.extractMetadata(processedContent);
    
    console.log('✅ MetadataExtractor Results:');
    console.log(`   Extraction duration: ${extractedMetadata.extractionDuration}ms`);
    console.log(`   Confidence: ${(extractedMetadata.confidence * 100).toFixed(1)}%`);
    console.log(`   Hierarchy:`);
    console.log(`     Max depth: ${extractedMetadata.hierarchy.maxDepth}`);
    console.log(`     Total nodes: ${extractedMetadata.hierarchy.totalNodes}`);
    console.log(`     Root levels: ${extractedMetadata.hierarchy.levels.length}`);
    console.log(`   Sections: ${extractedMetadata.sections.length}`);
    console.log(`   Titles: ${extractedMetadata.titles.length}`);
    console.log(`   Document structure:`);
    console.log(`     Type: ${extractedMetadata.documentStructure.documentType}`);
    console.log(`     Complexity: ${(extractedMetadata.documentStructure.structureComplexity * 100).toFixed(1)}%`);
    console.log(`     Has TOC: ${extractedMetadata.documentStructure.hasTableOfContents}`);
    console.log('');

    // Test 3: DocumentChunker
    console.log('🔪 Test 3: DocumentChunker');
    const documentChunker = new DocumentChunker({
      strategy: 'hybrid',
      targetChunkSize: 400,
      maxChunkSize: 600,
      minChunkSize: 100,
      chunkOverlap: 50,
      respectSectionBoundaries: true,
      preserveHeadings: true,
      balanceChunkSizes: true
    });

    const chunkingResult = await documentChunker.chunkDocument(processedContent, extractedMetadata);
    
    console.log('✅ DocumentChunker Results:');
    console.log(`   Chunking duration: ${chunkingResult.chunkingDuration}ms`);
    console.log(`   Strategy: ${chunkingResult.chunkingStrategy}`);
    console.log(`   Total chunks: ${chunkingResult.totalChunks}`);
    console.log(`   Total characters: ${chunkingResult.totalCharacters}`);
    console.log(`   Total words: ${chunkingResult.totalWords}`);
    console.log(`   Average chunk size: ${chunkingResult.averageChunkSize} chars`);
    console.log(`   Quality metrics:`);
    console.log(`     Average quality: ${(chunkingResult.qualityMetrics.averageQuality * 100).toFixed(1)}%`);
    console.log(`     Min quality: ${(chunkingResult.qualityMetrics.minQuality * 100).toFixed(1)}%`);
    console.log(`     Max quality: ${(chunkingResult.qualityMetrics.maxQuality * 100).toFixed(1)}%`);
    
    // Show sample chunks
    console.log(`   Sample chunks:`);
    chunkingResult.chunks.slice(0, 3).forEach((chunk, index) => {
      console.log(`     Chunk ${index + 1}:`);
      console.log(`       Size: ${chunk.characterCount} chars, ${chunk.wordCount} words`);
      console.log(`       Quality: ${(chunk.quality.overallScore * 100).toFixed(1)}%`);
      console.log(`       Content preview: "${chunk.content.substring(0, 80)}..."`);
      console.log(`       Content type: ${chunk.metadata.contentType}`);
      console.log(`       Has headings: ${chunk.metadata.hasHeadings}`);
      console.log(`       Has lists: ${chunk.metadata.hasLists}`);
      console.log(`       Topics: ${chunk.metadata.topics.join(', ')}`);
    });
    console.log('');

    // Test 4: StructurePreserver
    console.log('🔗 Test 4: StructurePreserver');
    const structurePreserver = new StructurePreserver({
      preserveHierarchicalRelationships: true,
      preserveSequentialRelationships: true,
      preserveCrossReferences: true,
      trackCitations: true,
      contextWindowSize: 2,
      maxRelationships: 20
    });

    const preservedStructure = await structurePreserver.preserveStructure(
      chunkingResult.chunks,
      extractedMetadata,
      processedContent.cleanedContent
    );
    
    console.log('✅ StructurePreserver Results:');
    console.log(`   Processing duration: ${preservedStructure.processingDuration}ms`);
    console.log(`   Preservation quality: ${(preservedStructure.preservationQuality * 100).toFixed(1)}%`);
    console.log(`   Total relationships: ${preservedStructure.totalRelationships}`);
    console.log(`   Average relationships per chunk: ${preservedStructure.averageRelationshipsPerChunk.toFixed(2)}`);
    console.log(`   Structure complexity: ${(preservedStructure.structureComplexity * 100).toFixed(1)}%`);
    console.log(`   Relationship types:`);
    Object.entries(preservedStructure.relationshipTypes).forEach(([type, count]) => {
      if (count > 0) {
        console.log(`     ${type}: ${count}`);
      }
    });
    
    // Show sample relationships
    if (preservedStructure.relationships.length > 0) {
      console.log(`   Sample relationships:`);
      preservedStructure.relationships.slice(0, 3).forEach((rel, index) => {
        console.log(`     ${index + 1}. ${rel.type} (${rel.strength})`);
        console.log(`        From: ${rel.sourceChunkId}`);
        console.log(`        To: ${rel.targetChunkId}`);
        console.log(`        Description: ${rel.description}`);
        console.log(`        Confidence: ${(rel.confidence * 100).toFixed(1)}%`);
      });
    }
    console.log('');

    // Test 5: Complete ProcessingPipeline
    console.log('🚀 Test 5: Complete ProcessingPipeline');
    const pipeline = new ProcessingPipeline({
      contentProcessor: {
        removeExtraWhitespace: true,
        preserveHeadings: true,
        preserveLists: true,
        preserveTables: true
      },
      metadataExtractor: {
        extractHierarchy: true,
        extractSections: true,
        extractTitles: true
      },
      documentChunker: {
        strategy: 'hybrid',
        targetChunkSize: 400,
        chunkOverlap: 50
      },
      structurePreserver: {
        preserveHierarchicalRelationships: true,
        preserveSequentialRelationships: true,
        trackCitations: true
      },
      enableQualityChecks: true,
      qualityThreshold: 0.6,
      continueOnError: true,
      maxRetries: 1
    });

    const pipelineResult = await pipeline.processContent(mockScrapedContent);
    
    console.log('✅ ProcessingPipeline Results:');
    console.log(`   Total processing time: ${pipelineResult.totalProcessingTime}ms`);
    console.log(`   Overall quality: ${(pipelineResult.overallQuality * 100).toFixed(1)}%`);
    console.log(`   Final chunks: ${pipelineResult.finalChunks.length}`);
    console.log(`   Errors: ${pipelineResult.errors.length}`);
    console.log(`   Warnings: ${pipelineResult.warnings.length}`);
    console.log(`   Stage qualities:`);
    console.log(`     Content processing: ${(pipelineResult.stageQualities.contentProcessing * 100).toFixed(1)}%`);
    console.log(`     Metadata extraction: ${(pipelineResult.stageQualities.metadataExtraction * 100).toFixed(1)}%`);
    console.log(`     Document chunking: ${(pipelineResult.stageQualities.documentChunking * 100).toFixed(1)}%`);
    console.log(`     Structure preservation: ${(pipelineResult.stageQualities.structurePreservation * 100).toFixed(1)}%`);
    console.log(`   Stage timings:`);
    console.log(`     Content processing: ${pipelineResult.stageTimings.contentProcessing}ms`);
    console.log(`     Metadata extraction: ${pipelineResult.stageTimings.metadataExtraction}ms`);
    console.log(`     Document chunking: ${pipelineResult.stageTimings.documentChunking}ms`);
    console.log(`     Structure preservation: ${pipelineResult.stageTimings.structurePreservation}ms`);
    console.log('');

    // Test 6: Batch Processing
    console.log('📦 Test 6: Batch Processing');
    const batchDocuments = [
      mockScrapedContent,
      {
        ...mockScrapedContent,
        url: 'https://dfsa.ae/rulebook/chapter2',
        title: 'DFSA Rulebook - Chapter 2: Licensing',
        content: mockScrapedContent.content.replace('Chapter 1', 'Chapter 2').replace('Introduction', 'Licensing')
      },
      {
        ...mockScrapedContent,
        url: 'https://dfsa.ae/rulebook/chapter3',
        title: 'DFSA Rulebook - Chapter 3: Capital Requirements',
        content: mockScrapedContent.content.replace('Chapter 1', 'Chapter 3').replace('Introduction', 'Capital Requirements')
      }
    ];

    const batchResult = await pipeline.processBatch(batchDocuments);
    
    console.log('✅ Batch Processing Results:');
    console.log(`   Total processed: ${batchResult.totalProcessed}`);
    console.log(`   Successful: ${batchResult.successful}`);
    console.log(`   Failed: ${batchResult.failed}`);
    console.log(`   Total processing time: ${batchResult.totalProcessingTime}ms`);
    console.log(`   Average processing time: ${Math.round(batchResult.averageProcessingTime)}ms per document`);
    console.log(`   Overall quality: ${(batchResult.overallQuality * 100).toFixed(1)}%`);
    
    // Batch statistics
    const totalChunks = batchResult.results.reduce((sum, r) => sum + r.finalChunks.length, 0);
    const totalRelationships = batchResult.results.reduce((sum, r) => sum + r.preservedStructure.totalRelationships, 0);
    
    console.log(`   Batch statistics:`);
    console.log(`     Total chunks generated: ${totalChunks}`);
    console.log(`     Total relationships: ${totalRelationships}`);
    console.log(`     Average chunks per document: ${Math.round(totalChunks / batchResult.successful)}`);
    console.log(`     Average relationships per document: ${Math.round(totalRelationships / batchResult.successful)}`);
    console.log('');

    // Test 7: Error Handling
    console.log('⚠️ Test 7: Error Handling');
    const invalidContent: ScrapedContent = {
      url: 'https://example.com/invalid',
      title: 'Invalid Content',
      content: 'Too short',  // Below minimum length
      contentType: 'text/html',
      statusCode: 200,
      scrapedAt: new Date()
    };

    try {
      await pipeline.processContent(invalidContent);
      console.log('❌ Error handling test failed - should have thrown error');
    } catch (error) {
      console.log('✅ Error handling works correctly:');
      console.log(`   Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    console.log('');

    // Summary
    console.log('🎉 Content Processing Pipeline Test Summary:');
    console.log('✅ ContentProcessor: HTML cleaning, structure extraction, quality validation');
    console.log('✅ MetadataExtractor: Hierarchy building, section identification, document analysis');
    console.log('✅ DocumentChunker: Multi-strategy chunking with quality optimization');
    console.log('✅ StructurePreserver: Relationship tracking and context preservation');
    console.log('✅ ProcessingPipeline: Complete workflow orchestration with error handling');
    console.log('✅ Batch Processing: Efficient multi-document processing');
    console.log('✅ Error Handling: Graceful error recovery and validation');
    console.log('');
    console.log('🚀 All Task 7 components are working correctly!');

  } catch (error) {
    console.error('❌ Processing pipeline test failed:', error);
    if (error instanceof Error) {
      console.error('Stack trace:', error.stack);
    }
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testProcessingPipeline()
    .then(() => {
      console.log('\n✅ All tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

export { testProcessingPipeline };