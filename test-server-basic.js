const express = require('express');
const app = express();
const port = 3000;

app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/', (req, res) => {
  res.json({ message: 'DFSA Rulebook RAG System - Basic Test Server' });
});

app.listen(port, () => {
  console.log(`Basic test server running at http://localhost:${port}`);
  console.log(`Health check: http://localhost:${port}/api/health`);
});