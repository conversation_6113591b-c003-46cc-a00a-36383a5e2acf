#!/usr/bin/env ts-node

/**
 * Real Brightdata integration test with actual credentials
 */

import { BrightdataClient } from './src/scraping/brightdata-client';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testBrightdataIntegration() {
  console.log('🚀 Testing Brightdata Browser API Integration with Real Credentials\n');

  // Verify credentials are available
  if (!process.env.BRIGHTDATA_USERNAME || !process.env.BRIGHTDATA_PASSWORD) {
    console.error('❌ Missing Brightdata credentials in environment variables');
    process.exit(1);
  }

  console.log('📋 Configuration:');
  console.log(`  Username: ${process.env.BRIGHTDATA_USERNAME}`);
  console.log(`  Password: ${'*'.repeat(process.env.BRIGHTDATA_PASSWORD.length)}`);
  console.log(`  Zone: ${process.env.BRIGHTDATA_ZONE || 'browser_api'}`);
  console.log('');

  // Initialize client
  const client = new BrightdataClient({
    username: process.env.BRIGHTDATA_USERNAME,
    password: process.env.BRIGHTDATA_PASSWORD,
    zone: process.env.BRIGHTDATA_ZONE || 'scraping_browser2',
    country: 'US',
    timeout: 60000,
    rateLimitDelay: 2000
  });

  try {
    // Test 1: Connection Test
    console.log('🔍 Test 1: Testing connection...');
    const startTime = Date.now();
    const connectionTest = await client.testConnection();
    const connectionTime = Date.now() - startTime;
    
    console.log(`  Result: ${connectionTest ? '✅ Success' : '❌ Failed'}`);
    console.log(`  Time: ${connectionTime}ms`);
    
    if (!connectionTest) {
      console.error('❌ Connection test failed. Check credentials and network.');
      return;
    }

    // Test 2: Session Information
    console.log('\n📊 Test 2: Getting session information...');
    const sessionInfo = await client.getSessionInfo();
    console.log('  Session Info:', sessionInfo);

    // Test 3: Simple Web Page Scraping
    console.log('\n🌐 Test 3: Testing simple web page scraping...');
    const testUrl = 'https://httpbin.org/html';
    
    const simpleResult = await client.scrapeUrl(testUrl, {
      waitForTimeout: 5000,
      blockResources: ['image', 'stylesheet', 'font']
    });

    console.log('  Simple Scraping Result:');
    console.log(`    URL: ${simpleResult.url}`);
    console.log(`    Status: ${simpleResult.error ? '❌ Failed' : '✅ Success'}`);
    console.log(`    Status Code: ${simpleResult.statusCode}`);
    console.log(`    Title: ${simpleResult.title}`);
    console.log(`    Content Length: ${simpleResult.content.length} chars`);
    console.log(`    Load Time: ${simpleResult.loadTime}ms`);
    if (simpleResult.error) {
      console.log(`    Error: ${simpleResult.error}`);
    }

    // Test 4: DFSA Website Scraping (if simple test passed)
    if (!simpleResult.error) {
      console.log('\n🏛️ Test 4: Testing DFSA website scraping...');
      const dfsaUrl = 'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa';
      
      const dfsaResult = await client.scrapeUrl(dfsaUrl, {
        waitForSelector: 'h1, .content, .main',
        waitForTimeout: 15000,
        blockResources: ['image', 'stylesheet', 'font', 'media'],
        solveCaptcha: true,
        captchaTimeout: 30000
      });

      console.log('  DFSA Scraping Result:');
      console.log(`    URL: ${dfsaResult.url}`);
      console.log(`    Status: ${dfsaResult.error ? '❌ Failed' : '✅ Success'}`);
      console.log(`    Status Code: ${dfsaResult.statusCode}`);
      console.log(`    Title: ${dfsaResult.title}`);
      console.log(`    Content Length: ${dfsaResult.content.length} chars`);
      console.log(`    Load Time: ${dfsaResult.loadTime}ms`);
      
      if (dfsaResult.error) {
        console.log(`    Error: ${dfsaResult.error}`);
      } else {
        // Analyze content
        const hasDFSAContent = /dfsa|dubai financial services|rulebook/i.test(dfsaResult.content);
        const hasHeadings = /<h[1-6][^>]*>/i.test(dfsaResult.content);
        const wordCount = dfsaResult.content.split(/\s+/).length;
        
        console.log(`    Has DFSA Content: ${hasDFSAContent ? '✅ Yes' : '❌ No'}`);
        console.log(`    Has Headings: ${hasHeadings ? '✅ Yes' : '❌ No'}`);
        console.log(`    Word Count: ${wordCount}`);
        
        // Show content preview
        const contentPreview = dfsaResult.content
          .replace(/<[^>]*>/g, ' ')
          .replace(/\s+/g, ' ')
          .trim()
          .substring(0, 200);
        console.log(`    Content Preview: ${contentPreview}...`);
      }
    }

    // Test 5: Batch Scraping (if previous tests passed)
    if (!simpleResult.error) {
      console.log('\n📦 Test 5: Testing batch scraping...');
      const testUrls = [
        'https://httpbin.org/html',
        'https://httpbin.org/json'
      ];

      const batchResults = await client.scrapeUrls(testUrls, {
        waitForTimeout: 3000,
        blockResources: ['image', 'stylesheet', 'font']
      }, 1); // Low concurrency for testing

      console.log('  Batch Scraping Results:');
      console.log(`    Total URLs: ${testUrls.length}`);
      console.log(`    Successful: ${batchResults.filter(r => !r.error).length}`);
      console.log(`    Failed: ${batchResults.filter(r => r.error).length}`);
      
      const avgLoadTime = batchResults
        .filter(r => !r.error)
        .reduce((sum, r) => sum + r.loadTime, 0) / batchResults.filter(r => !r.error).length;
      
      if (!isNaN(avgLoadTime)) {
        console.log(`    Average Load Time: ${Math.round(avgLoadTime)}ms`);
      }

      batchResults.forEach((result, index) => {
        console.log(`    ${index + 1}. ${result.url}: ${result.error ? '❌ Failed' : '✅ Success'}`);
        if (result.error) {
          console.log(`       Error: ${result.error}`);
        }
      });
    }

    console.log('\n🎉 Brightdata integration test completed!');
    
    // Health status
    const health = client.getHealthStatus();
    console.log('\n📊 Final Health Status:', health);

  } catch (error) {
    console.error('\n❌ Test failed with error:', error);
  } finally {
    // Clean up
    await client.close();
    console.log('\n🧹 Client closed and resources cleaned up');
  }
}

// Run the test
if (require.main === module) {
  testBrightdataIntegration()
    .then(() => {
      console.log('\n✅ All tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test suite failed:', error);
      process.exit(1);
    });
}

export { testBrightdataIntegration };