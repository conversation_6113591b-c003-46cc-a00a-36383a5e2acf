#!/usr/bin/env ts-node

/**
 * Test script for the Quality Assurance system
 * Demonstrates test scraping, content validation, and manual confirmation workflow
 */

import { BrightdataClient } from './src/scraping/brightdata-client';
import { QualityAssurance } from './src/scraping/quality-assurance';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testQualityAssurance() {
  console.log('🧪 Testing DFSA Quality Assurance System\n');

  // Verify credentials are available
  if (!process.env.BRIGHTDATA_USERNAME || !process.env.BRIGHTDATA_PASSWORD) {
    console.error('❌ Missing Brightdata credentials in environment variables');
    process.exit(1);
  }

  // Initialize Brightdata client
  const brightdataClient = new BrightdataClient({
    username: process.env.BRIGHTDATA_USERNAME,
    password: process.env.BRIGHTDATA_PASSWORD,
    zone: process.env.BRIGHTDATA_ZONE || 'scraping_browser2',
    country: 'US',
    timeout: 60000,
    rateLimitDelay: 2000
  });

  // Initialize Quality Assurance system
  const qualityAssurance = new QualityAssurance(brightdataClient, {
    testScrapeSize: 3, // Small sample for testing
    qualityThreshold: 0.6, // Lower threshold for testing
    requireManualConfirmation: true,
    autoApproveThreshold: 0.95
  });

  try {
    console.log('🔍 Step 1: Preparing test URLs...');
    
    // Sample DFSA URLs for testing
    const testUrls = [
      'https://dfsaen.thomsonreuters.com/rulebook/dubai-financial-services-authority-dfsa',
      'https://dfsaen.thomsonreuters.com/rulebook/general-module',
      'https://dfsaen.thomsonreuters.com/rulebook/conduct-of-business',
      'https://dfsaen.thomsonreuters.com/rulebook/prudential-investment-business-sourcebook',
      'https://dfsaen.thomsonreuters.com/rulebook/market-conduct'
    ];

    console.log(`📋 Test URLs prepared: ${testUrls.length} URLs`);
    testUrls.forEach((url, index) => {
      console.log(`  ${index + 1}. ${url}`);
    });

    console.log('\n🧪 Step 2: Performing test scrape workflow...');
    console.log('This will:');
    console.log('  • Scrape a sample of URLs');
    console.log('  • Validate content quality');
    console.log('  • Request manual confirmation');
    console.log('  • Generate detailed reports');

    const workflowResult = await qualityAssurance.performTestScrapeWorkflow(testUrls);

    console.log('\n📊 Step 3: Test scrape workflow completed!');
    console.log(`  Approval Status: ${workflowResult.approved ? '✅ APPROVED' : '❌ REJECTED'}`);
    console.log(`  Quality Score: ${(workflowResult.testResult.qualityScore * 100).toFixed(1)}%`);
    console.log(`  URLs Tested: ${workflowResult.testResult.results.length}`);
    
    const successful = workflowResult.testResult.results.filter(r => r.status === 'scraped').length;
    console.log(`  Successful Scrapes: ${successful}/${workflowResult.testResult.results.length}`);

    if (workflowResult.testResult.recommendations.length > 0) {
      console.log('\n💡 System Recommendations:');
      workflowResult.testResult.recommendations.forEach(rec => {
        console.log(`  • ${rec}`);
      });
    }

    // Display confirmation report if available
    if (workflowResult.confirmationReport) {
      console.log('\n📄 Confirmation Report Generated');
      console.log('  (Report saved to memory - in production this would be saved to database)');
    }

    // Demonstrate quality gate validation
    if (workflowResult.approved && workflowResult.testResult.results.length > 0) {
      console.log('\n🚦 Step 4: Demonstrating quality gate validation...');
      
      const qualityGateResult = await qualityAssurance.performQualityGate(
        workflowResult.testResult.results,
        { completed: workflowResult.testResult.results.length, total: testUrls.length }
      );

      console.log(`  Quality Gate Status: ${qualityGateResult.passed ? '✅ PASSED' : '❌ FAILED'}`);
      console.log(`  Overall Score: ${(qualityGateResult.overallScore * 100).toFixed(1)}%`);
      console.log(`  Issues Found: ${qualityGateResult.issues.length}`);

      if (qualityGateResult.issues.length > 0) {
        console.log('\n⚠️  Quality Issues:');
        qualityGateResult.issues.forEach(issue => {
          const severity = issue.severity === 'high' ? '🔴' : 
                          issue.severity === 'medium' ? '🟡' : '🟢';
          console.log(`    ${severity} ${issue.type.toUpperCase()}: ${issue.description}`);
        });
      }

      // Generate comprehensive quality report
      console.log('\n📈 Step 5: Generating comprehensive quality report...');
      const qualityReport = qualityAssurance.generateQualityReport(
        workflowResult.testResult,
        [qualityGateResult]
      );

      console.log('  📊 Quality Report Summary:');
      console.log(`    Test Scrape Approved: ${qualityReport.testScrapeApproved ? '✅' : '❌'}`);
      console.log(`    Quality Gates Passed: ${qualityReport.qualityGatesPassed}`);
      console.log(`    Quality Gates Failed: ${qualityReport.qualityGatesFailed}`);
      console.log(`    Overall Quality Score: ${(qualityReport.overallQualityScore * 100).toFixed(1)}%`);
      console.log(`    Total Issues: ${qualityReport.issues.length}`);
      console.log(`    Recommendations: ${qualityReport.recommendations.length}`);
    }

    console.log('\n🎉 Quality Assurance System Test Completed Successfully!');
    
    if (workflowResult.approved) {
      console.log('\n✅ System is ready for full-scale scraping');
      console.log('   Next steps:');
      console.log('   • Configure scraping orchestrator');
      console.log('   • Set up job queue management');
      console.log('   • Begin full DFSA rulebook scraping');
    } else {
      console.log('\n⚠️  System requires adjustments before full-scale scraping');
      console.log('   Recommended actions:');
      console.log('   • Review and adjust scraping parameters');
      console.log('   • Test with different URL samples');
      console.log('   • Address identified quality issues');
    }

  } catch (error) {
    console.error('\n❌ Quality Assurance test failed:', error);
  } finally {
    // Clean up resources
    await qualityAssurance.cleanup();
    await brightdataClient.close();
    console.log('\n🧹 Resources cleaned up');
  }
}

// Run the test
if (require.main === module) {
  testQualityAssurance()
    .then(() => {
      console.log('\n✅ Quality Assurance test completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Quality Assurance test failed:', error);
      process.exit(1);
    });
}

export { testQualityAssurance };