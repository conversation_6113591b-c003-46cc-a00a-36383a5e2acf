#!/usr/bin/env node

/**
 * Integration test for the content processing pipeline
 * Tests the components working together with mock data
 */

const fs = require('fs');

// Mock DFSA content for testing
const mockDfsaContent = {
  url: 'https://dfsa.ae/rulebook/chapter1',
  title: 'DFSA Rulebook - Chapter 1: Introduction',
  content: `
    <div class="rulebook-content">
      <h1>Chapter 1: Introduction to DFSA Regulations</h1>
      
      <h2>1.1 Purpose and Scope</h2>
      <p>The Dubai Financial Services Authority (DFSA) is the independent regulator of financial services conducted in or from the Dubai International Financial Centre (DIFC). This rulebook sets out the regulatory framework for financial institutions operating within the DIFC.</p>
      
      <h3>1.1.1 Regulatory Objectives</h3>
      <p>The DFSA's regulatory objectives include:</p>
      <ul>
        <li>Maintaining confidence in the financial system</li>
        <li>Protecting consumers and investors</li>
        <li>Promoting market integrity</li>
        <li>Reducing financial crime</li>
      </ul>
      
      <h2>1.2 Application of Rules</h2>
      <p>These rules apply to all Authorised Firms, Recognised Bodies, and other entities as specified in <strong>Rule 1.2.1</strong>.</p>
      
      <h3>1.2.1 Scope of Application</h3>
      <p>Rule 1.2.1 states that all financial institutions must comply with the requirements set out in this rulebook. See <em>Section 2.1</em> for detailed compliance requirements.</p>
      
      <table>
        <thead>
          <tr><th>Entity Type</th><th>Applicable Rules</th><th>Compliance Date</th></tr>
        </thead>
        <tbody>
          <tr><td>Banks</td><td>All Rules</td><td>Immediate</td></tr>
          <tr><td>Insurance Companies</td><td>Chapters 1-5</td><td>6 months</td></tr>
          <tr><td>Investment Firms</td><td>Chapters 1-3, 6-8</td><td>3 months</td></tr>
        </tbody>
      </table>
      
      <h2>1.3 Definitions</h2>
      <p>For the purposes of this rulebook, the following definitions apply:</p>
      <p><strong>Authorised Firm:</strong> A firm that has been granted a licence by the DFSA to conduct financial services in or from the DIFC.</p>
      <p><strong>Recognised Body:</strong> An entity recognised by the DFSA for specific purposes under these rules.</p>
    </div>
  `,
  contentType: 'text/html',
  statusCode: 200,
  scrapedAt: new Date(),
  metadata: {
    wordCount: 250,
    hasImages: false,
    hasLinks: true,
    hasTables: true,
    language: 'en'
  }
};

function simulateContentProcessing(content) {
  console.log('🔄 Simulating Content Processing Pipeline\n');
  
  // Step 1: Content Processing
  console.log('1️⃣ Content Processing');
  const cleanedContent = content.content
    .replace(/<[^>]*>/g, ' ')  // Remove HTML tags
    .replace(/\s+/g, ' ')      // Normalize whitespace
    .trim();
  
  console.log(`   ✅ HTML tags removed`);
  console.log(`   ✅ Whitespace normalized`);
  console.log(`   📊 Original: ${content.content.length} chars → Cleaned: ${cleanedContent.length} chars`);
  
  // Step 2: Metadata Extraction
  console.log('\n2️⃣ Metadata Extraction');
  const headings = content.content.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/g) || [];
  const ruleReferences = content.content.match(/Rule\s+\d+\.\d+(\.\d+)?/g) || [];
  const sectionReferences = content.content.match(/Section\s+\d+\.\d+/g) || [];
  const listItems = content.content.match(/<li[^>]*>(.*?)<\/li>/g) || [];
  
  console.log(`   ✅ Headings extracted: ${headings.length}`);
  console.log(`   ✅ Rule references found: ${ruleReferences.length}`);
  console.log(`   ✅ Section references found: ${sectionReferences.length}`);
  console.log(`   ✅ List items extracted: ${listItems.length}`);
  
  if (headings.length > 0) {
    console.log(`   📝 Sample heading: "${headings[0].replace(/<[^>]*>/g, '')}"`);
  }
  if (ruleReferences.length > 0) {
    console.log(`   📋 Sample rule: "${ruleReferences[0]}"`);
  }
  
  // Step 3: Document Chunking
  console.log('\n3️⃣ Document Chunking');
  const targetChunkSize = 400;
  const overlap = 50;
  const words = cleanedContent.split(' ');
  const chunks = [];
  
  for (let i = 0; i < words.length; i += targetChunkSize - overlap) {
    const chunkWords = words.slice(i, i + targetChunkSize);
    const chunk = chunkWords.join(' ');
    if (chunk.trim().length > 50) { // Minimum chunk size
      chunks.push({
        content: chunk,
        startIndex: i,
        wordCount: chunkWords.length,
        characterCount: chunk.length
      });
    }
  }
  
  console.log(`   ✅ Document chunked into ${chunks.length} pieces`);
  console.log(`   📊 Average chunk size: ${Math.round(chunks.reduce((sum, c) => sum + c.characterCount, 0) / chunks.length)} chars`);
  console.log(`   📊 Target size: ${targetChunkSize} words, Overlap: ${overlap} words`);
  
  // Step 4: Structure Preservation
  console.log('\n4️⃣ Structure Preservation');
  const relationships = [];
  
  // Simulate relationship detection
  chunks.forEach((chunk, index) => {
    if (index > 0) {
      relationships.push({
        type: 'sequential',
        from: index - 1,
        to: index,
        strength: 0.8
      });
    }
    
    // Check for rule references creating relationships
    ruleReferences.forEach(rule => {
      if (chunk.content.includes(rule)) {
        relationships.push({
          type: 'rule_reference',
          chunk: index,
          reference: rule,
          strength: 0.9
        });
      }
    });
  });
  
  console.log(`   ✅ Relationships identified: ${relationships.length}`);
  console.log(`   📊 Sequential relationships: ${relationships.filter(r => r.type === 'sequential').length}`);
  console.log(`   📊 Rule reference relationships: ${relationships.filter(r => r.type === 'rule_reference').length}`);
  
  // Step 5: Quality Assessment
  console.log('\n5️⃣ Quality Assessment');
  const qualityMetrics = {
    contentRetention: cleanedContent.length / content.content.length,
    structurePreservation: headings.length > 0 ? 1.0 : 0.5,
    metadataRichness: (ruleReferences.length + sectionReferences.length) / 10,
    chunkingQuality: chunks.length > 0 && chunks.length < 20 ? 1.0 : 0.7,
    relationshipDensity: relationships.length / chunks.length
  };
  
  const overallQuality = Object.values(qualityMetrics).reduce((sum, val) => sum + Math.min(val, 1), 0) / Object.keys(qualityMetrics).length;
  
  console.log(`   📊 Content retention: ${(qualityMetrics.contentRetention * 100).toFixed(1)}%`);
  console.log(`   📊 Structure preservation: ${(qualityMetrics.structurePreservation * 100).toFixed(1)}%`);
  console.log(`   📊 Metadata richness: ${(Math.min(qualityMetrics.metadataRichness, 1) * 100).toFixed(1)}%`);
  console.log(`   📊 Chunking quality: ${(qualityMetrics.chunkingQuality * 100).toFixed(1)}%`);
  console.log(`   📊 Relationship density: ${qualityMetrics.relationshipDensity.toFixed(2)} per chunk`);
  console.log(`   🎯 Overall quality: ${(overallQuality * 100).toFixed(1)}%`);
  
  return {
    processedContent: cleanedContent,
    metadata: {
      headings: headings.length,
      ruleReferences: ruleReferences.length,
      sectionReferences: sectionReferences.length,
      listItems: listItems.length
    },
    chunks: chunks,
    relationships: relationships,
    quality: overallQuality
  };
}

function testProcessingIntegration() {
  console.log('🧪 Content Processing Pipeline Integration Test\n');
  console.log('📋 Test Configuration:');
  console.log(`   Content: DFSA Rulebook Chapter 1`);
  console.log(`   Original size: ${mockDfsaContent.content.length} characters`);
  console.log(`   Content type: ${mockDfsaContent.contentType}`);
  console.log(`   Has tables: ${mockDfsaContent.metadata.hasTables}`);
  console.log('');
  
  try {
    const startTime = Date.now();
    const result = simulateContentProcessing(mockDfsaContent);
    const duration = Date.now() - startTime;
    
    console.log('\n✅ Processing Pipeline Completed Successfully!');
    console.log(`   ⏱️  Processing time: ${duration}ms`);
    console.log(`   📄 Chunks created: ${result.chunks.length}`);
    console.log(`   🔗 Relationships found: ${result.relationships.length}`);
    console.log(`   📊 Overall quality: ${(result.quality * 100).toFixed(1)}%`);
    
    // Show sample chunk
    if (result.chunks.length > 0) {
      console.log('\n📝 Sample Chunk:');
      const sampleChunk = result.chunks[0];
      console.log(`   Content: "${sampleChunk.content.substring(0, 100)}..."`);
      console.log(`   Size: ${sampleChunk.characterCount} chars, ${sampleChunk.wordCount} words`);
    }
    
    // Show sample relationship
    if (result.relationships.length > 0) {
      console.log('\n🔗 Sample Relationship:');
      const sampleRel = result.relationships[0];
      console.log(`   Type: ${sampleRel.type}`);
      console.log(`   Strength: ${sampleRel.strength}`);
    }
    
    console.log('\n🎉 Integration Test Passed!');
    return true;
    
  } catch (error) {
    console.error('\n❌ Integration test failed:', error.message);
    return false;
  }
}

// Run the test
if (require.main === module) {
  const success = testProcessingIntegration();
  console.log(`\n${success ? '✅' : '❌'} Test ${success ? 'completed successfully' : 'failed'}!`);
  process.exit(success ? 0 : 1);
}

module.exports = { testProcessingIntegration };