[build]
builder = "NIXPACKS"
buildCommand = "npm run build"

[deploy]
startCommand = "node dist/index.js"
healthcheckPath = "/api/health"
healthcheckTimeout = 100
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 5

[env]
NODE_ENV = "production"
PORT = "3000"

[[services]]
name = "redis"
image = "redis:7-alpine"
internal_port = 6379
command = "redis-server --appendonly yes"

[[services]]
name = "app"
internal_port = 3000