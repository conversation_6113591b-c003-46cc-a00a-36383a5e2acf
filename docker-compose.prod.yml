version: '3.8'

services:
  # Redis for job queues and caching
  redis:
    image: redis:7-alpine
    container_name: dfsa-redis-prod
    restart: always
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Main application
  app:
    image: ${DOCKER_REGISTRY:-localhost}/dfsa-rag:${TAG:-latest}
    container_name: dfsa-app-prod
    restart: always
    ports:
      - "${PORT:-3000}:3000"
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=${DATABASE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - PINECONE_API_KEY=${PINECONE_API_KEY}
      - PINECONE_ENVIRONMENT=${PINECONE_ENVIRONMENT}
      - PINECONE_INDEX=${PINECONE_INDEX}
      - BRIGHTDATA_API_KEY=${BRIGHTDATA_API_KEY}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      - app_logs:/app/logs
      - app_data:/app/data
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "-qO-", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 4G
    logging:
      driver: "json-file"
      options:
        max-size: "20m"
        max-file: "5"

volumes:
  redis_data:
    driver: local
    driver_opts:
      type: none
      device: ${DATA_PATH:-/data}/redis
      o: bind
  app_logs:
    driver: local
    driver_opts:
      type: none
      device: ${DATA_PATH:-/data}/logs
      o: bind
  app_data:
    driver: local
    driver_opts:
      type: none
      device: ${DATA_PATH:-/data}/app_data
      o: bind

networks:
  default:
    driver: bridge