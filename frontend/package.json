{"name": "dfsa-rulebook-rag-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.18.0", "@mui/material": "^5.18.0", "@mui/x-date-pickers": "^6.20.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.66", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "axios": "^1.6.2", "chart.js": "^4.4.0", "date-fns": "^3.6.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.4", "typescript": "^4.9.5", "uuid": "^9.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3000"}