import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import EvaluationDashboard from '../pages/EvaluationDashboard';
// import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Mock axios
jest.mock('axios');

// Mock chart.js
jest.mock('chart.js', () => ({
  Chart: {
    register: jest.fn(),
  },
  CategoryScale: jest.fn(),
  LinearScale: jest.fn(),
  PointElement: jest.fn(),
  LineElement: jest.fn(),
  BarElement: jest.fn(),
  Title: jest.fn(),
  Tooltip: jest.fn(),
  Legend: jest.fn(),
  ArcElement: jest.fn(),
}));

// Mock react-chartjs-2
jest.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="mock-line-chart" />,
  Bar: () => <div data-testid="mock-bar-chart" />,
  Pie: () => <div data-testid="mock-pie-chart" />,
}));

describe('EvaluationDashboard', () => {
  const renderWithProviders = (ui: React.ReactElement) => {
    return render(
      <BrowserRouter>
        {ui}
      </BrowserRouter>
    );
  };

  test('renders evaluation dashboard with tabs', () => {
    renderWithProviders(<EvaluationDashboard />);
    
    // Check for the title
    expect(screen.getByText('RAG Evaluation Tools')).toBeInTheDocument();
    
    // Check for all tabs
    expect(screen.getByText('Ground Truth')).toBeInTheDocument();
    expect(screen.getByText('Evaluation Runs')).toBeInTheDocument();
    expect(screen.getByText('Config Comparison')).toBeInTheDocument();
    expect(screen.getByText('Feedback Analysis')).toBeInTheDocument();
  });

  test('switches between tabs when clicked', () => {
    renderWithProviders(<EvaluationDashboard />);
    
    // Initially, the Ground Truth tab should be active
    const groundTruthTab = screen.getByText('Ground Truth');
    const evaluationRunsTab = screen.getByText('Evaluation Runs');
    const configComparisonTab = screen.getByText('Config Comparison');
    const feedbackAnalysisTab = screen.getByText('Feedback Analysis');
    
    // Click on Evaluation Runs tab
    fireEvent.click(evaluationRunsTab);
    
    // Click on Config Comparison tab
    fireEvent.click(configComparisonTab);
    
    // Click on Feedback Analysis tab
    fireEvent.click(feedbackAnalysisTab);
    
    // Click back to Ground Truth tab
    fireEvent.click(groundTruthTab);
  });
});