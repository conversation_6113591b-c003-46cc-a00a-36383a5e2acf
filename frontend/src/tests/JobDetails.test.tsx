import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import JobDetails from '../pages/JobDetails';

// Mock the react-router-dom hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ jobId: 'test-job-id' }),
  useNavigate: () => jest.fn(),
}));

// Mock the socket context
jest.mock('../contexts/SocketContext', () => ({
  useSocket: () => ({
    socket: {
      emit: jest.fn((event, data, callback) => {
        if (event === 'get:job') {
          callback({
            success: true,
            job: {
              id: 'test-job-id',
              status: 'running',
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              config: {
                mode: 'recursive',
                maxDepth: 3,
                delay: 1000,
                retryAttempts: 3,
              },
              progress: {
                percentage: 50,
                completedUrls: 5,
                totalUrls: 10,
                failedUrls: 0,
                currentUrl: 'https://example.com/page',
              },
              errors: [
                {
                  url: 'https://example.com/error',
                  error: 'Failed to scrape',
                  timestamp: new Date().toISOString(),
                  retryCount: 2,
                },
              ],
              urls: [
                {
                  url: 'https://example.com/page1',
                  status: 'scraped',
                  scrapedAt: new Date().toISOString(),
                },
                {
                  url: 'https://example.com/page2',
                  status: 'pending',
                },
              ],
            },
          });
        }
      }),
      on: jest.fn(),
      off: jest.fn(),
    },
    isConnected: true,
  }),
}));

describe('JobDetails Component', () => {
  test('renders job details title', async () => {
    render(
      <BrowserRouter>
        <JobDetails />
      </BrowserRouter>
    );
    
    expect(await screen.findByText('Job Details')).toBeInTheDocument();
  });

  test('renders job summary section', async () => {
    render(
      <BrowserRouter>
        <JobDetails />
      </BrowserRouter>
    );
    
    expect(await screen.findByText('Job Summary')).toBeInTheDocument();
    expect(await screen.findByText('Job ID')).toBeInTheDocument();
    expect(await screen.findByText('Status')).toBeInTheDocument();
  });

  test('renders configuration section', async () => {
    render(
      <BrowserRouter>
        <JobDetails />
      </BrowserRouter>
    );
    
    expect(await screen.findByText('Configuration')).toBeInTheDocument();
    expect(await screen.findByText('Mode')).toBeInTheDocument();
    expect(await screen.findByText('recursive')).toBeInTheDocument();
  });

  test('renders progress section', async () => {
    render(
      <BrowserRouter>
        <JobDetails />
      </BrowserRouter>
    );
    
    expect(await screen.findByText('Progress')).toBeInTheDocument();
    expect(await screen.findByText('Total URLs')).toBeInTheDocument();
    expect(await screen.findByText('10')).toBeInTheDocument();
  });

  test('renders URL status section', async () => {
    render(
      <BrowserRouter>
        <JobDetails />
      </BrowserRouter>
    );
    
    expect(await screen.findByText('URL Status')).toBeInTheDocument();
  });

  test('renders errors section', async () => {
    render(
      <BrowserRouter>
        <JobDetails />
      </BrowserRouter>
    );
    
    expect(await screen.findByText('Errors')).toBeInTheDocument();
    expect(await screen.findByText('Failed to scrape')).toBeInTheDocument();
  });
});