import React from 'react';
import { Chip } from '@mui/material';
import {
  CheckCircle as CompletedIcon,
  <PERSON>rror as FailedIcon,
  HourglassEmpty as WaitingIcon,
  PlayCircle as RunningIcon,
  Cancel as CancelledIcon,
} from '@mui/icons-material';

interface JobStatusChipProps {
  status: string;
}

const JobStatusChip: React.FC<JobStatusChipProps> = ({ status }) => {
  const getStatusConfig = () => {
    switch (status.toLowerCase()) {
      case 'completed':
        return {
          label: 'Completed',
          color: 'success' as const,
          icon: <CompletedIcon />,
        };
      case 'failed':
        return {
          label: 'Failed',
          color: 'error' as const,
          icon: <FailedIcon />,
        };
      case 'running':
        return {
          label: 'Running',
          color: 'primary' as const,
          icon: <RunningIcon />,
        };
      case 'pending':
        return {
          label: 'Pending',
          color: 'warning' as const,
          icon: <WaitingIcon />,
        };
      case 'cancelled':
        return {
          label: 'Cancelled',
          color: 'default' as const,
          icon: <CancelledIcon />,
        };
      default:
        return {
          label: status,
          color: 'default' as const,
          icon: null,
        };
    }
  };

  const { label, color, icon } = getStatusConfig();

  return <Chip label={label} color={color} icon={icon || undefined} size="small" />;
};

export default JobStatusChip;