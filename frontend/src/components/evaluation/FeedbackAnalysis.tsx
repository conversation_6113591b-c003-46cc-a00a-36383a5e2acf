import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Alert,
  CircularProgress,
  Snackbar,
  Grid,
  Card,
  CardContent,
  Chip,
  Stack,
  Divider,
} from '@mui/material';
import {
  Add,
  Delete,
  Visibility,
  Assessment,
  ThumbUp,
  ThumbDown,
  ThumbsUpDown,
} from '@mui/icons-material';
// import { DatePicker } from '@mui/x-date-pickers/DatePicker';
// import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
// import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import axios from 'axios';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Line, Bar, Pie } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface UserFeedback {
  id: string;
  conversationId: string;
  messageId: string;
  rating: 'positive' | 'negative' | 'neutral';
  category?: 'accuracy' | 'relevance' | 'completeness' | 'clarity' | 'other';
  comment?: string;
  createdAt: Date;
  userId?: string;
}

interface FeedbackAnalysis {
  id: string;
  period: {
    start: Date;
    end: Date;
  };
  metrics: {
    totalFeedback: number;
    positivePercentage: number;
    negativePercentage: number;
    neutralPercentage: number;
    categoryBreakdown: Record<string, number>;
  };
  trends: Array<{
    date: Date;
    positiveCount: number;
    negativeCount: number;
    neutralCount: number;
  }>;
  topIssues: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  createdAt: Date;
}

const FeedbackAnalysisComponent: React.FC = () => {
  const [feedback, setFeedback] = useState<UserFeedback[]>([]);
  const [analyses, setAnalyses] = useState<FeedbackAnalysis[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [viewAnalysis, setViewAnalysis] = useState<FeedbackAnalysis | null>(null);
  const [formData, setFormData] = useState({
    start: new Date(new Date().setDate(new Date().getDate() - 30)),
    end: new Date(),
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  useEffect(() => {
    fetchFeedback();
    fetchAnalyses();
  }, []);

  const fetchFeedback = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/evaluation/feedback');
      setFeedback(response.data);
      setError(null);
    } catch (error) {
      setError('Failed to fetch feedback');
      console.error('Error fetching feedback:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalyses = async () => {
    try {
      const response = await axios.get('/api/evaluation/feedback/analysis');
      setAnalyses(response.data);
    } catch (error) {
      console.error('Error fetching analyses:', error);
    }
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleDateChange = (name: string, date: Date | null) => {
    if (date) {
      setFormData(prev => ({ ...prev, [name]: date }));
    }
  };

  const handleSubmit = async () => {
    try {
      const response = await axios.post('/api/evaluation/feedback/analyze', {
        start: formData.start,
        end: formData.end,
      });
      
      setSnackbar({
        open: true,
        message: 'Feedback analysis created successfully',
        severity: 'success',
      });
      
      handleCloseDialog();
      fetchAnalyses();
      setViewAnalysis(response.data);
    } catch (error) {
      console.error('Error creating feedback analysis:', error);
      setSnackbar({
        open: true,
        message: 'Failed to create feedback analysis',
        severity: 'error',
      });
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this analysis?')) {
      try {
        await axios.delete(`/api/evaluation/feedback/analysis/${id}`);
        setSnackbar({
          open: true,
          message: 'Analysis deleted successfully',
          severity: 'success',
        });
        fetchAnalyses();
      } catch (error) {
        console.error('Error deleting analysis:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete analysis',
          severity: 'error',
        });
      }
    }
  };

  const handleViewAnalysis = async (id: string) => {
    try {
      const response = await axios.get(`/api/evaluation/feedback/analysis/${id}`);
      setViewAnalysis(response.data);
    } catch (error) {
      console.error('Error fetching analysis details:', error);
      setSnackbar({
        open: true,
        message: 'Failed to fetch analysis details',
        severity: 'error',
      });
    }
  };

  const handleCloseViewDialog = () => {
    setViewAnalysis(null);
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const getRatingIcon = (rating: string) => {
    switch (rating) {
      case 'positive':
        return <ThumbUp fontSize="small" color="success" />;
      case 'negative':
        return <ThumbDown fontSize="small" color="error" />;
      case 'neutral':
        return <ThumbsUpDown fontSize="small" color="action" />;
      default:
        return null;
    }
  };

  const getCategoryColor = (category: string | undefined) => {
    switch (category) {
      case 'accuracy':
        return 'primary';
      case 'relevance':
        return 'secondary';
      case 'completeness':
        return 'info';
      case 'clarity':
        return 'success';
      case 'other':
        return 'default';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Feedback Analysis
        </Typography>
        <Box>
          <Button
            startIcon={<Assessment />}
            variant="contained"
            onClick={handleOpenDialog}
          >
            New Analysis
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Recent Feedback
          </Typography>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Rating</TableCell>
                    <TableCell>Category</TableCell>
                    <TableCell>Comment</TableCell>
                    <TableCell>Date</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {feedback.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} align="center">
                        No feedback found.
                      </TableCell>
                    </TableRow>
                  ) : (
                    feedback.slice(0, 10).map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {getRatingIcon(item.rating)}
                            <Typography sx={{ ml: 1 }}>
                              {item.rating.charAt(0).toUpperCase() + item.rating.slice(1)}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          {item.category ? (
                            <Chip
                              label={item.category}
                              color={getCategoryColor(item.category) as any}
                              size="small"
                            />
                          ) : (
                            'Uncategorized'
                          )}
                        </TableCell>
                        <TableCell>
                          {item.comment
                            ? item.comment.length > 50
                              ? `${item.comment.substring(0, 50)}...`
                              : item.comment
                            : 'No comment'}
                        </TableCell>
                        <TableCell>
                          {new Date(item.createdAt).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </Grid>

        <Grid item xs={12} md={6}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Analyses
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Period</TableCell>
                  <TableCell>Total Feedback</TableCell>
                  <TableCell>Positive %</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {analyses.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} align="center">
                      No analyses found. Create one to get started.
                    </TableCell>
                  </TableRow>
                ) : (
                  analyses.map((analysis) => (
                    <TableRow key={analysis.id}>
                      <TableCell>
                        {new Date(analysis.period.start).toLocaleDateString()} - {new Date(analysis.period.end).toLocaleDateString()}
                      </TableCell>
                      <TableCell>{analysis.metrics.totalFeedback}</TableCell>
                      <TableCell>{analysis.metrics.positivePercentage.toFixed(1)}%</TableCell>
                      <TableCell>
                        {new Date(analysis.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleViewAnalysis(analysis.id)}
                        >
                          <Visibility />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleDelete(analysis.id)}
                        >
                          <Delete />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
      </Grid>

      {/* Create Analysis Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>Create New Feedback Analysis</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2, mb: 2 }}>
            <TextField
              label="Start Date"
              type="date"
              value={formData.start ? formData.start.toISOString().split('T')[0] : ''}
              onChange={(e) => handleDateChange('start', new Date(e.target.value))}
              fullWidth
              sx={{ mb: 2 }}
              InputLabelProps={{ shrink: true }}
            />
            <TextField
              label="End Date"
              type="date"
              value={formData.end ? formData.end.toISOString().split('T')[0] : ''}
              onChange={(e) => handleDateChange('end', new Date(e.target.value))}
              fullWidth
              InputLabelProps={{ shrink: true }}
            />
          </Box>
        </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseDialog}>Cancel</Button>
            <Button onClick={handleSubmit} variant="contained">
              Analyze
            </Button>
          </DialogActions>
        </Dialog>

      {/* View Analysis Dialog */}
      <Dialog
        open={!!viewAnalysis}
        onClose={handleCloseViewDialog}
        maxWidth="lg"
        fullWidth
      >
        {viewAnalysis && (
          <>
            <DialogTitle>
              Feedback Analysis: {new Date(viewAnalysis.period.start).toLocaleDateString()} - {new Date(viewAnalysis.period.end).toLocaleDateString()}
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Card variant="outlined" sx={{ height: '100%' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Summary
                      </Typography>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Total Feedback: {viewAnalysis.metrics.totalFeedback}
                      </Typography>
                      <Box sx={{ mt: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2">Positive:</Typography>
                          <Typography variant="body2">{viewAnalysis.metrics.positivePercentage.toFixed(1)}%</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2">Negative:</Typography>
                          <Typography variant="body2">{viewAnalysis.metrics.negativePercentage.toFixed(1)}%</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                          <Typography variant="body2">Neutral:</Typography>
                          <Typography variant="body2">{viewAnalysis.metrics.neutralPercentage.toFixed(1)}%</Typography>
                        </Box>
                      </Box>
                      <Box sx={{ height: 200, mt: 3 }}>
                        <Pie
                          data={{
                            labels: ['Positive', 'Negative', 'Neutral'],
                            datasets: [
                              {
                                data: [
                                  viewAnalysis.metrics.positivePercentage,
                                  viewAnalysis.metrics.negativePercentage,
                                  viewAnalysis.metrics.neutralPercentage,
                                ],
                                backgroundColor: [
                                  'rgba(75, 192, 192, 0.6)',
                                  'rgba(255, 99, 132, 0.6)',
                                  'rgba(201, 203, 207, 0.6)',
                                ],
                                borderColor: [
                                  'rgb(75, 192, 192)',
                                  'rgb(255, 99, 132)',
                                  'rgb(201, 203, 207)',
                                ],
                                borderWidth: 1,
                              },
                            ],
                          }}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                          }}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={8}>
                  <Card variant="outlined" sx={{ height: '100%' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Feedback Trends
                      </Typography>
                      <Box sx={{ height: 300 }}>
                        <Line
                          data={{
                            labels: viewAnalysis.trends.map(t => new Date(t.date).toLocaleDateString()),
                            datasets: [
                              {
                                label: 'Positive',
                                data: viewAnalysis.trends.map(t => t.positiveCount),
                                borderColor: 'rgb(75, 192, 192)',
                                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                                tension: 0.1,
                              },
                              {
                                label: 'Negative',
                                data: viewAnalysis.trends.map(t => t.negativeCount),
                                borderColor: 'rgb(255, 99, 132)',
                                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                tension: 0.1,
                              },
                              {
                                label: 'Neutral',
                                data: viewAnalysis.trends.map(t => t.neutralCount),
                                borderColor: 'rgb(201, 203, 207)',
                                backgroundColor: 'rgba(201, 203, 207, 0.5)',
                                tension: 0.1,
                              },
                            ],
                          }}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                              y: {
                                beginAtZero: true,
                              },
                            },
                          }}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Category Breakdown
                      </Typography>
                      <Box sx={{ height: 300 }}>
                        <Bar
                          data={{
                            labels: Object.keys(viewAnalysis.metrics.categoryBreakdown),
                            datasets: [
                              {
                                label: 'Count',
                                data: Object.values(viewAnalysis.metrics.categoryBreakdown),
                                backgroundColor: [
                                  'rgba(54, 162, 235, 0.6)',
                                  'rgba(255, 206, 86, 0.6)',
                                  'rgba(75, 192, 192, 0.6)',
                                  'rgba(153, 102, 255, 0.6)',
                                  'rgba(255, 159, 64, 0.6)',
                                ],
                                borderColor: [
                                  'rgb(54, 162, 235)',
                                  'rgb(255, 206, 86)',
                                  'rgb(75, 192, 192)',
                                  'rgb(153, 102, 255)',
                                  'rgb(255, 159, 64)',
                                ],
                                borderWidth: 1,
                              },
                            ],
                          }}
                          options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                              y: {
                                beginAtZero: true,
                              },
                            },
                          }}
                        />
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Top Issues
                      </Typography>
                      {viewAnalysis.topIssues.length === 0 ? (
                        <Alert severity="info">No issues identified.</Alert>
                      ) : (
                        <TableContainer>
                          <Table>
                            <TableHead>
                              <TableRow>
                                <TableCell>Category</TableCell>
                                <TableCell>Count</TableCell>
                                <TableCell>Percentage</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {viewAnalysis.topIssues.map((issue) => (
                                <TableRow key={issue.category}>
                                  <TableCell>
                                    <Chip
                                      label={issue.category}
                                      color={getCategoryColor(issue.category) as any}
                                      size="small"
                                    />
                                  </TableCell>
                                  <TableCell>{issue.count}</TableCell>
                                  <TableCell>{issue.percentage.toFixed(1)}%</TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseViewDialog}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default FeedbackAnalysisComponent;