import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Button,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Stack,
  Alert,
  CircularProgress,
  Snackbar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  LinearProgress,
  Tooltip,
} from '@mui/material';
import {
  Add,
  Delete,
  ExpandMore,
  Visibility,
  Compare,
  CheckCircle,
} from '@mui/icons-material';
import axios from 'axios';

interface ConfigComparison {
  id: string;
  name: string;
  description?: string;
  configurations: Array<{
    id: string;
    name: string;
    config: {
      retrieval: {
        maxResults: number;
        minSimilarity: number;
        hybridSearch: boolean;
        rerankResults: boolean;
      };
      generation: {
        maxTokens: number;
        temperature: number;
        systemPrompt: string;
        includeContext: boolean;
        citeSources: boolean;
      };
      conversation: {
        maxHistory: number;
        contextWindow: number;
      };
    };
    metrics: {
      averageBleuScore?: number;
      averageRougeScore?: number;
      averageExactMatchScore?: number;
      averageF1Score?: number;
      averageSemanticSimilarity?: number;
      averageAnswerCorrectness?: number;
      averageCitationAccuracy?: number;
      averageResponseTime?: number;
    };
  }>;
  winner?: string;
  createdAt: Date;
  completedAt?: Date;
  status: 'pending' | 'running' | 'completed' | 'failed';
  error?: string;
}

interface GroundTruthItem {
  id: string;
  question: string;
  tags?: string[];
}

const ConfigComparison: React.FC = () => {
  const [comparisons, setComparisons] = useState<ConfigComparison[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [viewComparison, setViewComparison] = useState<ConfigComparison | null>(null);
  const [groundTruthItems, setGroundTruthItems] = useState<GroundTruthItem[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    configurations: [
      {
        name: 'Configuration 1',
        config: {
          retrieval: {
            maxResults: 5,
            minSimilarity: 0.7,
            hybridSearch: true,
            rerankResults: true,
          },
          generation: {
            maxTokens: 1000,
            temperature: 0.7,
            systemPrompt: 'You are a helpful assistant.',
            includeContext: true,
            citeSources: true,
          },
          conversation: {
            maxHistory: 5,
            contextWindow: 4000,
          },
        },
      },
      {
        name: 'Configuration 2',
        config: {
          retrieval: {
            maxResults: 10,
            minSimilarity: 0.5,
            hybridSearch: false,
            rerankResults: false,
          },
          generation: {
            maxTokens: 1500,
            temperature: 0.5,
            systemPrompt: 'You are a helpful assistant.',
            includeContext: true,
            citeSources: true,
          },
          conversation: {
            maxHistory: 3,
            contextWindow: 3000,
          },
        },
      },
    ],
    groundTruthIds: [] as string[],
    tags: [] as string[],
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });

  useEffect(() => {
    fetchConfigComparisons();
    fetchGroundTruthItems();
  }, []);

  const fetchConfigComparisons = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/evaluation/comparisons');
      setComparisons(response.data);
      setError(null);
    } catch (error) {
      setError('Failed to fetch configuration comparisons');
      console.error('Error fetching configuration comparisons:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchGroundTruthItems = async () => {
    try {
      const response = await axios.get('/api/evaluation/ground-truth');
      setGroundTruthItems(response.data);
    } catch (error) {
      console.error('Error fetching ground truth items:', error);
    }
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleConfigNameChange = (index: number, value: string) => {
    setFormData(prev => {
      const newConfigs = [...prev.configurations];
      newConfigs[index] = { ...newConfigs[index], name: value };
      return { ...prev, configurations: newConfigs };
    });
  };

  const handleConfigChange = (index: number, path: string, value: any) => {
    setFormData(prev => {
      const newConfigs = [...prev.configurations];
      const pathParts = path.split('.');
      
      let current = { ...newConfigs[index].config };
      let temp: any = current;
      
      for (let i = 0; i < pathParts.length - 1; i++) {
        temp[pathParts[i]] = { ...temp[pathParts[i]] };
        temp = temp[pathParts[i]];
      }
      
      temp[pathParts[pathParts.length - 1]] = value;
      newConfigs[index] = { ...newConfigs[index], config: current };
      
      return { ...prev, configurations: newConfigs };
    });
  };

  const handleAddConfiguration = () => {
    setFormData(prev => ({
      ...prev,
      configurations: [
        ...prev.configurations,
        {
          name: `Configuration ${prev.configurations.length + 1}`,
          config: {
            retrieval: {
              maxResults: 5,
              minSimilarity: 0.7,
              hybridSearch: true,
              rerankResults: true,
            },
            generation: {
              maxTokens: 1000,
              temperature: 0.7,
              systemPrompt: 'You are a helpful assistant.',
              includeContext: true,
              citeSources: true,
            },
            conversation: {
              maxHistory: 5,
              contextWindow: 4000,
            },
          },
        },
      ],
    }));
  };

  const handleRemoveConfiguration = (index: number) => {
    if (formData.configurations.length <= 2) {
      setSnackbar({
        open: true,
        message: 'At least two configurations are required',
        severity: 'error',
      });
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      configurations: prev.configurations.filter((_, i) => i !== index),
    }));
  };

  const handleSelectChange = (e: any) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async () => {
    try {
      await axios.post('/api/evaluation/comparisons', formData);
      setSnackbar({
        open: true,
        message: 'Configuration comparison created successfully',
        severity: 'success',
      });
      handleCloseDialog();
      fetchConfigComparisons();
    } catch (error) {
      console.error('Error creating configuration comparison:', error);
      setSnackbar({
        open: true,
        message: 'Failed to create configuration comparison',
        severity: 'error',
      });
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this configuration comparison?')) {
      try {
        await axios.delete(`/api/evaluation/comparisons/${id}`);
        setSnackbar({
          open: true,
          message: 'Configuration comparison deleted successfully',
          severity: 'success',
        });
        fetchConfigComparisons();
      } catch (error) {
        console.error('Error deleting configuration comparison:', error);
        setSnackbar({
          open: true,
          message: 'Failed to delete configuration comparison',
          severity: 'error',
        });
      }
    }
  };

  const handleViewComparison = async (id: string) => {
    try {
      const response = await axios.get(`/api/evaluation/comparisons/${id}`);
      setViewComparison(response.data);
    } catch (error) {
      console.error('Error fetching configuration comparison details:', error);
      setSnackbar({
        open: true,
        message: 'Failed to fetch configuration comparison details',
        severity: 'error',
      });
    }
  };

  const handleCloseViewDialog = () => {
    setViewComparison(null);
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const formatMetricValue = (value: number | undefined) => {
    if (value === undefined) return 'N/A';
    return value.toFixed(2);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'info';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5" component="h2">
          Configuration Comparisons
        </Typography>
        <Button
          startIcon={<Add />}
          variant="contained"
          onClick={handleOpenDialog}
        >
          New Comparison
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Configurations</TableCell>
                <TableCell>Winner</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {comparisons.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No configuration comparisons found. Create one to get started.
                  </TableCell>
                </TableRow>
              ) : (
                comparisons.map((comparison) => (
                  <TableRow key={comparison.id}>
                    <TableCell>{comparison.name}</TableCell>
                    <TableCell>
                      <Chip
                        label={comparison.status}
                        color={getStatusColor(comparison.status) as any}
                        size="small"
                      />
                      {comparison.status === 'running' && (
                        <LinearProgress sx={{ mt: 1 }} />
                      )}
                    </TableCell>
                    <TableCell>{comparison.configurations.length}</TableCell>
                    <TableCell>
                      {comparison.winner ? (
                        <Chip
                          icon={<CheckCircle fontSize="small" />}
                          label={comparison.configurations.find(c => c.id === comparison.winner)?.name || 'Unknown'}
                          color="success"
                          size="small"
                        />
                      ) : (
                        'Not determined'
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(comparison.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handleViewComparison(comparison.id)}
                      >
                        <Visibility />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(comparison.id)}
                      >
                        <Delete />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Create Configuration Comparison Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        <DialogTitle>Create New Configuration Comparison</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="name"
            label="Name"
            type="text"
            fullWidth
            value={formData.name}
            onChange={handleInputChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="description"
            label="Description"
            type="text"
            fullWidth
            multiline
            rows={2}
            value={formData.description}
            onChange={handleInputChange}
            sx={{ mb: 3 }}
          />

          <Typography variant="h6" sx={{ mb: 2 }}>
            Configurations
          </Typography>

          {formData.configurations.map((config, index) => (
            <Accordion key={index} defaultExpanded={index === 0} sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
                  <Typography>{config.name}</Typography>
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveConfiguration(index);
                    }}
                  >
                    <Delete fontSize="small" />
                  </IconButton>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <TextField
                  margin="dense"
                  label="Configuration Name"
                  type="text"
                  fullWidth
                  value={config.name}
                  onChange={(e) => handleConfigNameChange(index, e.target.value)}
                  sx={{ mb: 2 }}
                />

                <Typography variant="subtitle1" sx={{ mb: 1 }}>Retrieval Settings</Typography>
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <TextField
                      label="Max Results"
                      type="number"
                      fullWidth
                      value={config.config.retrieval.maxResults}
                      onChange={(e) => handleConfigChange(index, 'retrieval.maxResults', parseInt(e.target.value))}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Min Similarity"
                      type="number"
                      fullWidth
                      inputProps={{ min: 0, max: 1, step: 0.1 }}
                      value={config.config.retrieval.minSimilarity}
                      onChange={(e) => handleConfigChange(index, 'retrieval.minSimilarity', parseFloat(e.target.value))}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <FormControl fullWidth>
                      <InputLabel>Hybrid Search</InputLabel>
                      <Select
                        value={config.config.retrieval.hybridSearch}
                        onChange={(e) => handleConfigChange(index, 'retrieval.hybridSearch', e.target.value === 'true')}
                      >
                        <MenuItem value="true">Enabled</MenuItem>
                        <MenuItem value="false">Disabled</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={6}>
                    <FormControl fullWidth>
                      <InputLabel>Rerank Results</InputLabel>
                      <Select
                        value={config.config.retrieval.rerankResults}
                        onChange={(e) => handleConfigChange(index, 'retrieval.rerankResults', e.target.value === 'true')}
                      >
                        <MenuItem value="true">Enabled</MenuItem>
                        <MenuItem value="false">Disabled</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" sx={{ mb: 1 }}>Generation Settings</Typography>
                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <TextField
                      label="Max Tokens"
                      type="number"
                      fullWidth
                      value={config.config.generation.maxTokens}
                      onChange={(e) => handleConfigChange(index, 'generation.maxTokens', parseInt(e.target.value))}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Temperature"
                      type="number"
                      fullWidth
                      inputProps={{ min: 0, max: 1, step: 0.1 }}
                      value={config.config.generation.temperature}
                      onChange={(e) => handleConfigChange(index, 'generation.temperature', parseFloat(e.target.value))}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      label="System Prompt"
                      fullWidth
                      multiline
                      rows={2}
                      value={config.config.generation.systemPrompt}
                      onChange={(e) => handleConfigChange(index, 'generation.systemPrompt', e.target.value)}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <FormControl fullWidth>
                      <InputLabel>Include Context</InputLabel>
                      <Select
                        value={config.config.generation.includeContext}
                        onChange={(e) => handleConfigChange(index, 'generation.includeContext', e.target.value === 'true')}
                      >
                        <MenuItem value="true">Enabled</MenuItem>
                        <MenuItem value="false">Disabled</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={6}>
                    <FormControl fullWidth>
                      <InputLabel>Cite Sources</InputLabel>
                      <Select
                        value={config.config.generation.citeSources}
                        onChange={(e) => handleConfigChange(index, 'generation.citeSources', e.target.value === 'true')}
                      >
                        <MenuItem value="true">Enabled</MenuItem>
                        <MenuItem value="false">Disabled</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" sx={{ mb: 1 }}>Conversation Settings</Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <TextField
                      label="Max History"
                      type="number"
                      fullWidth
                      value={config.config.conversation.maxHistory}
                      onChange={(e) => handleConfigChange(index, 'conversation.maxHistory', parseInt(e.target.value))}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      label="Context Window"
                      type="number"
                      fullWidth
                      value={config.config.conversation.contextWindow}
                      onChange={(e) => handleConfigChange(index, 'conversation.contextWindow', parseInt(e.target.value))}
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          ))}

          <Button
            variant="outlined"
            startIcon={<Add />}
            onClick={handleAddConfiguration}
            sx={{ mb: 3 }}
          >
            Add Configuration
          </Button>

          <Divider sx={{ my: 2 }} />

          <Typography variant="h6" sx={{ mb: 2 }}>
            Ground Truth Data
          </Typography>

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Ground Truth Items</InputLabel>
            <Select
              multiple
              name="groundTruthIds"
              value={formData.groundTruthIds}
              onChange={handleSelectChange}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => {
                    const item = groundTruthItems.find(i => i.id === value);
                    return (
                      <Chip key={value} label={item?.question.substring(0, 20) + '...'} />
                    );
                  })}
                </Box>
              )}
            >
              {groundTruthItems.map((item) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.question}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth>
            <InputLabel>Tags</InputLabel>
            <Select
              multiple
              name="tags"
              value={formData.tags}
              onChange={handleSelectChange}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip key={value} label={value} />
                  ))}
                </Box>
              )}
            >
              {Array.from(
                new Set(
                  groundTruthItems
                    .flatMap(item => item.tags || [])
                    .filter(Boolean)
                )
              ).map((tag) => (
                <MenuItem key={tag} value={tag}>
                  {tag}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Configuration Comparison Dialog */}
      <Dialog
        open={!!viewComparison}
        onClose={handleCloseViewDialog}
        maxWidth="lg"
        fullWidth
      >
        {viewComparison && (
          <>
            <DialogTitle>
              Configuration Comparison: {viewComparison.name}
              <Chip
                label={viewComparison.status}
                color={getStatusColor(viewComparison.status) as any}
                size="small"
                sx={{ ml: 2 }}
              />
            </DialogTitle>
            <DialogContent>
              {viewComparison.description && (
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {viewComparison.description}
                </Typography>
              )}

              <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
                Results
              </Typography>

              {viewComparison.status === 'running' && (
                <Box sx={{ mb: 3 }}>
                  <Alert severity="info">
                    Comparison is currently running. Results will be available when completed.
                  </Alert>
                  <LinearProgress sx={{ mt: 2 }} />
                </Box>
              )}

              {viewComparison.status === 'pending' && (
                <Alert severity="info" sx={{ mb: 3 }}>
                  Comparison is pending. Results will be available when completed.
                </Alert>
              )}

              {viewComparison.status === 'failed' && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  Comparison failed: {viewComparison.error || 'Unknown error'}
                </Alert>
              )}

              {viewComparison.status === 'completed' && (
                <Grid container spacing={3}>
                  {viewComparison.configurations.map((config) => (
                    <Grid item xs={12} md={6} key={config.id}>
                      <Card 
                        variant="outlined" 
                        sx={{ 
                          height: '100%',
                          ...(viewComparison.winner === config.id ? {
                            border: '2px solid #4caf50',
                            boxShadow: '0 0 10px rgba(76, 175, 80, 0.3)'
                          } : {})
                        }}
                      >
                        <CardHeader
                          title={config.name}
                          action={
                            viewComparison.winner === config.id && (
                              <Tooltip title="Winner">
                                <CheckCircle color="success" />
                              </Tooltip>
                            )
                          }
                          sx={{
                            bgcolor: viewComparison.winner === config.id ? 'rgba(76, 175, 80, 0.1)' : 'inherit'
                          }}
                        />
                        <CardContent>
                          <Typography variant="subtitle1" gutterBottom>
                            Metrics
                          </Typography>
                          <TableContainer component={Paper} variant="outlined" sx={{ mb: 2 }}>
                            <Table size="small">
                              <TableBody>
                                <TableRow>
                                  <TableCell>F1 Score</TableCell>
                                  <TableCell>{formatMetricValue(config.metrics.averageF1Score)}</TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>Answer Correctness</TableCell>
                                  <TableCell>{formatMetricValue(config.metrics.averageAnswerCorrectness)}</TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>Semantic Similarity</TableCell>
                                  <TableCell>{formatMetricValue(config.metrics.averageSemanticSimilarity)}</TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>Citation Accuracy</TableCell>
                                  <TableCell>{formatMetricValue(config.metrics.averageCitationAccuracy)}</TableCell>
                                </TableRow>
                                <TableRow>
                                  <TableCell>Response Time</TableCell>
                                  <TableCell>
                                    {config.metrics.averageResponseTime !== undefined
                                      ? `${(config.metrics.averageResponseTime / 1000).toFixed(2)}s`
                                      : 'N/A'}
                                  </TableCell>
                                </TableRow>
                              </TableBody>
                            </Table>
                          </TableContainer>

                          <Typography variant="subtitle1" gutterBottom>
                            Configuration
                          </Typography>
                          <Accordion>
                            <AccordionSummary expandIcon={<ExpandMore />}>
                              <Typography>Retrieval Settings</Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                              <TableContainer>
                                <Table size="small">
                                  <TableBody>
                                    <TableRow>
                                      <TableCell>Max Results</TableCell>
                                      <TableCell>{config.config.retrieval.maxResults}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell>Min Similarity</TableCell>
                                      <TableCell>{config.config.retrieval.minSimilarity}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell>Hybrid Search</TableCell>
                                      <TableCell>{config.config.retrieval.hybridSearch ? 'Enabled' : 'Disabled'}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell>Rerank Results</TableCell>
                                      <TableCell>{config.config.retrieval.rerankResults ? 'Enabled' : 'Disabled'}</TableCell>
                                    </TableRow>
                                  </TableBody>
                                </Table>
                              </TableContainer>
                            </AccordionDetails>
                          </Accordion>

                          <Accordion>
                            <AccordionSummary expandIcon={<ExpandMore />}>
                              <Typography>Generation Settings</Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                              <TableContainer>
                                <Table size="small">
                                  <TableBody>
                                    <TableRow>
                                      <TableCell>Max Tokens</TableCell>
                                      <TableCell>{config.config.generation.maxTokens}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell>Temperature</TableCell>
                                      <TableCell>{config.config.generation.temperature}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell>Include Context</TableCell>
                                      <TableCell>{config.config.generation.includeContext ? 'Enabled' : 'Disabled'}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell>Cite Sources</TableCell>
                                      <TableCell>{config.config.generation.citeSources ? 'Enabled' : 'Disabled'}</TableCell>
                                    </TableRow>
                                  </TableBody>
                                </Table>
                              </TableContainer>
                            </AccordionDetails>
                          </Accordion>

                          <Accordion>
                            <AccordionSummary expandIcon={<ExpandMore />}>
                              <Typography>Conversation Settings</Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                              <TableContainer>
                                <Table size="small">
                                  <TableBody>
                                    <TableRow>
                                      <TableCell>Max History</TableCell>
                                      <TableCell>{config.config.conversation.maxHistory}</TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell>Context Window</TableCell>
                                      <TableCell>{config.config.conversation.contextWindow}</TableCell>
                                    </TableRow>
                                  </TableBody>
                                </Table>
                              </TableContainer>
                            </AccordionDetails>
                          </Accordion>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseViewDialog}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert
          onClose={handleCloseSnackbar}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default ConfigComparison;