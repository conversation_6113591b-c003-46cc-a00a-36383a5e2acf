/**
 * Master Integration Test Runner
 * 
 * Orchestrates all integration tests for Task 19
 * Runs system validation, integration tests, load tests, and end-to-end workflow tests
 */

const { runSystemValidation } = require('./validate-complete-system');
const { runIntegrationTests } = require('./test-complete-integration');
const { runLoadTests } = require('./test-load-performance');
const { runEndToEndWorkflowTest } = require('./test-end-to-end-workflow');

class MasterTestRunner {
  constructor() {
    this.testSuites = [
      {
        name: 'System Validation',
        description: 'Validates project structure, dependencies, and configuration',
        runner: runSystemValidation,
        required: true
      },
      {
        name: 'Integration Tests',
        description: 'Tests API endpoints and component integration',
        runner: runIntegrationTests,
        required: true
      },
      {
        name: 'Load Performance Tests',
        description: 'Tests system performance under load',
        runner: runLoadTests,
        required: false
      },
      {
        name: 'End-to-End Workflow Test',
        description: 'Tests complete workflow from scraping to RAG queries',
        runner: runEndToEndWorkflowTest,
        required: true
      }
    ];
    
    this.results = [];
    this.startTime = null;
    this.endTime = null;
  }

  async runAllTests(options = {}) {
    console.log('🚀 DFSA Rulebook RAG System - Master Integration Test Suite');
    console.log('=' .repeat(80));
    console.log('📋 Running comprehensive integration tests for Task 19');
    console.log('🎯 Validating complete system integration and readiness');
    console.log('=' .repeat(80));

    this.startTime = new Date();

    try {
      // Run each test suite
      for (const suite of this.testSuites) {
        if (options.skipOptional && !suite.required) {
          console.log(`\n⏭️  Skipping optional test suite: ${suite.name}`);
          this.results.push({
            name: suite.name,
            status: 'skipped',
            message: 'Skipped (optional)',
            duration: 0
          });
          continue;
        }

        await this.runTestSuite(suite);
      }

      this.endTime = new Date();
      this.printFinalResults();

    } catch (error) {
      console.error('\n❌ Master test suite failed:', error.message);
      this.endTime = new Date();
      this.printFinalResults();
      process.exit(1);
    }
  }

  async runTestSuite(suite) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`🧪 Running: ${suite.name}`);
    console.log(`📝 ${suite.description}`);
    console.log(`${'='.repeat(60)}`);

    const suiteStartTime = Date.now();

    try {
      await suite.runner();
      const duration = Date.now() - suiteStartTime;

      this.results.push({
        name: suite.name,
        status: 'passed',
        message: 'All tests passed successfully',
        duration
      });

      console.log(`\n✅ ${suite.name} completed successfully`);
      console.log(`⏱️  Duration: ${(duration / 1000).toFixed(2)} seconds`);

    } catch (error) {
      const duration = Date.now() - suiteStartTime;

      this.results.push({
        name: suite.name,
        status: 'failed',
        message: error.message,
        duration
      });

      console.log(`\n❌ ${suite.name} failed: ${error.message}`);
      console.log(`⏱️  Duration: ${(duration / 1000).toFixed(2)} seconds`);

      if (suite.required) {
        throw new Error(`Required test suite failed: ${suite.name}`);
      }
    }
  }

  printFinalResults() {
    const totalDuration = this.endTime - this.startTime;
    const passed = this.results.filter(r => r.status === 'passed').length;
    const failed = this.results.filter(r => r.status === 'failed').length;
    const skipped = this.results.filter(r => r.status === 'skipped').length;
    const total = this.results.length;

    console.log('\n' + '='.repeat(80));
    console.log('📊 MASTER INTEGRATION TEST RESULTS');
    console.log('='.repeat(80));

    console.log(`\n🕐 Test Execution Summary:`);
    console.log(`   Start Time: ${this.startTime.toISOString()}`);
    console.log(`   End Time: ${this.endTime.toISOString()}`);
    console.log(`   Total Duration: ${(totalDuration / 1000).toFixed(2)} seconds`);

    console.log(`\n📈 Test Suite Results:`);
    this.results.forEach(result => {
      const statusIcon = {
        'passed': '✅',
        'failed': '❌',
        'skipped': '⏭️'
      }[result.status];

      console.log(`   ${statusIcon} ${result.name}: ${result.message} (${(result.duration / 1000).toFixed(2)}s)`);
    });

    console.log(`\n📊 Summary Statistics:`);
    console.log(`   ✅ Passed: ${passed}/${total}`);
    console.log(`   ❌ Failed: ${failed}/${total}`);
    console.log(`   ⏭️  Skipped: ${skipped}/${total}`);
    console.log(`   📈 Success Rate: ${((passed / (total - skipped)) * 100).toFixed(1)}%`);

    console.log('\n' + '='.repeat(80));

    if (failed === 0) {
      console.log('🎉 ALL INTEGRATION TESTS PASSED!');
      console.log('✨ System is fully integrated and ready for deployment');
      console.log('🚀 Task 19 (System Integration) completed successfully');
      
      console.log('\n🎯 Next Steps:');
      console.log('   1. Deploy to staging environment');
      console.log('   2. Conduct user acceptance testing');
      console.log('   3. Prepare for production deployment');
      
    } else {
      console.log('⚠️  INTEGRATION TESTS FAILED');
      console.log('🔧 Please review and fix the failed test suites before proceeding');
      console.log('📋 Task 19 requires all integration tests to pass');
    }

    console.log('='.repeat(80));
  }

  // Utility method to run specific test suites
  async runSpecificTests(suiteNames) {
    const selectedSuites = this.testSuites.filter(suite => 
      suiteNames.includes(suite.name)
    );

    if (selectedSuites.length === 0) {
      throw new Error('No matching test suites found');
    }

    console.log(`🎯 Running specific test suites: ${suiteNames.join(', ')}`);
    
    for (const suite of selectedSuites) {
      await this.runTestSuite(suite);
    }

    this.printFinalResults();
  }
}

// Command line interface
async function main() {
  const args = process.argv.slice(2);
  const runner = new MasterTestRunner();

  if (args.includes('--help') || args.includes('-h')) {
    console.log('DFSA Rulebook RAG System - Integration Test Runner');
    console.log('\nUsage:');
    console.log('  node run-integration-tests.js [options]');
    console.log('\nOptions:');
    console.log('  --skip-optional    Skip optional test suites (load tests)');
    console.log('  --validation-only  Run only system validation');
    console.log('  --integration-only Run only integration tests');
    console.log('  --load-only        Run only load tests');
    console.log('  --e2e-only         Run only end-to-end tests');
    console.log('  --help, -h         Show this help message');
    console.log('\nTest Suites:');
    runner.testSuites.forEach(suite => {
      const required = suite.required ? '(required)' : '(optional)';
      console.log(`  • ${suite.name} ${required}: ${suite.description}`);
    });
    return;
  }

  try {
    if (args.includes('--validation-only')) {
      await runner.runSpecificTests(['System Validation']);
    } else if (args.includes('--integration-only')) {
      await runner.runSpecificTests(['Integration Tests']);
    } else if (args.includes('--load-only')) {
      await runner.runSpecificTests(['Load Performance Tests']);
    } else if (args.includes('--e2e-only')) {
      await runner.runSpecificTests(['End-to-End Workflow Test']);
    } else {
      const options = {
        skipOptional: args.includes('--skip-optional')
      };
      await runner.runAllTests(options);
    }
  } catch (error) {
    console.error('Test runner failed:', error.message);
    process.exit(1);
  }
}

// Export for use in other files
module.exports = {
  MasterTestRunner,
  main
};

// Run if this file is executed directly
if (require.main === module) {
  main();
}