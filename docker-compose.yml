version: '3.8'

services:
  # Redis for job queues and caching
  redis:
    image: redis:7-alpine
    container_name: dfsa-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL database (alternative to Supabase for local dev)
  postgres:
    image: postgres:15-alpine
    container_name: dfsa-postgres
    environment:
      POSTGRES_DB: dfsa_rag
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Main application
  app:
    build:
      context: .
      target: development
    container_name: dfsa-app
    ports:
      - "3000:3000"
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/dfsa_rag
    volumes:
      - .:/app
      - /app/node_modules
      - /app/frontend/node_modules
      - app_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    command: npm run dev

  # Frontend development server (if running separately)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: dfsa-frontend
    ports:
      - "3001:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:3000
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - app

volumes:
  redis_data:
  postgres_data:
  app_logs: