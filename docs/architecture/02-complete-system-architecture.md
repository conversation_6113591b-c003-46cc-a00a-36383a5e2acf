# Complete System Architecture

## 🎯 Overview

This diagram shows the complete system topology with all components, services, and their relationships in the DFSA Rulebook RAG system.

## 📊 Architecture Diagram

```mermaid
graph TB
    %% External Services & APIs
    subgraph "External Services"
        DFSA[DFSA Rulebook Website<br/>dfsaen.thomsonreuters.com]
        BD[Brightdata Proxy Service<br/>Browser API]
        OPENAI[OpenAI API<br/>GPT-4, Embeddings]
        ANTHROPIC[Anthropic API<br/>Claude Models]
        COHERE[Cohere API<br/>Embeddings]
        PINECONE[Pinecone Vector DB<br/>Semantic Search]
        WEAVIATE[Weaviate Vector DB<br/>Fallback Store]
    end

    %% Frontend Layer
    subgraph "Frontend Layer"
        REACT[React Dashboard<br/>Material-UI]
        SOCKET[Socket.IO Client<br/>Real-time Updates]
        ROUTES[React Router<br/>- Dashboard<br/>- Query Interface<br/>- Job Details<br/>- Evaluation]
    end

    %% API Gateway Layer
    subgraph "API Gateway"
        EXPRESS[Express.js Server<br/>CORS, Helmet, Security]
        SOCKETIO[Socket.IO Server<br/>Real-time Communication]
        HEALTH[Health Check<br/>/health endpoint]
        DOCS[API Documentation<br/>/api-docs OpenAPI]
    end

    %% API Controllers Layer
    subgraph "API Controllers"
        SCRAPE_CTRL[Scrape Controller<br/>/api/scraping/*]
        QUERY_CTRL[Query Controller<br/>/api/query/*]
        CONFIG_CTRL[Config Controller<br/>/api/config/*]
        MIDDLEWARE[Request Logging<br/>Error Handling<br/>Validation]
    end

    %% Core Orchestration Layer
    subgraph "Core Orchestration"
        RAG_ORCH[RAG Orchestrator<br/>- Query Processing<br/>- Context Assembly<br/>- Response Generation<br/>- Conversation Management]
        SCRAPE_ORCH[Scraping Orchestrator<br/>- URL Discovery<br/>- Quality Assurance<br/>- Job Management<br/>- Progress Tracking]
        PROC_PIPE[Processing Pipeline<br/>- Content Processing<br/>- Metadata Extraction<br/>- Document Chunking<br/>- Structure Preservation]
    end

    %% Service Layer
    subgraph "Service Layer"
        subgraph "LLM Services"
            LLM_SVC[LLM Service<br/>Provider Management<br/>Fallback Logic]
            RESP_GEN[Response Generator<br/>Context Assembly<br/>Source Citations]
            LLM_FACTORY[LLM Provider Factory<br/>OpenAI, Anthropic, Local]
        end
        
        subgraph "Vector Services"
            VECTOR_SVC[Vector Store Service<br/>Primary/Fallback<br/>Health Monitoring]
            VECTOR_FACTORY[Vector Store Factory<br/>Pinecone, Weaviate]
        end
        
        subgraph "Embedding Services"
            EMBED_SVC[Embedding Service<br/>Text Vectorization]
            EMBED_FACTORY[Embedding Factory<br/>OpenAI, Cohere, HuggingFace]
        end
    end

    %% Processing Components
    subgraph "Processing Components"
        subgraph "Content Processing"
            CONTENT_PROC[Content Processor<br/>HTML Cleaning<br/>Text Normalization]
            META_EXTRACT[Metadata Extractor<br/>Hierarchical Structure<br/>DFSA-specific Rules]
            DOC_CHUNK[Document Chunker<br/>Semantic Chunking<br/>Overlap Management]
            STRUCT_PRES[Structure Preserver<br/>Relationship Mapping<br/>Context Preservation]
        end
        
        subgraph "Quality Assurance"
            QA_SVC[Quality Assurance<br/>Test Scraping<br/>Manual Confirmation]
            CONTENT_VAL[Content Validator<br/>Quality Metrics<br/>DFSA Validation]
            MANUAL_CONF[Manual Confirmation<br/>Human Review<br/>Quality Gates]
        end
    end

    %% Scraping Components
    subgraph "Scraping Components"
        URL_DISC[URL Discovery Service<br/>Recursive/Comprehensive<br/>Robots.txt Parsing<br/>Sitemap Analysis]
        BRIGHTDATA_CLIENT[Brightdata Client<br/>Proxy Management<br/>Browser Automation<br/>Rate Limiting]
        JOB_MGR[Job Manager<br/>Queue Management<br/>Progress Tracking<br/>Error Handling]
        TEST_SCRAPER[Test Scraper<br/>Sample Validation<br/>Quality Assessment]
    end

    %% RAG Components
    subgraph "RAG Components"
        QUERY_PROC[Query Processor<br/>Query Expansion<br/>Intent Analysis]
        CONTEXT_ASM[Context Assembler<br/>Chunk Ranking<br/>Context Building]
        CONV_MGR[Conversation Manager<br/>History Management<br/>Context Continuity]
        RETRIEVAL[Retrieval Engine<br/>Vector Search<br/>Hybrid Search<br/>Reranking]
    end

    %% Data Layer
    subgraph "Data Layer"
        subgraph "Primary Database (Supabase)"
            SCRAPED_REPO[Scraped Content<br/>Repository]
            JOBS_REPO[Scraping Jobs<br/>Repository]
            URLS_REPO[URLs Repository]
            CHUNKS_REPO[Document Chunks<br/>Repository]
            QUALITY_REPO[Quality Validations<br/>Repository]
            CONFIG_REPO[System Config<br/>Repository]
            GROUND_TRUTH_REPO[Ground Truth<br/>Repository]
        end
        
        subgraph "Vector Databases"
            PINECONE_STORE[Pinecone Store<br/>Primary Vector DB<br/>Semantic Search]
            WEAVIATE_STORE[Weaviate Store<br/>Fallback Vector DB<br/>Graph Capabilities]
        end
    end

    %% Configuration & Utils
    subgraph "Configuration & Utilities"
        ENV_CONFIG[Environment Config<br/>API Keys<br/>Database URLs<br/>Service Settings]
        LOGGER[Logger Service<br/>Structured Logging<br/>Error Tracking]
        ERROR_HANDLER[Error Handling<br/>Type Guards<br/>Graceful Degradation]
    end

    %% Data Flow Connections
    REACT --> EXPRESS
    SOCKET --> SOCKETIO
    EXPRESS --> SCRAPE_CTRL
    EXPRESS --> QUERY_CTRL
    EXPRESS --> CONFIG_CTRL
    
    SCRAPE_CTRL --> SCRAPE_ORCH
    QUERY_CTRL --> RAG_ORCH
    CONFIG_CTRL --> CONFIG_REPO
    
    SCRAPE_ORCH --> URL_DISC
    SCRAPE_ORCH --> QA_SVC
    SCRAPE_ORCH --> JOB_MGR
    SCRAPE_ORCH --> BRIGHTDATA_CLIENT
    
    RAG_ORCH --> QUERY_PROC
    RAG_ORCH --> RETRIEVAL
    RAG_ORCH --> CONTEXT_ASM
    RAG_ORCH --> CONV_MGR
    RAG_ORCH --> RESP_GEN
    
    BRIGHTDATA_CLIENT --> BD
    BRIGHTDATA_CLIENT --> DFSA
    
    QA_SVC --> TEST_SCRAPER
    QA_SVC --> CONTENT_VAL
    QA_SVC --> MANUAL_CONF
    
    SCRAPE_ORCH --> PROC_PIPE
    PROC_PIPE --> CONTENT_PROC
    PROC_PIPE --> META_EXTRACT
    PROC_PIPE --> DOC_CHUNK
    PROC_PIPE --> STRUCT_PRES
    
    DOC_CHUNK --> EMBED_SVC
    EMBED_SVC --> EMBED_FACTORY
    EMBED_FACTORY --> OPENAI
    EMBED_FACTORY --> COHERE
    
    RETRIEVAL --> VECTOR_SVC
    VECTOR_SVC --> VECTOR_FACTORY
    VECTOR_FACTORY --> PINECONE_STORE
    VECTOR_FACTORY --> WEAVIATE_STORE
    PINECONE_STORE --> PINECONE
    WEAVIATE_STORE --> WEAVIATE
    
    RESP_GEN --> LLM_SVC
    LLM_SVC --> LLM_FACTORY
    LLM_FACTORY --> OPENAI
    LLM_FACTORY --> ANTHROPIC
    
    %% Database Connections
    SCRAPED_REPO --> SUPABASE[(Supabase PostgreSQL)]
    JOBS_REPO --> SUPABASE
    URLS_REPO --> SUPABASE
    CHUNKS_REPO --> SUPABASE
    QUALITY_REPO --> SUPABASE
    CONFIG_REPO --> SUPABASE
    GROUND_TRUTH_REPO --> SUPABASE
    
    %% Real-time Updates
    SOCKETIO -.-> JOB_MGR
    SOCKETIO -.-> RAG_ORCH
    SOCKETIO -.-> PROC_PIPE
    
    %% Configuration Flow
    ENV_CONFIG -.-> EXPRESS
    ENV_CONFIG -.-> SCRAPE_ORCH
    ENV_CONFIG -.-> RAG_ORCH
    ENV_CONFIG -.-> LLM_SVC
    ENV_CONFIG -.-> VECTOR_SVC
    ENV_CONFIG -.-> EMBED_SVC
    
    %% Logging Flow
    LOGGER -.-> EXPRESS
    LOGGER -.-> SCRAPE_ORCH
    LOGGER -.-> RAG_ORCH
    LOGGER -.-> PROC_PIPE
    LOGGER -.-> QA_SVC

    %% Styling
    classDef external fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef frontend fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef orchestration fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef processing fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef data fill:#e0f2f1,stroke:#00796b,stroke-width:2px
    classDef config fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class DFSA,BD,OPENAI,ANTHROPIC,COHERE,PINECONE,WEAVIATE external
    class REACT,SOCKET,ROUTES frontend
    class EXPRESS,SOCKETIO,HEALTH,DOCS,SCRAPE_CTRL,QUERY_CTRL,CONFIG_CTRL,MIDDLEWARE api
    class RAG_ORCH,SCRAPE_ORCH,PROC_PIPE orchestration
    class LLM_SVC,RESP_GEN,LLM_FACTORY,VECTOR_SVC,VECTOR_FACTORY,EMBED_SVC,EMBED_FACTORY service
    class CONTENT_PROC,META_EXTRACT,DOC_CHUNK,STRUCT_PRES,QA_SVC,CONTENT_VAL,MANUAL_CONF,URL_DISC,BRIGHTDATA_CLIENT,JOB_MGR,TEST_SCRAPER,QUERY_PROC,CONTEXT_ASM,CONV_MGR,RETRIEVAL processing
    class SCRAPED_REPO,JOBS_REPO,URLS_REPO,CHUNKS_REPO,QUALITY_REPO,CONFIG_REPO,GROUND_TRUTH_REPO,PINECONE_STORE,WEAVIATE_STORE,SUPABASE data
    class ENV_CONFIG,LOGGER,ERROR_HANDLER config
```

## 🏗️ Key Architectural Highlights

### **External Services (7 components)**
- **DFSA Website** - Source of regulatory content
- **Brightdata** - Proxy service for web scraping
- **OpenAI/Anthropic/Cohere** - AI model providers
- **Pinecone/Weaviate** - Vector database providers

### **Frontend Layer (3 components)**
- **React Dashboard** - Main user interface
- **Socket.IO Client** - Real-time updates
- **React Router** - Navigation and routing

### **API Gateway (4 components)**
- **Express.js Server** - HTTP server with security
- **Socket.IO Server** - WebSocket communication
- **Health Check** - System monitoring endpoint
- **API Documentation** - OpenAPI specification

### **Core Orchestration (3 components)**
- **RAG Orchestrator** - Query processing coordination
- **Scraping Orchestrator** - Web scraping workflow management
- **Processing Pipeline** - Content processing coordination

### **Service Layer (9 components)**
- **LLM Services** - Language model management
- **Vector Services** - Vector database operations
- **Embedding Services** - Text vectorization

### **Data Layer (9 repositories + 2 vector stores)**
- **Supabase PostgreSQL** - Primary relational database
- **Pinecone/Weaviate** - Vector storage with fallback

## 🔧 Cross-cutting Concerns

- **Logging & Monitoring** - Structured logging throughout
- **Configuration Management** - Environment-based settings
- **Error Handling** - Graceful degradation and recovery
- **Security** - Authentication, validation, and encryption

## 📊 Component Statistics

- **Total Components**: 60+ individual components
- **External Services**: 7 third-party integrations
- **Internal Services**: 25+ microservices
- **Data Repositories**: 9 database abstractions
- **Processing Components**: 20+ specialized processors
- **Real-time Connections**: WebSocket-based updates
- **Fallback Mechanisms**: Multiple redundancy layers
