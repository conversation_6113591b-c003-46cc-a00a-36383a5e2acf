# Key Functions & Component Interactions

## 🔧 Overview

This document details the 40+ core functions organized by domain and their interactions within the DFSA Rulebook RAG system.

## 📊 Functions & Interactions Diagram

```mermaid
graph LR
    %% User Actions
    subgraph "👤 USER ACTIONS"
        START_JOB[🚀 Start Scraping Job]
        QUERY_RAG[❓ Ask Question]
        MONITOR_JOB[📊 Monitor Progress]
        REVIEW_QA[✅ Review Quality]
    end

    %% Core Functions
    subgraph "🔍 URL DISCOVERY FUNCTIONS"
        PARSE_SITEMAP[📋 Parse Sitemap<br/>- XML parsing<br/>- URL extraction<br/>- Priority scoring]
        
        CRAWL_RECURSIVE[🕷️ Recursive Crawl<br/>- Link following<br/>- Depth limiting<br/>- Duplicate detection]
        
        ROBOTS_CHECK[🤖 Robots.txt Check<br/>- Permission validation<br/>- Crawl delay respect<br/>- User-agent compliance]
        
        URL_FILTER[🔍 URL Filtering<br/>- Pattern matching<br/>- Domain validation<br/>- Content type filtering]
    end

    subgraph "🕷️ SCRAPING FUNCTIONS"
        PROXY_CONNECT[🌍 Proxy Connection<br/>- Brightdata auth<br/>- Session management<br/>- IP rotation]
        
        BROWSER_CONTROL[🤖 Browser Control<br/>- Page navigation<br/>- Element interaction<br/>- Screenshot capture]
        
        CONTENT_EXTRACT[📄 Content Extraction<br/>- HTML parsing<br/>- Text cleaning<br/>- Metadata extraction]
        
        RATE_LIMIT[⏱️ Rate Limiting<br/>- Request throttling<br/>- Delay management<br/>- Queue processing]
    end

    subgraph "⚙️ PROCESSING FUNCTIONS"
        HTML_CLEAN[🧹 HTML Cleaning<br/>- Tag removal<br/>- Script stripping<br/>- Text normalization]
        
        METADATA_EXTRACT[🏷️ Metadata Extraction<br/>- Title extraction<br/>- Header hierarchy<br/>- Link analysis]
        
        SEMANTIC_CHUNK[📝 Semantic Chunking<br/>- Sentence boundary<br/>- Overlap management<br/>- Context preservation]
        
        STRUCTURE_MAP[🗺️ Structure Mapping<br/>- Document hierarchy<br/>- Cross-references<br/>- Relationship tracking]
    end

    subgraph "🧮 EMBEDDING FUNCTIONS"
        TEXT_VECTORIZE[🔢 Text Vectorization<br/>- Token processing<br/>- Embedding generation<br/>- Dimension reduction]
        
        BATCH_PROCESS[📦 Batch Processing<br/>- Chunk batching<br/>- API optimization<br/>- Error handling]
        
        VECTOR_NORMALIZE[📏 Vector Normalization<br/>- L2 normalization<br/>- Dimension validation<br/>- Quality checks]
        
        METADATA_ATTACH[🏷️ Metadata Attachment<br/>- Source tracking<br/>- Timestamp addition<br/>- Category tagging]
    end

    subgraph "💾 STORAGE FUNCTIONS"
        VECTOR_UPSERT[⬆️ Vector Upsert<br/>- Batch insertion<br/>- Duplicate handling<br/>- Index optimization]
        
        METADATA_INDEX[📇 Metadata Indexing<br/>- Searchable fields<br/>- Filter optimization<br/>- Performance tuning]
        
        BACKUP_SYNC[🔄 Backup & Sync<br/>- Cross-store sync<br/>- Consistency checks<br/>- Recovery procedures]
        
        HEALTH_MONITOR[💓 Health Monitoring<br/>- Connection status<br/>- Performance metrics<br/>- Alert generation]
    end

    subgraph "🔍 QUERY FUNCTIONS"
        INTENT_ANALYZE[🧠 Intent Analysis<br/>- Query classification<br/>- Entity extraction<br/>- Context understanding]
        
        QUERY_EXPAND[📈 Query Expansion<br/>- Synonym addition<br/>- Related terms<br/>- Context enrichment]
        
        VECTOR_SEARCH[🎯 Vector Search<br/>- Similarity calculation<br/>- Top-K retrieval<br/>- Score normalization]
        
        RESULT_RERANK[📊 Result Reranking<br/>- Relevance scoring<br/>- Diversity optimization<br/>- Quality filtering]
    end

    subgraph "📝 CONTEXT FUNCTIONS"
        CHUNK_RANK[📊 Chunk Ranking<br/>- Relevance scoring<br/>- Diversity selection<br/>- Quality assessment]
        
        CONTEXT_BUILD[🏗️ Context Building<br/>- Chunk assembly<br/>- Order optimization<br/>- Length management]
        
        SOURCE_TRACK[🔗 Source Tracking<br/>- Citation preparation<br/>- URL preservation<br/>- Metadata linking]
        
        CONTEXT_COMPRESS[🗜️ Context Compression<br/>- Redundancy removal<br/>- Key info extraction<br/>- Length optimization]
    end

    subgraph "🤖 GENERATION FUNCTIONS"
        PROMPT_BUILD[📝 Prompt Building<br/>- Template selection<br/>- Context injection<br/>- Instruction crafting]
        
        LLM_CALL[🧠 LLM API Call<br/>- Provider selection<br/>- Parameter tuning<br/>- Response handling]
        
        RESPONSE_PARSE[📋 Response Parsing<br/>- Answer extraction<br/>- Citation formatting<br/>- Quality validation]
        
        FALLBACK_HANDLE[🔄 Fallback Handling<br/>- Provider switching<br/>- Error recovery<br/>- Quality maintenance]
    end

    subgraph "✅ QUALITY FUNCTIONS"
        TEST_SCRAPE[🧪 Test Scraping<br/>- Sample validation<br/>- Quality assessment<br/>- Performance testing]
        
        CONTENT_VALIDATE[📊 Content Validation<br/>- Quality scoring<br/>- Completeness check<br/>- Accuracy assessment]
        
        MANUAL_REVIEW[👁️ Manual Review<br/>- Human validation<br/>- Quality confirmation<br/>- Approval workflow]
        
        QUALITY_GATE[🚪 Quality Gates<br/>- Threshold checking<br/>- Automated decisions<br/>- Process control]
    end

    subgraph "📊 MONITORING FUNCTIONS"
        METRICS_COLLECT[📈 Metrics Collection<br/>- Performance tracking<br/>- Usage statistics<br/>- Error monitoring]
        
        PROGRESS_TRACK[📊 Progress Tracking<br/>- Job status<br/>- Completion rates<br/>- Time estimation]
        
        ALERT_MANAGE[🚨 Alert Management<br/>- Threshold monitoring<br/>- Notification sending<br/>- Escalation handling]
        
        DASHBOARD_UPDATE[📺 Dashboard Updates<br/>- Real-time data<br/>- Visualization refresh<br/>- Status broadcasting]
    end

    %% Function Interactions
    START_JOB --> PARSE_SITEMAP
    START_JOB --> CRAWL_RECURSIVE
    PARSE_SITEMAP --> URL_FILTER
    CRAWL_RECURSIVE --> ROBOTS_CHECK
    URL_FILTER --> TEST_SCRAPE
    
    TEST_SCRAPE --> PROXY_CONNECT
    PROXY_CONNECT --> BROWSER_CONTROL
    BROWSER_CONTROL --> CONTENT_EXTRACT
    CONTENT_EXTRACT --> HTML_CLEAN
    
    HTML_CLEAN --> METADATA_EXTRACT
    METADATA_EXTRACT --> SEMANTIC_CHUNK
    SEMANTIC_CHUNK --> STRUCTURE_MAP
    STRUCTURE_MAP --> TEXT_VECTORIZE
    
    TEXT_VECTORIZE --> BATCH_PROCESS
    BATCH_PROCESS --> VECTOR_NORMALIZE
    VECTOR_NORMALIZE --> METADATA_ATTACH
    METADATA_ATTACH --> VECTOR_UPSERT
    
    VECTOR_UPSERT --> METADATA_INDEX
    METADATA_INDEX --> BACKUP_SYNC
    BACKUP_SYNC --> HEALTH_MONITOR
    
    QUERY_RAG --> INTENT_ANALYZE
    INTENT_ANALYZE --> QUERY_EXPAND
    QUERY_EXPAND --> VECTOR_SEARCH
    VECTOR_SEARCH --> RESULT_RERANK
    
    RESULT_RERANK --> CHUNK_RANK
    CHUNK_RANK --> CONTEXT_BUILD
    CONTEXT_BUILD --> SOURCE_TRACK
    SOURCE_TRACK --> CONTEXT_COMPRESS
    
    CONTEXT_COMPRESS --> PROMPT_BUILD
    PROMPT_BUILD --> LLM_CALL
    LLM_CALL --> RESPONSE_PARSE
    RESPONSE_PARSE --> FALLBACK_HANDLE
    
    CONTENT_VALIDATE --> QUALITY_GATE
    MANUAL_REVIEW --> QUALITY_GATE
    QUALITY_GATE --> METRICS_COLLECT
    
    MONITOR_JOB --> PROGRESS_TRACK
    PROGRESS_TRACK --> DASHBOARD_UPDATE
    METRICS_COLLECT --> ALERT_MANAGE
    ALERT_MANAGE --> DASHBOARD_UPDATE
    
    REVIEW_QA --> MANUAL_REVIEW
    
    %% Cross-function dependencies
    RATE_LIMIT -.-> PROXY_CONNECT
    HEALTH_MONITOR -.-> ALERT_MANAGE
    FALLBACK_HANDLE -.-> HEALTH_MONITOR
    QUALITY_GATE -.-> PROGRESS_TRACK

    %% Styling
    classDef user fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    classDef discovery fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef scraping fill:#fce4ec,stroke:#ad1457,stroke-width:2px
    classDef processing fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef embedding fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef storage fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef query fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef context fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    classDef generation fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef quality fill:#fafafa,stroke:#424242,stroke-width:2px
    classDef monitoring fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px

    class START_JOB,QUERY_RAG,MONITOR_JOB,REVIEW_QA user
    class PARSE_SITEMAP,CRAWL_RECURSIVE,ROBOTS_CHECK,URL_FILTER discovery
    class PROXY_CONNECT,BROWSER_CONTROL,CONTENT_EXTRACT,RATE_LIMIT scraping
    class HTML_CLEAN,METADATA_EXTRACT,SEMANTIC_CHUNK,STRUCTURE_MAP processing
    class TEXT_VECTORIZE,BATCH_PROCESS,VECTOR_NORMALIZE,METADATA_ATTACH embedding
    class VECTOR_UPSERT,METADATA_INDEX,BACKUP_SYNC,HEALTH_MONITOR storage
    class INTENT_ANALYZE,QUERY_EXPAND,VECTOR_SEARCH,RESULT_RERANK query
    class CHUNK_RANK,CONTEXT_BUILD,SOURCE_TRACK,CONTEXT_COMPRESS context
    class PROMPT_BUILD,LLM_CALL,RESPONSE_PARSE,FALLBACK_HANDLE generation
    class TEST_SCRAPE,CONTENT_VALIDATE,MANUAL_REVIEW,QUALITY_GATE quality
    class METRICS_COLLECT,PROGRESS_TRACK,ALERT_MANAGE,DASHBOARD_UPDATE monitoring
```

## 🔍 Function Categories

### **👤 User Actions (4 functions)**
Core user interactions that trigger system workflows:

1. **🚀 Start Scraping Job** - Initiates web scraping workflow
2. **❓ Ask Question** - Triggers RAG query processing
3. **📊 Monitor Progress** - Tracks job status and system health
4. **✅ Review Quality** - Manual quality assurance review

### **🔍 URL Discovery Functions (4 functions)**
Intelligent URL discovery and validation:

1. **📋 Parse Sitemap** - XML sitemap parsing and URL extraction
2. **🕷️ Recursive Crawl** - Link following with depth control
3. **🤖 Robots.txt Check** - Crawling permission validation
4. **🔍 URL Filtering** - Pattern matching and content validation

### **🕷️ Scraping Functions (4 functions)**
Web scraping and content extraction:

1. **🌍 Proxy Connection** - Brightdata proxy management
2. **🤖 Browser Control** - Puppeteer browser automation
3. **📄 Content Extraction** - HTML parsing and text extraction
4. **⏱️ Rate Limiting** - Request throttling and queue management

### **⚙️ Processing Functions (4 functions)**
Content processing and preparation:

1. **🧹 HTML Cleaning** - Tag removal and text normalization
2. **🏷️ Metadata Extraction** - Document structure analysis
3. **📝 Semantic Chunking** - Intelligent content segmentation
4. **🗺️ Structure Mapping** - Document relationship tracking

### **🧮 Embedding Functions (4 functions)**
Text vectorization and preparation:

1. **🔢 Text Vectorization** - Token processing and embedding generation
2. **📦 Batch Processing** - API optimization and error handling
3. **📏 Vector Normalization** - L2 normalization and validation
4. **🏷️ Metadata Attachment** - Source tracking and categorization

### **💾 Storage Functions (4 functions)**
Vector storage and database operations:

1. **⬆️ Vector Upsert** - Batch insertion and duplicate handling
2. **📇 Metadata Indexing** - Searchable field optimization
3. **🔄 Backup & Sync** - Cross-store synchronization
4. **💓 Health Monitoring** - Connection status and performance

### **🔍 Query Functions (4 functions)**
Query processing and optimization:

1. **🧠 Intent Analysis** - Query classification and entity extraction
2. **📈 Query Expansion** - Synonym addition and context enrichment
3. **🎯 Vector Search** - Similarity calculation and retrieval
4. **📊 Result Reranking** - Relevance scoring and optimization

### **📝 Context Functions (4 functions)**
Context assembly and optimization:

1. **📊 Chunk Ranking** - Relevance scoring and selection
2. **🏗️ Context Building** - Chunk assembly and optimization
3. **🔗 Source Tracking** - Citation preparation and linking
4. **🗜️ Context Compression** - Redundancy removal and optimization

### **🤖 Generation Functions (4 functions)**
Response generation and handling:

1. **📝 Prompt Building** - Template selection and context injection
2. **🧠 LLM API Call** - Provider selection and response handling
3. **📋 Response Parsing** - Answer extraction and formatting
4. **🔄 Fallback Handling** - Provider switching and error recovery

### **✅ Quality Functions (4 functions)**
Quality assurance and validation:

1. **🧪 Test Scraping** - Sample validation and assessment
2. **📊 Content Validation** - Quality scoring and completeness
3. **👁️ Manual Review** - Human validation and confirmation
4. **🚪 Quality Gates** - Threshold checking and process control

### **📊 Monitoring Functions (4 functions)**
System monitoring and alerting:

1. **📈 Metrics Collection** - Performance tracking and statistics
2. **📊 Progress Tracking** - Job status and completion rates
3. **🚨 Alert Management** - Threshold monitoring and notifications
4. **📺 Dashboard Updates** - Real-time data and visualization

## 🔄 Function Interaction Patterns

### **Sequential Processing Chains**
Functions that execute in sequence for data transformation:

**Scraping Chain**:
`URL Discovery → Test Scraping → Full Scraping → Content Processing → Chunking → Embedding → Storage`

**RAG Chain**:
`Query Input → Intent Analysis → Vector Search → Context Building → LLM Generation → Response Formatting`

### **Parallel Processing Groups**
Functions that can execute concurrently:

- **URL Discovery** - Multiple discovery methods run in parallel
- **Content Processing** - Batch processing of multiple documents
- **Vector Operations** - Parallel embedding generation and storage
- **Quality Checks** - Concurrent validation processes

### **Feedback Loops**
Functions that create feedback mechanisms:

- **Quality Gates** → **Progress Tracking** → **Dashboard Updates**
- **Health Monitoring** → **Alert Management** → **System Adjustments**
- **Error Handling** → **Fallback Mechanisms** → **Recovery Procedures**

### **Cross-cutting Dependencies**
Functions that support multiple workflows:

- **Rate Limiting** - Controls all external API calls
- **Health Monitoring** - Monitors all system components
- **Error Handling** - Provides recovery for all operations
- **Metrics Collection** - Tracks performance across all functions

## 📊 Function Performance Characteristics

### **High-Frequency Functions**
Functions called frequently during normal operation:

- **Vector Search** - Every RAG query
- **Content Extraction** - Every scraped page
- **Progress Tracking** - Continuous monitoring
- **Health Monitoring** - Regular health checks

### **Batch Processing Functions**
Functions optimized for bulk operations:

- **Embedding Generation** - Batch API calls
- **Vector Upsert** - Bulk database operations
- **Content Processing** - Parallel document processing
- **Metadata Indexing** - Batch index updates

### **Critical Path Functions**
Functions that directly impact user experience:

- **Query Processing** - RAG response time
- **LLM Generation** - Answer quality and speed
- **Vector Search** - Retrieval accuracy
- **Response Formatting** - Final answer presentation

### **Background Functions**
Functions that run asynchronously:

- **Backup & Sync** - Data consistency maintenance
- **Metrics Collection** - Performance data gathering
- **Alert Management** - Proactive monitoring
- **Quality Validation** - Continuous quality assessment
