# Layered Architecture & Abstractions

## 🏛️ Overview

This document describes the 7-layer architecture of the DFSA Rulebook RAG system, showing clear separation of concerns and abstraction layers.

## 📊 Layered Architecture Diagram

```mermaid
graph TB
    %% Layer 1: Presentation Layer
    subgraph "🎨 PRESENTATION LAYER"
        subgraph "Frontend Applications"
            REACT_APP[React Dashboard<br/>📊 Material-UI Components<br/>🔍 Query Interface<br/>📋 Job Management<br/>📈 Analytics Dashboard]
            MOBILE_APP[Mobile App<br/>📱 React Native<br/>(Future Extension)]
        end
        
        subgraph "Real-time Communication"
            WEBSOCKET_CLIENT[WebSocket Client<br/>⚡ Socket.IO<br/>📡 Real-time Updates<br/>🔄 Progress Tracking]
        end
    end

    %% Layer 2: API Gateway Layer
    subgraph "🌐 API GATEWAY LAYER"
        subgraph "HTTP Server"
            EXPRESS_SERVER[Express.js Server<br/>🛡️ Security (Helmet, CORS)<br/>📝 Request Logging<br/>⚖️ Rate Limiting<br/>🔐 Authentication]
        end
        
        subgraph "API Controllers"
            REST_CONTROLLERS[REST Controllers<br/>🕷️ Scraping Controller<br/>🤖 Query Controller<br/>⚙️ Config Controller<br/>📊 Analytics Controller]
        end
        
        subgraph "WebSocket Server"
            SOCKETIO_SERVER[Socket.IO Server<br/>⚡ Real-time Events<br/>📡 Progress Broadcasting<br/>🔄 Status Updates]
        end
    end

    %% Layer 3: Business Logic Layer
    subgraph "🧠 BUSINESS LOGIC LAYER"
        subgraph "Core Orchestrators"
            RAG_ORCHESTRATOR[RAG Orchestrator<br/>🤖 Query Processing<br/>🔍 Context Assembly<br/>💬 Conversation Management<br/>📊 Metrics Tracking]
            
            SCRAPING_ORCHESTRATOR[Scraping Orchestrator<br/>🕷️ Workflow Management<br/>🔍 URL Discovery<br/>✅ Quality Assurance<br/>📋 Job Coordination]
        end
        
        subgraph "Processing Pipelines"
            CONTENT_PIPELINE[Content Processing Pipeline<br/>⚙️ HTML Cleaning<br/>📄 Text Extraction<br/>🏷️ Metadata Extraction<br/>📝 Structure Preservation]
            
            RAG_PIPELINE[RAG Processing Pipeline<br/>🔍 Query Analysis<br/>🎯 Vector Retrieval<br/>📝 Context Building<br/>🧠 Response Generation]
        end
        
        subgraph "Quality Management"
            QA_MANAGER[Quality Assurance Manager<br/>🧪 Test Scraping<br/>📊 Quality Metrics<br/>✅ Manual Confirmation<br/>🚪 Quality Gates]
        end
    end

    %% Layer 4: Service Layer
    subgraph "⚙️ SERVICE LAYER"
        subgraph "AI Services"
            LLM_SERVICE[LLM Service<br/>🤖 Provider Management<br/>🔄 Fallback Logic<br/>⚡ Response Generation<br/>📊 Performance Monitoring]
            
            EMBEDDING_SERVICE[Embedding Service<br/>🧮 Text Vectorization<br/>📦 Batch Processing<br/>🔄 Provider Switching<br/>💾 Caching]
            
            VECTOR_SERVICE[Vector Store Service<br/>🔍 Semantic Search<br/>💾 Vector Management<br/>🔄 Fallback Handling<br/>📊 Health Monitoring]
        end
        
        subgraph "Content Services"
            SCRAPING_SERVICE[Scraping Service<br/>🌍 Proxy Management<br/>🤖 Browser Automation<br/>⏱️ Rate Limiting<br/>🔄 Retry Logic]
            
            PROCESSING_SERVICE[Processing Service<br/>📄 Document Chunking<br/>🏷️ Metadata Extraction<br/>🔗 Structure Linking<br/>✅ Quality Validation]
        end
        
        subgraph "Job Management"
            JOB_SERVICE[Job Management Service<br/>📋 Queue Management<br/>📊 Progress Tracking<br/>🔄 Error Handling<br/>⏸️ Pause/Resume]
        end
    end

    %% Layer 5: Integration Layer
    subgraph "🔌 INTEGRATION LAYER"
        subgraph "Provider Factories"
            LLM_FACTORY[LLM Provider Factory<br/>🏭 OpenAI Provider<br/>🏭 Anthropic Provider<br/>🏭 Local Model Provider<br/>🔧 Configuration Management]
            
            VECTOR_FACTORY[Vector Store Factory<br/>🏭 Pinecone Provider<br/>🏭 Weaviate Provider<br/>🏭 Chroma Provider<br/>🔧 Connection Management]
            
            EMBEDDING_FACTORY[Embedding Factory<br/>🏭 OpenAI Embeddings<br/>🏭 Cohere Embeddings<br/>🏭 HuggingFace Models<br/>🔧 Model Management]
        end
        
        subgraph "External Adapters"
            BRIGHTDATA_ADAPTER[Brightdata Adapter<br/>🌍 Proxy Integration<br/>🤖 Browser API<br/>🔐 Authentication<br/>📊 Usage Monitoring]
            
            DATABASE_ADAPTER[Database Adapter<br/>🗄️ Supabase Integration<br/>📊 Repository Pattern<br/>🔄 Connection Pooling<br/>📈 Query Optimization]
        end
    end

    %% Layer 6: Data Access Layer
    subgraph "💾 DATA ACCESS LAYER"
        subgraph "Repository Pattern"
            CONTENT_REPO[Content Repository<br/>📄 Scraped Content<br/>🔍 Search & Filter<br/>📊 Statistics<br/>🗂️ Archival]
            
            JOB_REPO[Job Repository<br/>📋 Job Management<br/>📊 Status Tracking<br/>📈 Analytics<br/>🔄 State Management]
            
            CONFIG_REPO[Configuration Repository<br/>⚙️ System Settings<br/>🔧 Feature Flags<br/>📊 Monitoring Config<br/>🔐 Security Settings]
        end
        
        subgraph "Vector Repositories"
            VECTOR_REPO[Vector Repository<br/>🧮 Embedding Storage<br/>🔍 Similarity Search<br/>🏷️ Metadata Indexing<br/>📊 Performance Metrics]
        end
    end

    %% Layer 7: Infrastructure Layer
    subgraph "🏗️ INFRASTRUCTURE LAYER"
        subgraph "Primary Storage"
            SUPABASE_DB[(Supabase PostgreSQL<br/>🗄️ Relational Data<br/>🔐 Row Level Security<br/>📊 Real-time Subscriptions<br/>🔄 Automatic Backups)]
        end
        
        subgraph "Vector Storage"
            PINECONE_DB[(Pinecone Vector DB<br/>🔍 Semantic Search<br/>⚡ Low Latency<br/>📈 Auto Scaling<br/>🔧 Managed Service)]
            
            WEAVIATE_DB[(Weaviate Vector DB<br/>🕸️ Graph Capabilities<br/>🔍 Hybrid Search<br/>🤖 ML Integration<br/>🔄 Fallback Storage)]
        end
        
        subgraph "External APIs"
            OPENAI_API[(OpenAI API<br/>🤖 GPT-4 Models<br/>🧮 Embeddings<br/>📊 Usage Tracking<br/>🔐 API Key Management)]
            
            ANTHROPIC_API[(Anthropic API<br/>🧠 Claude Models<br/>📝 Long Context<br/>🔐 Safety Features<br/>⚡ Fast Inference)]
            
            BRIGHTDATA_API[(Brightdata API<br/>🌍 Global Proxies<br/>🤖 Browser Automation<br/>🔄 Session Management<br/>📊 Usage Analytics)]
        end
    end

    %% Cross-cutting Concerns
    subgraph "🔧 CROSS-CUTTING CONCERNS"
        LOGGING[Logging & Monitoring<br/>📝 Structured Logging<br/>📊 Performance Metrics<br/>🚨 Error Tracking<br/>📈 Analytics]
        
        SECURITY[Security & Auth<br/>🔐 API Authentication<br/>🛡️ Input Validation<br/>🔒 Data Encryption<br/>🚨 Threat Detection]
        
        CONFIG_MGT[Configuration Management<br/>⚙️ Environment Variables<br/>🔧 Feature Flags<br/>📊 Runtime Configuration<br/>🔄 Hot Reloading]
        
        ERROR_HANDLING[Error Handling<br/>🚨 Exception Management<br/>🔄 Retry Logic<br/>📊 Error Reporting<br/>🛡️ Graceful Degradation]
    end

    %% Layer Connections
    REACT_APP --> EXPRESS_SERVER
    WEBSOCKET_CLIENT --> SOCKETIO_SERVER
    EXPRESS_SERVER --> REST_CONTROLLERS
    REST_CONTROLLERS --> RAG_ORCHESTRATOR
    REST_CONTROLLERS --> SCRAPING_ORCHESTRATOR
    
    RAG_ORCHESTRATOR --> RAG_PIPELINE
    SCRAPING_ORCHESTRATOR --> CONTENT_PIPELINE
    RAG_ORCHESTRATOR --> LLM_SERVICE
    SCRAPING_ORCHESTRATOR --> SCRAPING_SERVICE
    
    LLM_SERVICE --> LLM_FACTORY
    VECTOR_SERVICE --> VECTOR_FACTORY
    EMBEDDING_SERVICE --> EMBEDDING_FACTORY
    SCRAPING_SERVICE --> BRIGHTDATA_ADAPTER
    
    LLM_FACTORY --> OPENAI_API
    LLM_FACTORY --> ANTHROPIC_API
    VECTOR_FACTORY --> PINECONE_DB
    VECTOR_FACTORY --> WEAVIATE_DB
    BRIGHTDATA_ADAPTER --> BRIGHTDATA_API
    
    CONTENT_REPO --> SUPABASE_DB
    JOB_REPO --> SUPABASE_DB
    CONFIG_REPO --> SUPABASE_DB
    VECTOR_REPO --> PINECONE_DB
    
    %% Cross-cutting connections (dotted lines)
    LOGGING -.-> EXPRESS_SERVER
    LOGGING -.-> RAG_ORCHESTRATOR
    LOGGING -.-> SCRAPING_ORCHESTRATOR
    SECURITY -.-> EXPRESS_SERVER
    SECURITY -.-> REST_CONTROLLERS
    CONFIG_MGT -.-> LLM_SERVICE
    CONFIG_MGT -.-> VECTOR_SERVICE
    ERROR_HANDLING -.-> RAG_ORCHESTRATOR
    ERROR_HANDLING -.-> SCRAPING_ORCHESTRATOR

    %% Styling
    classDef presentation fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef business fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef service fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef integration fill:#fce4ec,stroke:#ad1457,stroke-width:2px
    classDef data fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef infrastructure fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef crosscutting fill:#f1f8e9,stroke:#558b2f,stroke-width:2px

    class REACT_APP,MOBILE_APP,WEBSOCKET_CLIENT presentation
    class EXPRESS_SERVER,REST_CONTROLLERS,SOCKETIO_SERVER api
    class RAG_ORCHESTRATOR,SCRAPING_ORCHESTRATOR,CONTENT_PIPELINE,RAG_PIPELINE,QA_MANAGER business
    class LLM_SERVICE,EMBEDDING_SERVICE,VECTOR_SERVICE,SCRAPING_SERVICE,PROCESSING_SERVICE,JOB_SERVICE service
    class LLM_FACTORY,VECTOR_FACTORY,EMBEDDING_FACTORY,BRIGHTDATA_ADAPTER,DATABASE_ADAPTER integration
    class CONTENT_REPO,JOB_REPO,CONFIG_REPO,VECTOR_REPO data
    class SUPABASE_DB,PINECONE_DB,WEAVIATE_DB,OPENAI_API,ANTHROPIC_API,BRIGHTDATA_API infrastructure
    class LOGGING,SECURITY,CONFIG_MGT,ERROR_HANDLING crosscutting
```

## 🏗️ Layer Descriptions

### **Layer 1: 🎨 Presentation Layer**
**Purpose**: User interface and client-side interactions

**Components**:
- **React Dashboard** - Main web application with Material-UI
- **Query Interface** - Natural language query processing UI
- **Job Management** - Scraping job monitoring and control
- **WebSocket Client** - Real-time updates and notifications
- **Mobile App** - Future React Native extension

**Responsibilities**:
- User interaction handling
- Data visualization and presentation
- Real-time status updates
- Responsive design and accessibility

### **Layer 2: 🌐 API Gateway Layer**
**Purpose**: Request routing, security, and protocol handling

**Components**:
- **Express.js Server** - HTTP server with security middleware
- **REST Controllers** - API endpoint implementations
- **Socket.IO Server** - WebSocket communication management
- **Authentication** - User authentication and authorization

**Responsibilities**:
- Request routing and validation
- Security enforcement (CORS, Helmet, rate limiting)
- Protocol translation (HTTP/WebSocket)
- API documentation and versioning

### **Layer 3: 🧠 Business Logic Layer**
**Purpose**: Core business rules and workflow orchestration

**Components**:
- **RAG Orchestrator** - Query processing coordination
- **Scraping Orchestrator** - Web scraping workflow management
- **Processing Pipelines** - Content and RAG processing coordination
- **Quality Assurance Manager** - Quality control and validation

**Responsibilities**:
- Business rule enforcement
- Workflow coordination
- Decision making logic
- Quality assurance processes

### **Layer 4: ⚙️ Service Layer**
**Purpose**: Reusable business services and operations

**Components**:
- **AI Services** - LLM, embedding, and vector services
- **Content Services** - Scraping and processing services
- **Job Management** - Queue and progress management

**Responsibilities**:
- Service composition and coordination
- Provider management and fallback logic
- Performance monitoring and optimization
- Resource management and scaling

### **Layer 5: 🔌 Integration Layer**
**Purpose**: External system integration and abstraction

**Components**:
- **Provider Factories** - AI provider abstraction
- **External Adapters** - Third-party service integration
- **Configuration Management** - Provider configuration

**Responsibilities**:
- External API abstraction
- Provider switching and fallback
- Connection management and pooling
- Configuration and credential management

### **Layer 6: 💾 Data Access Layer**
**Purpose**: Data persistence and retrieval abstraction

**Components**:
- **Repository Pattern** - Database abstraction
- **Vector Repositories** - Vector database operations
- **Data Models** - Entity definitions and relationships

**Responsibilities**:
- Data access abstraction
- Query optimization and caching
- Transaction management
- Data consistency and integrity

### **Layer 7: 🏗️ Infrastructure Layer**
**Purpose**: Physical storage and external services

**Components**:
- **Supabase PostgreSQL** - Primary relational database
- **Vector Databases** - Pinecone and Weaviate
- **External APIs** - OpenAI, Anthropic, Brightdata

**Responsibilities**:
- Data storage and retrieval
- External service communication
- Infrastructure monitoring and health
- Backup and disaster recovery

## 🔧 Cross-cutting Concerns

### **Logging & Monitoring**
- **Structured Logging** - Consistent log format across layers
- **Performance Metrics** - Response times and throughput tracking
- **Error Tracking** - Exception monitoring and alerting
- **Analytics** - Business intelligence and reporting

### **Security & Authentication**
- **API Authentication** - Token-based authentication
- **Input Validation** - Request sanitization and validation
- **Data Encryption** - Secure data transmission and storage
- **Threat Detection** - Security monitoring and alerting

### **Configuration Management**
- **Environment Variables** - Environment-specific configuration
- **Feature Flags** - Runtime feature toggling
- **Runtime Configuration** - Dynamic configuration updates
- **Hot Reloading** - Configuration changes without restart

### **Error Handling**
- **Exception Management** - Centralized error handling
- **Retry Logic** - Automatic retry with backoff
- **Error Reporting** - Detailed error information
- **Graceful Degradation** - Fallback mechanisms

## 📊 Layer Benefits

### **Separation of Concerns**
- Each layer has a single, well-defined responsibility
- Changes in one layer don't affect others
- Easier testing and maintenance
- Clear dependency direction (top-down)

### **Scalability**
- Individual layers can be scaled independently
- Horizontal scaling at service and infrastructure layers
- Load balancing and resource optimization
- Performance monitoring at each layer

### **Maintainability**
- Clear boundaries and interfaces
- Easier debugging and troubleshooting
- Modular development and deployment
- Technology stack flexibility

### **Testability**
- Unit testing at each layer
- Integration testing between layers
- Mock implementations for external dependencies
- Comprehensive test coverage

## 🔄 Data Flow Between Layers

### **Request Flow (Top-Down)**
1. **Presentation** → API Gateway → Business Logic → Service → Integration → Data Access → Infrastructure
2. Each layer validates and transforms data before passing to the next
3. Error handling and logging at each layer
4. Response flows back up through the same layers

### **Event Flow (Bottom-Up)**
1. **Infrastructure** events (database changes, API responses)
2. **Data Access** layer processes and normalizes events
3. **Service** layer applies business logic to events
4. **Business Logic** layer coordinates responses
5. **API Gateway** broadcasts events via WebSocket
6. **Presentation** layer updates UI in real-time
