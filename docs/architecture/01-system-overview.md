# DFSA Rulebook RAG System - Architecture Overview

## 🏗️ System Architecture Documentation

This document provides a comprehensive overview of the DFSA Rulebook RAG (Retrieval-Augmented Generation) system architecture, including all key components, data flows, layers, abstractions, and interactions.

## 📋 Table of Contents

1. [Complete System Architecture](#complete-system-architecture)
2. [Data Flow & Processing Pipeline](#data-flow--processing-pipeline)
3. [Layered Architecture & Abstractions](#layered-architecture--abstractions)
4. [Key Functions & Component Interactions](#key-functions--component-interactions)
5. [Architectural Strengths](#architectural-strengths)

## 🎯 System Purpose

The DFSA Rulebook RAG system is designed to:
- **Scrape** regulatory content from the DFSA (Dubai Financial Services Authority) website
- **Process** and chunk the content for optimal retrieval
- **Generate embeddings** and store them in vector databases
- **Provide intelligent Q&A** capabilities using RAG methodology
- **Ensure quality** through comprehensive validation and monitoring

## 🏛️ High-Level Architecture

The system follows a **7-layer architecture** with clear separation of concerns:

1. **🎨 Presentation Layer** - User interfaces and real-time communication
2. **🌐 API Gateway Layer** - HTTP/WebSocket servers and controllers
3. **🧠 Business Logic Layer** - Core orchestrators and processing pipelines
4. **⚙️ Service Layer** - AI services, content services, and job management
5. **🔌 Integration Layer** - Provider factories and external adapters
6. **💾 Data Access Layer** - Repository pattern and data abstractions
7. **🏗️ Infrastructure Layer** - Databases, external APIs, and storage

## 🔧 Core Components

### **External Services**
- **DFSA Website** - Source of regulatory content
- **Brightdata** - Proxy service for web scraping
- **OpenAI** - GPT-4 models and embeddings
- **Anthropic** - Claude models for fallback
- **Cohere** - Alternative embedding provider
- **Pinecone** - Primary vector database
- **Weaviate** - Fallback vector database

### **Frontend Applications**
- **React Dashboard** - Main user interface with Material-UI
- **Query Interface** - Natural language query processing
- **Job Management** - Scraping job monitoring and control
- **Analytics Dashboard** - System metrics and performance

### **Core Orchestrators**
- **RAG Orchestrator** - Coordinates query processing and response generation
- **Scraping Orchestrator** - Manages web scraping workflows
- **Processing Pipeline** - Handles content processing and chunking

### **Service Layer**
- **LLM Service** - Language model management with fallback
- **Vector Store Service** - Vector database operations with redundancy
- **Embedding Service** - Text vectorization with multiple providers
- **Job Management Service** - Queue and progress management

## 📊 Key Metrics & Monitoring

The system includes comprehensive monitoring across all layers:
- **Performance Metrics** - Response times, throughput, error rates
- **Quality Metrics** - Content quality scores, validation results
- **System Health** - Service availability, resource utilization
- **Business Metrics** - Query success rates, user satisfaction

## 🔐 Security & Compliance

Security is implemented at multiple levels:
- **API Authentication** - Secure access control
- **Input Validation** - Comprehensive data sanitization
- **Rate Limiting** - Protection against abuse
- **Audit Logging** - Complete activity tracking
- **Data Encryption** - Secure data transmission and storage

## 🚀 Scalability Features

The architecture supports horizontal and vertical scaling:
- **Microservices Design** - Independent component scaling
- **Async Processing** - Non-blocking operations
- **Connection Pooling** - Efficient resource utilization
- **Caching Layers** - Performance optimization
- **Load Balancing** - Traffic distribution

---

*For detailed diagrams and technical specifications, see the individual architecture documents in this folder.*
