# Data Flow & Processing Pipeline

## 🔄 Overview

This diagram illustrates how data moves through the DFSA Rulebook RAG system in two main workflows: the Scraping Workflow and the RAG Query Workflow.

## 📊 Data Flow Diagram

```mermaid
flowchart TD
    %% User Interactions
    USER[👤 User] 
    
    %% Frontend
    DASHBOARD[📊 React Dashboard]
    QUERY_UI[🔍 Query Interface]
    JOB_UI[📋 Job Management]
    
    %% API Layer
    API[🌐 Express API Server]
    
    %% Main Workflows
    subgraph "🕷️ SCRAPING WORKFLOW"
        direction TB
        START_SCRAPE[Start Scraping Request<br/>POST /api/scraping/start]
        URL_DISCOVERY[🔍 URL Discovery<br/>- Sitemap parsing<br/>- Recursive crawling<br/>- Robots.txt compliance]
        TEST_SCRAPE[🧪 Test Scraping<br/>- Sample 5-10 URLs<br/>- Quality assessment<br/>- Manual confirmation]
        FULL_SCRAPE[🚀 Full Scraping<br/>- Brightdata proxy<br/>- Rate limiting<br/>- Error handling]
        CONTENT_PROC[⚙️ Content Processing<br/>- HTML cleaning<br/>- Text extraction<br/>- Metadata extraction]
        CHUNKING[📄 Document Chunking<br/>- Semantic chunking<br/>- Overlap management<br/>- Structure preservation]
        EMBEDDING[🧮 Embedding Generation<br/>- OpenAI text-embedding-3<br/>- Batch processing<br/>- Vector creation]
        VECTOR_STORE[💾 Vector Storage<br/>- Pinecone upsert<br/>- Metadata indexing<br/>- Search optimization]
    end
    
    subgraph "🤖 RAG QUERY WORKFLOW"
        direction TB
        QUERY_INPUT[Query Input<br/>POST /api/query]
        QUERY_PROC[🔍 Query Processing<br/>- Intent analysis<br/>- Query expansion<br/>- Context extraction]
        VECTOR_SEARCH[🎯 Vector Search<br/>- Semantic similarity<br/>- Top-K retrieval<br/>- Relevance scoring]
        CONTEXT_BUILD[📝 Context Assembly<br/>- Chunk ranking<br/>- Context building<br/>- Source tracking]
        LLM_GENERATION[🧠 LLM Generation<br/>- Prompt engineering<br/>- Response generation<br/>- Source citations]
        RESPONSE_FORMAT[📋 Response Formatting<br/>- Answer structuring<br/>- Source links<br/>- Confidence scores]
    end
    
    %% Data Stores
    subgraph "💾 DATA LAYER"
        SUPABASE[(🗄️ Supabase PostgreSQL<br/>- Scraped content<br/>- Job tracking<br/>- Quality metrics<br/>- System config)]
        PINECONE[(🔍 Pinecone Vector DB<br/>- Document embeddings<br/>- Semantic search<br/>- Metadata filtering)]
        WEAVIATE[(🕸️ Weaviate Vector DB<br/>- Fallback storage<br/>- Graph relationships<br/>- Hybrid search)]
    end
    
    %% External Services
    subgraph "🌐 EXTERNAL SERVICES"
        DFSA_SITE[🏛️ DFSA Website<br/>dfsaen.thomsonreuters.com]
        BRIGHTDATA[🌍 Brightdata<br/>Proxy service<br/>Browser automation]
        OPENAI_API[🤖 OpenAI API<br/>GPT-4 & Embeddings]
        ANTHROPIC_API[🧠 Anthropic API<br/>Claude models]
    end
    
    %% Real-time Communication
    WEBSOCKET[⚡ WebSocket<br/>Real-time updates]
    
    %% User Flow Connections
    USER --> DASHBOARD
    USER --> QUERY_UI
    USER --> JOB_UI
    
    DASHBOARD --> API
    QUERY_UI --> API
    JOB_UI --> API
    
    %% Scraping Workflow
    API --> START_SCRAPE
    START_SCRAPE --> URL_DISCOVERY
    URL_DISCOVERY --> DFSA_SITE
    URL_DISCOVERY --> TEST_SCRAPE
    TEST_SCRAPE --> BRIGHTDATA
    BRIGHTDATA --> DFSA_SITE
    TEST_SCRAPE --> FULL_SCRAPE
    FULL_SCRAPE --> BRIGHTDATA
    FULL_SCRAPE --> CONTENT_PROC
    CONTENT_PROC --> CHUNKING
    CHUNKING --> EMBEDDING
    EMBEDDING --> OPENAI_API
    EMBEDDING --> VECTOR_STORE
    VECTOR_STORE --> PINECONE
    VECTOR_STORE --> WEAVIATE
    
    %% RAG Query Workflow
    API --> QUERY_INPUT
    QUERY_INPUT --> QUERY_PROC
    QUERY_PROC --> VECTOR_SEARCH
    VECTOR_SEARCH --> PINECONE
    VECTOR_SEARCH --> CONTEXT_BUILD
    CONTEXT_BUILD --> LLM_GENERATION
    LLM_GENERATION --> OPENAI_API
    LLM_GENERATION --> ANTHROPIC_API
    LLM_GENERATION --> RESPONSE_FORMAT
    RESPONSE_FORMAT --> API
    
    %% Database Connections
    START_SCRAPE --> SUPABASE
    FULL_SCRAPE --> SUPABASE
    CONTENT_PROC --> SUPABASE
    CHUNKING --> SUPABASE
    QUERY_INPUT --> SUPABASE
    
    %% Real-time Updates
    API -.-> WEBSOCKET
    WEBSOCKET -.-> DASHBOARD
    FULL_SCRAPE -.-> WEBSOCKET
    CONTENT_PROC -.-> WEBSOCKET
    VECTOR_STORE -.-> WEBSOCKET
    
    %% Data Flow Labels
    START_SCRAPE -.->|"Job ID, Status"| DASHBOARD
    FULL_SCRAPE -.->|"Progress Updates"| DASHBOARD
    VECTOR_SEARCH -.->|"Search Results"| CONTEXT_BUILD
    RESPONSE_FORMAT -.->|"Final Answer"| QUERY_UI
    
    %% Styling
    classDef user fill:#e1f5fe,stroke:#0277bd,stroke-width:3px
    classDef frontend fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef api fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef scraping fill:#fce4ec,stroke:#ad1457,stroke-width:2px
    classDef rag fill:#f3e5f5,stroke:#6a1b9a,stroke-width:2px
    classDef data fill:#e0f2f1,stroke:#00695c,stroke-width:2px
    classDef external fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef realtime fill:#f1f8e9,stroke:#558b2f,stroke-width:2px
    
    class USER user
    class DASHBOARD,QUERY_UI,JOB_UI frontend
    class API api
    class START_SCRAPE,URL_DISCOVERY,TEST_SCRAPE,FULL_SCRAPE,CONTENT_PROC,CHUNKING,EMBEDDING,VECTOR_STORE scraping
    class QUERY_INPUT,QUERY_PROC,VECTOR_SEARCH,CONTEXT_BUILD,LLM_GENERATION,RESPONSE_FORMAT rag
    class SUPABASE,PINECONE,WEAVIATE data
    class DFSA_SITE,BRIGHTDATA,OPENAI_API,ANTHROPIC_API external
    class WEBSOCKET realtime
```

## 🕷️ Scraping Workflow Details

### **Phase 1: URL Discovery**
1. **Sitemap Parsing** - Extract URLs from XML sitemaps
2. **Recursive Crawling** - Follow links to discover additional pages
3. **Robots.txt Compliance** - Respect crawling permissions and delays
4. **URL Filtering** - Apply domain and content type filters

### **Phase 2: Quality Assurance**
1. **Test Scraping** - Sample 5-10 URLs for quality assessment
2. **Content Validation** - Check content quality and completeness
3. **Manual Confirmation** - Human review of test results
4. **Quality Gates** - Automated approval/rejection decisions

### **Phase 3: Full Scraping**
1. **Proxy Management** - Brightdata proxy rotation and session management
2. **Browser Automation** - Puppeteer-based content extraction
3. **Rate Limiting** - Respect server load and crawl delays
4. **Error Handling** - Retry logic and graceful failure handling

### **Phase 4: Content Processing**
1. **HTML Cleaning** - Remove scripts, styles, and irrelevant content
2. **Text Extraction** - Extract clean text and preserve structure
3. **Metadata Extraction** - Extract titles, headers, and document hierarchy
4. **Quality Validation** - Ensure content meets quality standards

### **Phase 5: Document Preparation**
1. **Semantic Chunking** - Split content into meaningful chunks
2. **Overlap Management** - Maintain context between chunks
3. **Structure Preservation** - Keep document relationships intact
4. **Metadata Attachment** - Add source and context information

### **Phase 6: Vectorization & Storage**
1. **Embedding Generation** - Create vector representations using OpenAI
2. **Batch Processing** - Optimize API calls for efficiency
3. **Vector Normalization** - Ensure consistent vector dimensions
4. **Database Storage** - Store in Pinecone with Weaviate fallback

## 🤖 RAG Query Workflow Details

### **Phase 1: Query Processing**
1. **Intent Analysis** - Understand user query intent and context
2. **Query Expansion** - Add synonyms and related terms
3. **Context Extraction** - Identify key entities and concepts
4. **Query Optimization** - Prepare for optimal vector search

### **Phase 2: Information Retrieval**
1. **Vector Search** - Find semantically similar content chunks
2. **Similarity Scoring** - Rank results by relevance
3. **Top-K Selection** - Select most relevant chunks
4. **Metadata Filtering** - Apply additional filters if needed

### **Phase 3: Context Assembly**
1. **Chunk Ranking** - Re-rank chunks for optimal context
2. **Context Building** - Assemble coherent context from chunks
3. **Source Tracking** - Maintain citation information
4. **Length Management** - Optimize context length for LLM

### **Phase 4: Response Generation**
1. **Prompt Engineering** - Craft optimal prompts with context
2. **LLM API Calls** - Generate responses using GPT-4 or Claude
3. **Response Parsing** - Extract and format the answer
4. **Citation Formatting** - Add proper source citations

### **Phase 5: Response Delivery**
1. **Answer Structuring** - Format response for presentation
2. **Source Linking** - Add clickable source references
3. **Confidence Scoring** - Provide confidence indicators
4. **Real-time Delivery** - Stream response to user interface

## ⚡ Real-time Features

### **WebSocket Communication**
- **Job Progress Updates** - Live scraping progress
- **System Status** - Real-time health monitoring
- **Error Notifications** - Immediate error alerts
- **Completion Alerts** - Job completion notifications

### **Dashboard Synchronization**
- **Multi-user Support** - Synchronized views across users
- **Live Metrics** - Real-time performance indicators
- **Status Broadcasting** - System-wide status updates
- **Interactive Updates** - Dynamic UI updates without refresh

## 📊 Data Flow Metrics

### **Scraping Pipeline Performance**
- **URL Discovery Rate** - URLs discovered per minute
- **Scraping Throughput** - Pages scraped per hour
- **Processing Speed** - Content processed per minute
- **Embedding Generation** - Vectors created per batch

### **RAG Query Performance**
- **Query Response Time** - End-to-end latency
- **Vector Search Speed** - Retrieval performance
- **LLM Generation Time** - Response generation latency
- **Overall Accuracy** - Answer quality metrics

### **System Health Indicators**
- **Error Rates** - Failure percentages by component
- **Resource Utilization** - CPU, memory, and network usage
- **API Rate Limits** - External service usage tracking
- **Database Performance** - Query execution times
