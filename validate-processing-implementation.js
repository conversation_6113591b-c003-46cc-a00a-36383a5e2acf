#!/usr/bin/env node

/**
 * Validation script for the content processing pipeline implementation
 * This script validates the code structure and basic functionality
 */

const fs = require('fs');
const path = require('path');

function validateFile(filePath, expectedElements) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const results = {
      file: filePath,
      exists: true,
      size: content.length,
      lines: content.split('\n').length,
      validations: {}
    };

    // Check for expected elements
    for (const [element, pattern] of Object.entries(expectedElements)) {
      const regex = new RegExp(pattern, 'g');
      const matches = content.match(regex) || [];
      results.validations[element] = {
        expected: true,
        found: matches.length,
        matches: matches.slice(0, 3) // Show first 3 matches
      };
    }

    return results;
  } catch (error) {
    return {
      file: filePath,
      exists: false,
      error: error.message
    };
  }
}

function validateProcessingPipeline() {
  console.log('🔍 Validating Content Processing Pipeline Implementation\n');

  const validations = [
    {
      file: 'src/processing/content-processor.ts',
      expectedElements: {
        'ContentProcessor class': 'class\\s+ContentProcessor',
        'processContent method': 'processContent\\s*\\(',
        'HTML cleaning': '(cheerio|jsdom|clean)',
        'Text normalization': '(normalize|trim|whitespace)',
        'Interface definitions': 'interface\\s+\\w+'
      }
    },
    {
      file: 'src/processing/metadata-extractor.ts',
      expectedElements: {
        'MetadataExtractor class': 'class\\s+MetadataExtractor',
        'extractMetadata method': 'extractMetadata\\s*\\(',
        'DFSA-specific extraction': '(dfsa|DFSA|rule|Rule)',
        'Hierarchy extraction': '(hierarchy|heading|section)',
        'Citation extraction': '(citation|reference|cite)'
      }
    },
    {
      file: 'src/processing/document-chunker.ts',
      expectedElements: {
        'DocumentChunker class': 'class\\s+DocumentChunker',
        'chunkDocument method': 'chunkDocument\\s*\\(',
        'Chunking strategies': '(strategy|chunk|split)',
        'Overlap handling': '(overlap|boundary)',
        'Size management': '(size|length|limit)'
      }
    },
    {
      file: 'src/processing/structure-preserver.ts',
      expectedElements: {
        'StructurePreserver class': 'class\\s+StructurePreserver',
        'preserveStructure method': 'preserveStructure\\s*\\(',
        'Relationship tracking': '(relationship|relation|link)',
        'Context preservation': '(context|preserve|maintain)',
        'Cross-references': '(reference|cross|cite)'
      }
    },
    {
      file: 'src/processing/processing-pipeline.ts',
      expectedElements: {
        'ProcessingPipeline class': 'class\\s+ProcessingPipeline',
        'processContent method': 'processContent\\s*\\(',
        'Pipeline orchestration': '(pipeline|orchestrat|workflow)',
        'Error handling': '(try|catch|error|Error)',
        'Quality checks': '(quality|validate|check)'
      }
    }
  ];

  let totalScore = 0;
  let maxScore = 0;

  for (const validation of validations) {
    console.log(`📄 Validating ${validation.file}`);
    const result = validateFile(validation.file, validation.expectedElements);
    
    if (!result.exists) {
      console.log(`   ❌ File not found: ${result.error}`);
      continue;
    }

    console.log(`   📊 File size: ${result.size} bytes, ${result.lines} lines`);
    
    let fileScore = 0;
    const fileMaxScore = Object.keys(validation.expectedElements).length;
    
    for (const [element, validation] of Object.entries(result.validations)) {
      if (validation.found > 0) {
        console.log(`   ✅ ${element}: ${validation.found} matches`);
        fileScore++;
      } else {
        console.log(`   ❌ ${element}: not found`);
      }
    }
    
    console.log(`   📈 Score: ${fileScore}/${fileMaxScore} (${Math.round(fileScore/fileMaxScore*100)}%)\n`);
    
    totalScore += fileScore;
    maxScore += fileMaxScore;
  }

  // Test basic functionality with mock data
  console.log('🧪 Testing Basic Functionality\n');
  
  try {
    // Simple HTML processing test
    const mockHtml = `
      <h1>DFSA Rulebook Chapter 1</h1>
      <p>This is a test paragraph with <strong>important</strong> content.</p>
      <ul>
        <li>Rule 1.1: First rule</li>
        <li>Rule 1.2: Second rule</li>
      </ul>
    `;
    
    console.log('✅ Mock HTML content created');
    console.log(`   Content length: ${mockHtml.length} characters`);
    console.log(`   Contains headings: ${mockHtml.includes('<h1>') ? 'Yes' : 'No'}`);
    console.log(`   Contains lists: ${mockHtml.includes('<ul>') ? 'Yes' : 'No'}`);
    console.log(`   Contains DFSA references: ${mockHtml.includes('DFSA') ? 'Yes' : 'No'}`);
    
  } catch (error) {
    console.log(`❌ Basic functionality test failed: ${error.message}`);
  }

  // Summary
  console.log('\n📊 Validation Summary');
  console.log(`Overall Score: ${totalScore}/${maxScore} (${Math.round(totalScore/maxScore*100)}%)`);
  
  if (totalScore/maxScore >= 0.8) {
    console.log('✅ Content Processing Pipeline implementation looks solid!');
    return true;
  } else if (totalScore/maxScore >= 0.6) {
    console.log('⚠️  Content Processing Pipeline implementation is partially complete');
    return false;
  } else {
    console.log('❌ Content Processing Pipeline implementation needs significant work');
    return false;
  }
}

// Run validation
if (require.main === module) {
  const success = validateProcessingPipeline();
  process.exit(success ? 0 : 1);
}

module.exports = { validateProcessingPipeline };