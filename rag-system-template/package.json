{"name": "rag-system-template", "version": "1.0.0", "description": "A template for building RAG (Retrieval-Augmented Generation) systems with web scraping capabilities", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:integration": "node run-integration-tests.js", "test:validation": "node run-integration-tests.js --validation-only", "validate:system": "node validate-complete-system.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:generate-types": "supabase gen types typescript --local > src/database/types.ts", "db:reset": "supabase db reset", "db:migrate": "supabase db push"}, "keywords": ["rag", "retrieval-augmented-generation", "web-scraping", "ai", "llm", "embeddings", "vector-database", "typescript", "supabase"], "author": "Your Name", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "axios": "^1.6.2", "bull": "^4.12.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "redis": "^4.6.12", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bull": "^4.10.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "supabase": "^1.123.4", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}