{"name": "dfsa-rulebook-rag", "version": "1.0.0", "description": "DFSA Rulebook scraping and RAG pipeline system", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:integration": "node run-integration-tests.js", "test:integration:quick": "node run-integration-tests.js --skip-optional", "test:validation": "node run-integration-tests.js --validation-only", "test:e2e": "node run-integration-tests.js --e2e-only", "test:load": "node run-integration-tests.js --load-only", "validate:system": "node validate-complete-system.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "frontend": "cd frontend && npm start"}, "keywords": ["scraping", "rag", "dfsa", "compliance", "ai"], "author": "", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@pinecone-database/pinecone": "^1.1.2", "@supabase/supabase-js": "^2.38.5", "@types/uuid": "^10.0.0", "axios": "^1.6.2", "bull": "^4.16.5", "cheerio": "^1.0.0-rc.12", "cohere-ai": "^7.7.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "gpt-tokenizer": "^3.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "openai": "^4.20.1", "pdf-parse": "^1.1.1", "puppeteer": "^21.6.1", "redis": "^4.6.11", "socket.io": "^4.7.4", "tesseract.js": "^5.0.4", "uuid": "^11.1.0", "weaviate-client": "^3.7.0", "winston": "^3.11.0"}, "devDependencies": {"@types/bull": "^4.10.4", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.4", "@types/pdf-parse": "^1.1.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "js-yaml": "^4.1.0", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}}