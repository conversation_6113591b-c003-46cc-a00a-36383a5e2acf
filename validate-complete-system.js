/**
 * Complete System Validation Script
 * 
 * Validates all system components, quality gates, and workflows
 * This ensures the system meets all requirements before deployment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SystemValidator {
  constructor() {
    this.validationResults = [];
    this.errors = [];
    this.warnings = [];
  }

  async validateCompleteSystem() {
    console.log('🔍 Starting Complete System Validation');
    console.log('=' .repeat(60));

    try {
      // 1. Validate Project Structure
      await this.validateProjectStructure();

      // 2. Validate Dependencies
      await this.validateDependencies();

      // 3. Validate Configuration
      await this.validateConfiguration();

      // 4. Validate Database Schema
      await this.validateDatabaseSchema();

      // 5. Validate API Endpoints
      await this.validateAPIEndpoints();

      // 6. Validate Component Integration
      await this.validateComponentIntegration();

      // 7. Validate Quality Gates
      await this.validateQualityGates();

      // 8. Validate Documentation
      await this.validateDocumentation();

      // 9. Validate Deployment Configuration
      await this.validateDeploymentConfiguration();

      // 10. Validate Security Configuration
      await this.validateSecurityConfiguration();

      // Print validation results
      this.printValidationResults();

    } catch (error) {
      console.error('❌ System validation failed:', error.message);
      process.exit(1);
    }
  }

  async validateProjectStructure() {
    console.log('\n📁 Validating Project Structure');

    const requiredDirectories = [
      'src',
      'src/api',
      'src/config',
      'src/database',
      'src/embeddings',
      'src/llm',
      'src/processing',
      'src/rag',
      'src/scraping',
      'src/socket',
      'src/types',
      'src/utils',
      'src/vector-database',
      'frontend',
      'docs',
      'scripts',
      'aws'
    ];

    const requiredFiles = [
      'package.json',
      'tsconfig.json',
      'Dockerfile',
      'docker-compose.yml',
      '.env.example',
      'README.md',
      'src/index.ts',
      'src/config/environment.ts'
    ];

    let structureValid = true;

    // Check directories
    for (const dir of requiredDirectories) {
      if (!fs.existsSync(dir)) {
        this.addError(`Missing required directory: ${dir}`);
        structureValid = false;
      }
    }

    // Check files
    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        this.addError(`Missing required file: ${file}`);
        structureValid = false;
      }
    }

    this.recordValidation('Project Structure', structureValid, 
      structureValid ? 'All required directories and files present' : 'Missing required files/directories');

    if (structureValid) {
      console.log('✅ Project structure validation passed');
    } else {
      console.log('❌ Project structure validation failed');
    }
  }

  async validateDependencies() {
    console.log('\n📦 Validating Dependencies');

    try {
      // Check if package.json exists and is valid
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      const requiredDependencies = [
        'express',
        'typescript',
        '@supabase/supabase-js',
        '@pinecone-database/pinecone',
        'bull',
        'redis',
        'winston',
        'joi',
        'puppeteer',
        'cheerio',
        'socket.io',
        'cors',
        'helmet'
      ];

      const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
      let depsValid = true;

      for (const dep of requiredDependencies) {
        if (!allDeps[dep]) {
          this.addError(`Missing required dependency: ${dep}`);
          depsValid = false;
        }
      }

      // Check if node_modules exists
      if (!fs.existsSync('node_modules')) {
        this.addWarning('node_modules directory not found - run npm install');
      }

      this.recordValidation('Dependencies', depsValid, 
        depsValid ? 'All required dependencies present' : 'Missing required dependencies');

      if (depsValid) {
        console.log('✅ Dependencies validation passed');
      } else {
        console.log('❌ Dependencies validation failed');
      }

    } catch (error) {
      this.addError(`Dependencies validation error: ${error.message}`);
      this.recordValidation('Dependencies', false, error.message);
      console.log('❌ Dependencies validation failed');
    }
  }

  async validateConfiguration() {
    console.log('\n⚙️  Validating Configuration');

    try {
      // Check environment files
      const envFiles = ['.env.example', '.env.development', '.env.production'];
      let configValid = true;

      for (const envFile of envFiles) {
        if (fs.existsSync(envFile)) {
          const content = fs.readFileSync(envFile, 'utf8');
          
          // Check for required environment variables
          const requiredVars = [
            'NODE_ENV',
            'PORT',
            'SUPABASE_URL',
            'SUPABASE_ANON_KEY',
            'OPENAI_API_KEY',
            'PINECONE_API_KEY',
            'REDIS_URL'
          ];

          for (const varName of requiredVars) {
            if (!content.includes(varName)) {
              this.addWarning(`${envFile} missing variable: ${varName}`);
            }
          }
        } else if (envFile !== '.env') {
          this.addError(`Missing environment file: ${envFile}`);
          configValid = false;
        }
      }

      // Validate TypeScript configuration
      if (fs.existsSync('tsconfig.json')) {
        const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
        if (!tsConfig.compilerOptions || !tsConfig.compilerOptions.strict) {
          this.addWarning('TypeScript strict mode not enabled');
        }
      }

      this.recordValidation('Configuration', configValid, 
        configValid ? 'Configuration files valid' : 'Configuration issues found');

      if (configValid) {
        console.log('✅ Configuration validation passed');
      } else {
        console.log('❌ Configuration validation failed');
      }

    } catch (error) {
      this.addError(`Configuration validation error: ${error.message}`);
      this.recordValidation('Configuration', false, error.message);
      console.log('❌ Configuration validation failed');
    }
  }

  async validateDatabaseSchema() {
    console.log('\n🗄️  Validating Database Schema');

    try {
      const supabaseDir = 'supabase';
      const migrationsDir = path.join(supabaseDir, 'migrations');
      
      let schemaValid = true;

      // Check if Supabase directory exists
      if (!fs.existsSync(supabaseDir)) {
        this.addError('Supabase directory not found');
        schemaValid = false;
      }

      // Check for migrations
      if (fs.existsSync(migrationsDir)) {
        const migrations = fs.readdirSync(migrationsDir);
        if (migrations.length === 0) {
          this.addWarning('No database migrations found');
        } else {
          console.log(`   Found ${migrations.length} database migrations`);
        }
      }

      // Check database types file
      const typesFile = 'src/database/types.ts';
      if (!fs.existsSync(typesFile)) {
        this.addError('Database types file not found');
        schemaValid = false;
      }

      this.recordValidation('Database Schema', schemaValid, 
        schemaValid ? 'Database schema configuration valid' : 'Database schema issues found');

      if (schemaValid) {
        console.log('✅ Database schema validation passed');
      } else {
        console.log('❌ Database schema validation failed');
      }

    } catch (error) {
      this.addError(`Database schema validation error: ${error.message}`);
      this.recordValidation('Database Schema', false, error.message);
      console.log('❌ Database schema validation failed');
    }
  }

  async validateAPIEndpoints() {
    console.log('\n🌐 Validating API Endpoints');

    try {
      const controllersDir = 'src/api/controllers';
      let apiValid = true;

      if (!fs.existsSync(controllersDir)) {
        this.addError('API controllers directory not found');
        apiValid = false;
      } else {
        const requiredControllers = [
          'scrape-controller.ts',
          'query-controller.ts',
          'config-controller.ts'
        ];

        for (const controller of requiredControllers) {
          const controllerPath = path.join(controllersDir, controller);
          if (!fs.existsSync(controllerPath)) {
            this.addError(`Missing controller: ${controller}`);
            apiValid = false;
          }
        }
      }

      // Check API router
      const apiRouterPath = 'src/api/index.ts';
      if (!fs.existsSync(apiRouterPath)) {
        this.addError('API router not found');
        apiValid = false;
      }

      this.recordValidation('API Endpoints', apiValid, 
        apiValid ? 'All API endpoints configured' : 'API endpoint issues found');

      if (apiValid) {
        console.log('✅ API endpoints validation passed');
      } else {
        console.log('❌ API endpoints validation failed');
      }

    } catch (error) {
      this.addError(`API endpoints validation error: ${error.message}`);
      this.recordValidation('API Endpoints', false, error.message);
      console.log('❌ API endpoints validation failed');
    }
  }

  async validateComponentIntegration() {
    console.log('\n🔗 Validating Component Integration');

    try {
      const components = [
        'src/scraping/scraping-orchestrator.ts',
        'src/processing/processing-pipeline.ts',
        'src/embeddings/embedding-service.ts',
        'src/vector-database/vector-store-service.ts',
        'src/llm/llm-service.ts',
        'src/rag/rag-orchestrator.ts'
      ];

      let integrationValid = true;

      for (const component of components) {
        if (!fs.existsSync(component)) {
          this.addError(`Missing component: ${component}`);
          integrationValid = false;
        }
      }

      // Check main entry point
      const mainFile = 'src/index.ts';
      if (fs.existsSync(mainFile)) {
        const content = fs.readFileSync(mainFile, 'utf8');
        
        // Check for key integrations
        const requiredImports = [
          'express',
          'socket.io',
          './config/environment',
          './api'
        ];

        for (const importName of requiredImports) {
          if (!content.includes(importName)) {
            this.addWarning(`Main file missing import: ${importName}`);
          }
        }
      }

      this.recordValidation('Component Integration', integrationValid, 
        integrationValid ? 'All components integrated' : 'Component integration issues found');

      if (integrationValid) {
        console.log('✅ Component integration validation passed');
      } else {
        console.log('❌ Component integration validation failed');
      }

    } catch (error) {
      this.addError(`Component integration validation error: ${error.message}`);
      this.recordValidation('Component Integration', false, error.message);
      console.log('❌ Component integration validation failed');
    }
  }

  async validateQualityGates() {
    console.log('\n🚪 Validating Quality Gates');

    try {
      const qualityComponents = [
        'src/scraping/content-validator.ts',
        'src/scraping/quality-assurance.ts',
        'src/scraping/manual-confirmation.ts'
      ];

      let qualityValid = true;

      for (const component of qualityComponents) {
        if (!fs.existsSync(component)) {
          this.addError(`Missing quality component: ${component}`);
          qualityValid = false;
        }
      }

      // Check for test files
      const testDir = 'src/test';
      if (fs.existsSync(testDir)) {
        const testFiles = fs.readdirSync(testDir, { recursive: true });
        const testCount = testFiles.filter(file => file.endsWith('.test.ts') || file.endsWith('.test.js')).length;
        
        if (testCount === 0) {
          this.addWarning('No test files found in test directory');
        } else {
          console.log(`   Found ${testCount} test files`);
        }
      }

      this.recordValidation('Quality Gates', qualityValid, 
        qualityValid ? 'Quality assurance components present' : 'Quality gate issues found');

      if (qualityValid) {
        console.log('✅ Quality gates validation passed');
      } else {
        console.log('❌ Quality gates validation failed');
      }

    } catch (error) {
      this.addError(`Quality gates validation error: ${error.message}`);
      this.recordValidation('Quality Gates', false, error.message);
      console.log('❌ Quality gates validation failed');
    }
  }

  async validateDocumentation() {
    console.log('\n📚 Validating Documentation');

    try {
      const requiredDocs = [
        'README.md',
        'docs/setup.md',
        'docs/api.md'
      ];

      let docsValid = true;

      for (const doc of requiredDocs) {
        if (!fs.existsSync(doc)) {
          this.addError(`Missing documentation: ${doc}`);
          docsValid = false;
        } else {
          const content = fs.readFileSync(doc, 'utf8');
          if (content.length < 100) {
            this.addWarning(`Documentation file ${doc} seems incomplete`);
          }
        }
      }

      // Check for component README files
      const componentDirs = [
        'src/scraping',
        'src/processing',
        'src/rag',
        'src/llm',
        'src/vector-database'
      ];

      for (const dir of componentDirs) {
        const readmePath = path.join(dir, 'README.md');
        if (fs.existsSync(readmePath)) {
          console.log(`   Found component documentation: ${readmePath}`);
        }
      }

      this.recordValidation('Documentation', docsValid, 
        docsValid ? 'Required documentation present' : 'Documentation issues found');

      if (docsValid) {
        console.log('✅ Documentation validation passed');
      } else {
        console.log('❌ Documentation validation failed');
      }

    } catch (error) {
      this.addError(`Documentation validation error: ${error.message}`);
      this.recordValidation('Documentation', false, error.message);
      console.log('❌ Documentation validation failed');
    }
  }

  async validateDeploymentConfiguration() {
    console.log('\n🚀 Validating Deployment Configuration');

    try {
      const deploymentFiles = [
        'Dockerfile',
        'docker-compose.yml',
        'docker-compose.prod.yml',
        '.github/workflows/ci-cd.yml',
        'railway.json',
        'render.yaml'
      ];

      let deploymentValid = true;

      for (const file of deploymentFiles) {
        if (!fs.existsSync(file)) {
          this.addWarning(`Missing deployment file: ${file}`);
        } else {
          console.log(`   Found deployment config: ${file}`);
        }
      }

      // Check AWS deployment
      const awsDir = 'aws';
      if (fs.existsSync(awsDir)) {
        const awsFiles = ['cloudformation.yaml', 'deploy.sh'];
        for (const file of awsFiles) {
          const filePath = path.join(awsDir, file);
          if (fs.existsSync(filePath)) {
            console.log(`   Found AWS config: ${filePath}`);
          }
        }
      }

      this.recordValidation('Deployment Configuration', deploymentValid, 
        'Deployment configurations available');

      console.log('✅ Deployment configuration validation passed');

    } catch (error) {
      this.addError(`Deployment configuration validation error: ${error.message}`);
      this.recordValidation('Deployment Configuration', false, error.message);
      console.log('❌ Deployment configuration validation failed');
    }
  }

  async validateSecurityConfiguration() {
    console.log('\n🔒 Validating Security Configuration');

    try {
      let securityValid = true;

      // Check for security middleware in main file
      const mainFile = 'src/index.ts';
      if (fs.existsSync(mainFile)) {
        const content = fs.readFileSync(mainFile, 'utf8');
        
        const securityFeatures = [
          'helmet',
          'cors',
          'express.json({ limit'
        ];

        for (const feature of securityFeatures) {
          if (!content.includes(feature)) {
            this.addWarning(`Security feature not found: ${feature}`);
          }
        }
      }

      // Check environment variable handling
      const configFile = 'src/config/environment.ts';
      if (fs.existsSync(configFile)) {
        const content = fs.readFileSync(configFile, 'utf8');
        
        if (!content.includes('joi') && !content.includes('Joi')) {
          this.addWarning('Environment validation not found');
        }
      }

      // Check for .env in .gitignore
      if (fs.existsSync('.gitignore')) {
        const gitignore = fs.readFileSync('.gitignore', 'utf8');
        if (!gitignore.includes('.env')) {
          this.addError('.env files not in .gitignore');
          securityValid = false;
        }
      }

      this.recordValidation('Security Configuration', securityValid, 
        securityValid ? 'Security configurations in place' : 'Security issues found');

      if (securityValid) {
        console.log('✅ Security configuration validation passed');
      } else {
        console.log('❌ Security configuration validation failed');
      }

    } catch (error) {
      this.addError(`Security configuration validation error: ${error.message}`);
      this.recordValidation('Security Configuration', false, error.message);
      console.log('❌ Security configuration validation failed');
    }
  }

  // Helper methods
  addError(message) {
    this.errors.push(message);
    console.log(`   ❌ ERROR: ${message}`);
  }

  addWarning(message) {
    this.warnings.push(message);
    console.log(`   ⚠️  WARNING: ${message}`);
  }

  recordValidation(name, passed, details) {
    this.validationResults.push({
      name,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
  }

  printValidationResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 SYSTEM VALIDATION RESULTS');
    console.log('='.repeat(60));

    const passed = this.validationResults.filter(v => v.passed).length;
    const total = this.validationResults.length;

    this.validationResults.forEach(validation => {
      const status = validation.passed ? '✅' : '❌';
      console.log(`${status} ${validation.name}: ${validation.details}`);
    });

    if (this.errors.length > 0) {
      console.log('\n🚨 ERRORS:');
      this.errors.forEach(error => console.log(`   • ${error}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.warnings.forEach(warning => console.log(`   • ${warning}`));
    }

    console.log('\n' + '-'.repeat(60));
    console.log(`📈 Summary: ${passed}/${total} validations passed`);
    console.log(`🚨 Errors: ${this.errors.length}`);
    console.log(`⚠️  Warnings: ${this.warnings.length}`);
    
    if (passed === total && this.errors.length === 0) {
      console.log('🎉 System validation passed! Ready for deployment.');
    } else {
      console.log('⚠️  System validation issues found. Please review before deployment.');
    }
    
    console.log('='.repeat(60));
  }
}

// Run the system validation
async function runSystemValidation() {
  const validator = new SystemValidator();
  await validator.validateCompleteSystem();
}

// Export for use in other files
module.exports = {
  SystemValidator,
  runSystemValidation
};

// Run validation if this file is executed directly
if (require.main === module) {
  runSystemValidation().catch(error => {
    console.error('System validation failed:', error);
    process.exit(1);
  });
}