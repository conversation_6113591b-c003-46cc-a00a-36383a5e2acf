/**
 * Complete System Integration Test
 * 
 * Tests the complete workflow from URL discovery through RAG querying
 * This validates all components working together as a complete system
 */

const axios = require('axios');
const { config } = require('./src/config/environment');
const { logger } = require('./src/utils/logger');

// Test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  testUrl: 'https://www.dfsa.ae/rulebook/general-module',
  timeout: 30000,
  maxRetries: 3
};

class IntegrationTestSuite {
  constructor() {
    this.testResults = [];
    this.jobId = null;
    this.conversationId = null;
  }

  async runAllTests() {
    console.log('🚀 Starting Complete System Integration Tests');
    console.log('=' .repeat(60));

    try {
      // Test 1: Health Check
      await this.testHealthCheck();

      // Test 2: Configuration Management
      await this.testConfigurationManagement();

      // Test 3: Start Scraping Job
      await this.testStartScrapingJob();

      // Test 4: Monitor Scraping Progress
      await this.testMonitorScrapingProgress();

      // Test 5: RAG Query Processing
      await this.testRAGQueryProcessing();

      // Test 6: Conversation Management
      await this.testConversationManagement();

      // Test 7: System Metrics
      await this.testSystemMetrics();

      // Test 8: Error Handling
      await this.testErrorHandling();

      // Print results
      this.printTestResults();

    } catch (error) {
      console.error('❌ Integration test suite failed:', error.message);
      process.exit(1);
    }
  }

  async testHealthCheck() {
    console.log('\n📋 Test 1: Health Check');
    
    try {
      const response = await axios.get(`${TEST_CONFIG.baseUrl}/health`, {
        timeout: TEST_CONFIG.timeout
      });

      this.assert(response.status === 200, 'Health check should return 200');
      this.assert(response.data.status === 'healthy', 'Health status should be healthy');
      this.assert(response.data.environment, 'Environment should be specified');

      this.recordTest('Health Check', true, 'System is healthy and responsive');
      console.log('✅ Health check passed');

    } catch (error) {
      this.recordTest('Health Check', false, error.message);
      throw new Error(`Health check failed: ${error.message}`);
    }
  }

  async testConfigurationManagement() {
    console.log('\n⚙️  Test 2: Configuration Management');

    try {
      // Get all configurations
      const configResponse = await axios.get(`${TEST_CONFIG.baseUrl}/api/config`);
      this.assert(configResponse.status === 200, 'Config endpoint should be accessible');
      this.assert(typeof configResponse.data === 'object', 'Config should return object');

      // Test setting a configuration
      const testConfig = {
        key: 'test.integration.value',
        value: 'integration-test-value'
      };

      const setResponse = await axios.post(`${TEST_CONFIG.baseUrl}/api/config`, testConfig);
      this.assert(setResponse.status === 200, 'Config setting should succeed');

      // Verify the configuration was set
      const getResponse = await axios.get(`${TEST_CONFIG.baseUrl}/api/config/${testConfig.key}`);
      this.assert(getResponse.data.value === testConfig.value, 'Config value should match');

      this.recordTest('Configuration Management', true, 'Config CRUD operations working');
      console.log('✅ Configuration management passed');

    } catch (error) {
      this.recordTest('Configuration Management', false, error.message);
      throw new Error(`Configuration test failed: ${error.message}`);
    }
  }

  async testStartScrapingJob() {
    console.log('\n🕷️  Test 3: Start Scraping Job');

    try {
      const scrapingConfig = {
        baseUrl: TEST_CONFIG.testUrl,
        mode: 'test', // Use test mode for integration testing
        maxDepth: 1,
        delay: 500,
        maxPages: 5
      };

      const response = await axios.post(
        `${TEST_CONFIG.baseUrl}/api/scraping/start`,
        scrapingConfig,
        { timeout: TEST_CONFIG.timeout }
      );

      this.assert(response.status === 200, 'Scraping job should start successfully');
      this.assert(response.data.jobId, 'Job ID should be returned');
      this.assert(response.data.status === 'started', 'Job status should be started');

      this.jobId = response.data.jobId;
      this.recordTest('Start Scraping Job', true, `Job ${this.jobId} started successfully`);
      console.log(`✅ Scraping job started: ${this.jobId}`);

    } catch (error) {
      this.recordTest('Start Scraping Job', false, error.message);
      throw new Error(`Scraping job start failed: ${error.message}`);
    }
  }

  async testMonitorScrapingProgress() {
    console.log('\n📊 Test 4: Monitor Scraping Progress');

    if (!this.jobId) {
      throw new Error('No job ID available for monitoring');
    }

    try {
      let attempts = 0;
      let jobCompleted = false;

      while (attempts < 10 && !jobCompleted) {
        const response = await axios.get(`${TEST_CONFIG.baseUrl}/api/scraping/job/${this.jobId}`);
        
        this.assert(response.status === 200, 'Job status endpoint should be accessible');
        this.assert(response.data.jobId === this.jobId, 'Job ID should match');

        const status = response.data.status;
        console.log(`   Job status: ${status} (attempt ${attempts + 1})`);

        if (status === 'completed' || status === 'failed') {
          jobCompleted = true;
          
          if (status === 'completed') {
            this.assert(response.data.results, 'Completed job should have results');
            this.assert(response.data.results.urlsProcessed > 0, 'Should have processed URLs');
          }
        }

        attempts++;
        if (!jobCompleted && attempts < 10) {
          await this.sleep(2000); // Wait 2 seconds between checks
        }
      }

      this.recordTest('Monitor Scraping Progress', jobCompleted, 
        jobCompleted ? 'Job monitoring successful' : 'Job monitoring timeout');
      
      if (jobCompleted) {
        console.log('✅ Scraping progress monitoring passed');
      } else {
        console.log('⚠️  Scraping job still running (timeout reached)');
      }

    } catch (error) {
      this.recordTest('Monitor Scraping Progress', false, error.message);
      throw new Error(`Scraping progress monitoring failed: ${error.message}`);
    }
  }

  async testRAGQueryProcessing() {
    console.log('\n🤖 Test 5: RAG Query Processing');

    try {
      const queryData = {
        query: 'What are the main regulations in the DFSA rulebook?',
        config: {
          retrieval: {
            maxResults: 5,
            minSimilarity: 0.7
          },
          generation: {
            maxTokens: 500,
            temperature: 0.3
          }
        }
      };

      const response = await axios.post(
        `${TEST_CONFIG.baseUrl}/api/query`,
        queryData,
        { timeout: TEST_CONFIG.timeout }
      );

      this.assert(response.status === 200, 'Query should process successfully');
      this.assert(response.data.status === 'success', 'Query status should be success');
      this.assert(response.data.response, 'Query should return response object');
      this.assert(response.data.response.answer, 'Response should contain answer');
      this.assert(response.data.response.query === queryData.query, 'Query should match input');

      // Store conversation ID for next test
      if (response.data.response.conversationId) {
        this.conversationId = response.data.response.conversationId;
      }

      this.recordTest('RAG Query Processing', true, 'Query processed successfully');
      console.log('✅ RAG query processing passed');

    } catch (error) {
      this.recordTest('RAG Query Processing', false, error.message);
      throw new Error(`RAG query processing failed: ${error.message}`);
    }
  }

  async testConversationManagement() {
    console.log('\n💬 Test 6: Conversation Management');

    try {
      // Create a new conversation
      const createResponse = await axios.post(`${TEST_CONFIG.baseUrl}/api/query/conversation`, {
        title: 'Integration Test Conversation'
      });

      this.assert(createResponse.status === 200, 'Conversation creation should succeed');
      this.assert(createResponse.data.conversationId, 'Should return conversation ID');

      const conversationId = createResponse.data.conversationId;

      // Send a query with conversation context
      const queryData = {
        query: 'Can you provide more details about compliance requirements?',
        conversationId: conversationId,
        config: {
          retrieval: { maxResults: 3 }
        }
      };

      const queryResponse = await axios.post(`${TEST_CONFIG.baseUrl}/api/query`, queryData);
      this.assert(queryResponse.status === 200, 'Contextual query should succeed');

      // Get conversation history
      const historyResponse = await axios.get(
        `${TEST_CONFIG.baseUrl}/api/query/conversation/${conversationId}`
      );

      this.assert(historyResponse.status === 200, 'Conversation history should be accessible');
      this.assert(historyResponse.data.messages, 'Should return message history');
      this.assert(historyResponse.data.messages.length > 0, 'Should have messages');

      this.recordTest('Conversation Management', true, 'Conversation features working');
      console.log('✅ Conversation management passed');

    } catch (error) {
      this.recordTest('Conversation Management', false, error.message);
      throw new Error(`Conversation management failed: ${error.message}`);
    }
  }

  async testSystemMetrics() {
    console.log('\n📈 Test 7: System Metrics');

    try {
      // Get scraping stats
      const scrapingStatsResponse = await axios.get(`${TEST_CONFIG.baseUrl}/api/scraping/stats`);
      this.assert(scrapingStatsResponse.status === 200, 'Scraping stats should be accessible');

      // Get query metrics
      const queryMetricsResponse = await axios.get(`${TEST_CONFIG.baseUrl}/api/query/metrics`);
      this.assert(queryMetricsResponse.status === 200, 'Query metrics should be accessible');

      this.recordTest('System Metrics', true, 'Metrics endpoints accessible');
      console.log('✅ System metrics passed');

    } catch (error) {
      this.recordTest('System Metrics', false, error.message);
      throw new Error(`System metrics failed: ${error.message}`);
    }
  }

  async testErrorHandling() {
    console.log('\n🚨 Test 8: Error Handling');

    try {
      // Test invalid endpoint
      try {
        await axios.get(`${TEST_CONFIG.baseUrl}/api/invalid-endpoint`);
        this.assert(false, 'Invalid endpoint should return 404');
      } catch (error) {
        this.assert(error.response.status === 404, 'Should return 404 for invalid endpoint');
      }

      // Test invalid query
      try {
        await axios.post(`${TEST_CONFIG.baseUrl}/api/query`, {
          // Missing required query field
          config: {}
        });
        this.assert(false, 'Invalid query should return 400');
      } catch (error) {
        this.assert(error.response.status === 400, 'Should return 400 for invalid query');
      }

      this.recordTest('Error Handling', true, 'Error handling working correctly');
      console.log('✅ Error handling passed');

    } catch (error) {
      this.recordTest('Error Handling', false, error.message);
      throw new Error(`Error handling test failed: ${error.message}`);
    }
  }

  // Helper methods
  assert(condition, message) {
    if (!condition) {
      throw new Error(`Assertion failed: ${message}`);
    }
  }

  recordTest(testName, passed, details) {
    this.testResults.push({
      name: testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printTestResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 INTEGRATION TEST RESULTS');
    console.log('='.repeat(60));

    const passed = this.testResults.filter(t => t.passed).length;
    const total = this.testResults.length;

    this.testResults.forEach(test => {
      const status = test.passed ? '✅' : '❌';
      console.log(`${status} ${test.name}: ${test.details}`);
    });

    console.log('\n' + '-'.repeat(60));
    console.log(`📈 Summary: ${passed}/${total} tests passed`);
    
    if (passed === total) {
      console.log('🎉 All integration tests passed! System is ready for deployment.');
    } else {
      console.log('⚠️  Some tests failed. Please review and fix issues before deployment.');
    }
    
    console.log('='.repeat(60));
  }
}

// Run the integration tests
async function runIntegrationTests() {
  const testSuite = new IntegrationTestSuite();
  await testSuite.runAllTests();
}

// Export for use in other test files
module.exports = {
  IntegrationTestSuite,
  runIntegrationTests,
  TEST_CONFIG
};

// Run tests if this file is executed directly
if (require.main === module) {
  runIntegrationTests().catch(error => {
    console.error('Integration tests failed:', error);
    process.exit(1);
  });
}