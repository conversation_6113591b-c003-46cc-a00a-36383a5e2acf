/**
 * RAG Orchestrator Test
 * 
 * Tests the RAG orchestrator functionality
 */

const { RAGOrchestrator } = require('./dist/rag/rag-orchestrator');
const { InMemoryConversationManager } = require('./dist/rag/conversation-manager');
const { BasicQueryProcessor } = require('./dist/rag/query-processor');
const { BasicContextAssembler } = require('./dist/rag/context-assembler');
const { LLMService } = require('./dist/llm/llm-service');
const { VectorStoreService } = require('./dist/vector-database/vector-store-service');
const { ResponseGenerator } = require('./dist/llm/response-generator');

// Mock vector store service
class MockVectorStoreService {
  async initialize() {
    console.log('Initializing mock vector store service');
    return true;
  }

  async search(query) {
    console.log(`Searching for: ${query.query}`);
    return {
      matches: [
        {
          id: 'chunk1',
          score: 0.92,
          metadata: {
            content: 'This is the first chunk of content about financial regulations.',
            title: 'Financial Regulations',
            source: 'DFSA Rulebook Section 1.2'
          }
        },
        {
          id: 'chunk2',
          score: 0.85,
          metadata: {
            content: 'This is the second chunk of content about compliance requirements.',
            title: 'Compliance Requirements',
            source: 'DFSA Rulebook Section 2.3'
          }
        },
        {
          id: 'chunk3',
          score: 0.78,
          metadata: {
            content: 'This is the third chunk of content about reporting obligations.',
            title: 'Reporting Obligations',
            source: 'DFSA Rulebook Section 3.1'
          }
        }
      ]
    };
  }
}

// Mock LLM service
class MockLLMService {
  async initialize() {
    console.log('Initializing mock LLM service');
    return true;
  }

  async generateResponse(prompt, options) {
    console.log(`Generating response for prompt: ${prompt.substring(0, 50)}...`);
    return {
      content: 'This is a mock response based on the provided context. According to the DFSA Rulebook, financial regulations require compliance with reporting obligations as mentioned in [1] and [2].',
      usage: {
        inputTokens: 500,
        outputTokens: 50,
        totalTokens: 550
      },
      model: 'mock-model',
      finishReason: 'stop',
      responseTime: 500
    };
  }

  getProvider() {
    return 'mock-provider';
  }
}

// Mock response generator
class MockResponseGenerator {
  constructor() {
    this.llmService = new MockLLMService();
  }

  async generateResponse(query, context, conversationHistory, config) {
    console.log(`Generating response for query: ${query}`);
    return {
      answer: 'This is a mock response based on the provided context. According to the DFSA Rulebook, financial regulations require compliance with reporting obligations.',
      sources: [
        {
          id: 'chunk1',
          title: 'Financial Regulations',
          excerpt: 'This is the first chunk of content about financial regulations.',
          relevance: 0.92,
          url: 'DFSA Rulebook Section 1.2'
        },
        {
          id: 'chunk2',
          title: 'Compliance Requirements',
          excerpt: 'This is the second chunk of content about compliance requirements.',
          relevance: 0.85,
          url: 'DFSA Rulebook Section 2.3'
        }
      ],
      context: {
        chunks: [],
        totalChunks: 3,
        maxSimilarity: 0.92,
        minSimilarity: 0.78,
        query
      },
      confidence: 0.85,
      provider: 'mock-provider',
      model: 'mock-model',
      responseTime: 500
    };
  }
}

// Override the ResponseGenerator with our mock
jest.mock('./dist/llm/response-generator', () => {
  return {
    ResponseGenerator: MockResponseGenerator
  };
});

// Test the RAG orchestrator
async function testRAGOrchestrator() {
  try {
    console.log('Testing RAG Orchestrator...');

    // Create mock services
    const vectorStore = new MockVectorStoreService();
    const llmService = new MockLLMService();
    const queryProcessor = new BasicQueryProcessor();
    const contextAssembler = new BasicContextAssembler();
    const conversationManager = new InMemoryConversationManager();

    // Create RAG orchestrator
    const orchestrator = new RAGOrchestrator({
      vectorStore,
      llmService,
      queryProcessor,
      contextAssembler,
      conversationManager,
      enableConversation: true
    });

    // Initialize orchestrator
    await orchestrator.initialize();
    console.log('Orchestrator initialized successfully');

    // Create conversation
    const conversationId = await orchestrator.createConversation();
    console.log(`Created conversation: ${conversationId}`);

    // Process query
    const query = 'What are the financial regulations in DFSA?';
    console.log(`Processing query: ${query}`);
    const response = await orchestrator.processQuery(query);

    // Validate response
    console.log('Response received:');
    console.log(`- Answer: ${response.answer.substring(0, 50)}...`);
    console.log(`- Sources: ${response.sources.length}`);
    console.log(`- Confidence: ${response.confidence}`);
    console.log(`- Provider: ${response.provider}`);
    console.log(`- Model: ${response.model}`);
    console.log(`- Response time: ${response.responseTime}ms`);

    // Get metrics
    const metrics = orchestrator.getMetrics();
    console.log('Metrics:');
    console.log(`- Total queries: ${metrics.totalQueries}`);
    console.log(`- Average response time: ${metrics.averageResponseTime}ms`);
    console.log(`- Average confidence: ${metrics.averageConfidence}`);
    console.log(`- Error rate: ${metrics.errorRate}`);

    // Get conversation
    const conversation = await orchestrator.getConversation(conversationId);
    console.log(`Conversation has ${conversation.messages.length} messages`);

    console.log('RAG Orchestrator test completed successfully');
    return true;
  } catch (error) {
    console.error('Error testing RAG orchestrator:', error);
    return false;
  }
}

// Run the test
testRAGOrchestrator()
  .then(success => {
    if (success) {
      console.log('All tests passed!');
      process.exit(0);
    } else {
      console.error('Tests failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Test execution error:', error);
    process.exit(1);
  });