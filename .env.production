# Application
NODE_ENV=production
PORT=3000
LOG_LEVEL=info

# Database - Supabase (production)
DATABASE_URL=postgresql://postgres:ZKRDD5Jq4?<EMAIL>:5432/postgres
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20

# Redis
REDIS_URL=redis://redis:6379

# AI Providers
# Use environment variables for actual keys
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
COHERE_API_KEY=

# Vector Database
# Use environment variables for actual keys
PINECONE_API_KEY=
PINECONE_ENVIRONMENT=
PINECONE_INDEX=dfsa-prod

# Web Scraping - Brightdata
BRIGHTDATA_HOST=brd.superproxy.io
BRIGHTDATA_PORT=9222
BRIGHTDATA_USERNAME=brd-customer-hl_2573e6f5-zone-scraping_browser3
BRIGHTDATA_PASSWORD=3kzbxparb7pe
BRIGHTDATA_BROWSER_URL=wss://brd-customer-hl_2573e6f5-zone-scraping_browser3:<EMAIL>:9222
BRIGHTDATA_SELENIUM_URL=https://brd-customer-hl_2573e6f5-zone-scraping_browser3:<EMAIL>:9515
SCRAPING_RATE_LIMIT=5
SCRAPING_CONCURRENT_JOBS=3

# Security
# Use environment variables for actual secret
JWT_SECRET=
CORS_ORIGINS=https://dfsa-rag.example.com

# Deployment
DOCKER_REGISTRY=
TAG=latest
DATA_PATH=/data