# Multi-stage Dockerfile for DFSA Rulebook RAG System

# ===== Stage 1: Base =====
FROM node:18-alpine AS base
WORKDIR /app
ENV NODE_ENV=production

# Install dependencies required for Puppeteer
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont \
    nodejs \
    yarn

# Set environment variables for Puppeteer
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# ===== Stage 2: Dependencies =====
FROM base AS dependencies
WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./

# Install production dependencies
RUN npm ci --only=production

# For the development dependencies that will be used in the build stage
FROM dependencies AS dev-dependencies
RUN npm ci

# ===== Stage 3: Build =====
FROM dev-dependencies AS build
WORKDIR /app

# Copy source files
COPY . .

# Build the application
RUN npm run build

# Build the frontend
WORKDIR /app/frontend
COPY frontend/package.json frontend/package-lock.json ./
RUN npm ci
COPY frontend/ ./
RUN npm run build

# ===== Stage 4: Development =====
FROM dev-dependencies AS development
WORKDIR /app

# Copy source files
COPY . .

# Expose ports for development
EXPOSE 3000 3001

# Set environment for development
ENV NODE_ENV=development

# Start development server
CMD ["npm", "run", "dev"]

# ===== Stage 5: Production =====
FROM base AS production
WORKDIR /app

# Copy production dependencies
COPY --from=dependencies /app/node_modules ./node_modules

# Copy built backend
COPY --from=build /app/dist ./dist
COPY --from=build /app/package.json ./

# Copy built frontend
COPY --from=build /app/frontend/build ./public

# Copy configuration files
COPY .env.production .env

# Create volume mount points
VOLUME ["/app/logs", "/app/data"]

# Expose ports
EXPOSE 3000

# Set user to non-root
USER node

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=30s --retries=3 \
  CMD wget -qO- http://localhost:3000/api/health || exit 1

# Start the application
CMD ["node", "dist/index.js"]