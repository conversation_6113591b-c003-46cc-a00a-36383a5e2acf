/**
 * DFSA RAG Orchestrator Test
 * 
 * Tests the DFSA-specific RAG orchestrator functionality
 */

const { RAGOrchestrator } = require('./dist/rag/rag-orchestrator');
const { DFSAConversationManager } = require('./dist/rag/conversation-manager');
const { DFSAQueryProcessor } = require('./dist/rag/query-processor');
const { DFSAContextAssembler } = require('./dist/rag/context-assembler');
const { LLMService } = require('./dist/llm/llm-service');
const { VectorStoreService } = require('./dist/vector-database/vector-store-service');
const { ResponseGenerator } = require('./dist/llm/response-generator');

// Mock vector store service with DFSA-specific content
class MockDFSAVectorStoreService {
  async initialize() {
    console.log('Initializing mock DFSA vector store service');
    return true;
  }

  async search(query) {
    console.log(`Searching for: ${query.query}`);
    return {
      matches: [
        {
          id: 'dfsa_rule_1',
          score: 0.92,
          metadata: {
            content: 'Financial institutions must comply with all applicable regulations as set forth in the DFSA Rulebook.',
            title: 'General Compliance Requirements',
            source: 'DFSA Rulebook Section 1.2',
            section: '1.2',
            documentType: 'rule'
          }
        },
        {
          id: 'dfsa_rule_2',
          score: 0.85,
          metadata: {
            content: 'All financial institutions must submit quarterly compliance reports detailing adherence to regulatory requirements.',
            title: 'Reporting Requirements',
            source: 'DFSA Rulebook Section 2.3',
            section: '2.3',
            documentType: 'rule'
          }
        },
        {
          id: 'dfsa_guidance_1',
          score: 0.78,
          metadata: {
            content: 'Guidance on reporting obligations: Institutions should maintain detailed records of all transactions for a minimum of 7 years.',
            title: 'Record Keeping Guidance',
            source: 'DFSA Rulebook Section 3.1',
            section: '3.1',
            documentType: 'guidance'
          }
        }
      ]
    };
  }
}

// Mock LLM service with DFSA-specific responses
class MockDFSALLMService {
  async initialize() {
    console.log('Initializing mock DFSA LLM service');
    return true;
  }

  async generateResponse(prompt, options) {
    console.log(`Generating DFSA-specific response for prompt: ${prompt.substring(0, 50)}...`);
    return {
      content: 'According to the DFSA Rulebook, financial institutions must comply with all applicable regulations as set forth in Section 1.2 [1]. Additionally, institutions must submit quarterly compliance reports [2] and maintain detailed records of all transactions for a minimum of 7 years [3].',
      usage: {
        inputTokens: 500,
        outputTokens: 50,
        totalTokens: 550
      },
      model: 'gpt-4o',
      finishReason: 'stop',
      responseTime: 500
    };
  }

  getProvider() {
    return 'openai';
  }
}

// Test the DFSA RAG orchestrator
async function testDFSARAGOrchestrator() {
  try {
    console.log('Testing DFSA RAG Orchestrator...');

    // Create mock services
    const vectorStore = new MockDFSAVectorStoreService();
    const llmService = new MockDFSALLMService();
    const queryProcessor = new DFSAQueryProcessor();
    const contextAssembler = new DFSAContextAssembler();
    const conversationManager = new DFSAConversationManager();

    // Create RAG orchestrator
    const orchestrator = new RAGOrchestrator({
      vectorStore,
      llmService,
      queryProcessor,
      contextAssembler,
      conversationManager,
      enableConversation: true,
      defaultConfig: {
        retrieval: {
          maxResults: 5,
          minSimilarity: 0.7,
          hybridSearch: true,
          rerankResults: true
        },
        generation: {
          maxTokens: 1000,
          temperature: 0.7,
          systemPrompt: 'You are a DFSA regulatory expert that answers questions based on the provided context from the DFSA Rulebook. ' +
            'If you don\'t know the answer or the information is not in the context, say so. ' +
            'Always cite your sources using [1], [2], etc. corresponding to the context sections.',
          includeContext: true,
          citeSources: true
        },
        conversation: {
          maxHistory: 5,
          contextWindow: 4000
        }
      }
    });

    // Initialize orchestrator
    await orchestrator.initialize();
    console.log('DFSA Orchestrator initialized successfully');

    // Create conversation
    const conversationId = await orchestrator.createConversation();
    console.log(`Created DFSA conversation: ${conversationId}`);

    // Process query
    const query = 'What are the reporting requirements in the DFSA Rulebook?';
    console.log(`Processing DFSA query: ${query}`);
    const response = await orchestrator.processQuery(query);

    // Validate response
    console.log('DFSA Response received:');
    console.log(`- Answer: ${response.answer.substring(0, 50)}...`);
    console.log(`- Sources: ${response.sources.length}`);
    console.log(`- Confidence: ${response.confidence}`);
    console.log(`- Provider: ${response.provider}`);
    console.log(`- Model: ${response.model}`);
    console.log(`- Response time: ${response.responseTime}ms`);

    // Get metrics
    const metrics = orchestrator.getMetrics();
    console.log('DFSA Metrics:');
    console.log(`- Total queries: ${metrics.totalQueries}`);
    console.log(`- Average response time: ${metrics.averageResponseTime}ms`);
    console.log(`- Average confidence: ${metrics.averageConfidence}`);
    console.log(`- Error rate: ${metrics.errorRate}`);

    // Process follow-up query
    const followUpQuery = 'How long should records be kept?';
    console.log(`Processing DFSA follow-up query: ${followUpQuery}`);
    const followUpResponse = await orchestrator.processQuery(followUpQuery, {
      conversationId
    });

    // Validate follow-up response
    console.log('DFSA Follow-up response received:');
    console.log(`- Answer: ${followUpResponse.answer.substring(0, 50)}...`);
    console.log(`- Sources: ${followUpResponse.sources.length}`);
    console.log(`- Confidence: ${followUpResponse.confidence}`);

    // Get conversation
    const conversation = await orchestrator.getConversation(conversationId);
    console.log(`DFSA Conversation has ${conversation.messages.length} messages`);

    console.log('DFSA RAG Orchestrator test completed successfully');
    return true;
  } catch (error) {
    console.error('Error testing DFSA RAG orchestrator:', error);
    return false;
  }
}

// Run the test
testDFSARAGOrchestrator()
  .then(success => {
    if (success) {
      console.log('All DFSA tests passed!');
      process.exit(0);
    } else {
      console.error('DFSA tests failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('DFSA test execution error:', error);
    process.exit(1);
  });